# coding:utf-8
import re
import os
import time
import socket
import threading

import logging
import traceback

try:
    from .Utils import *
    from .OperatingSystemEx import *
except:
    from Utils import *
    from OperatingSystemEx import *

if OperatingSystemEx.IsWindows:
    try:
        import win_inet_pton
    except:
        logging.warning(
            "Missing required library: win_inet_pton. Some functions about IPAddress may not working."
        )


class Network:
    RegexByteDecimal = r"(2[0-4]\d|25[0-5]|[01]?\d\d?)"
    RegexIP = re.compile(r"(%s\.){3}%s" % (RegexByteDecimal, RegexByteDecimal))
    RegexIPOnly = re.compile(r"^(%s\.){3}%s$" % (RegexByteDecimal, RegexByteDecimal))
    RegexByteDecimalBinary = rb"(2[0-4]\d|25[0-5]|[01]?\d\d?)"
    RegexIPBinary = re.compile(
        rb"(%s\.){3}%s" % (RegexByteDecimalBinary, RegexByteDecimalBinary)
    )
    SPCEIAL_IP_ADDRESSES = set(
        [
            0,
            0x7F000000,
            0x7F000001,
            0xA9FE0000,
            0xA9FEFFFF,
            0xAC100000,
            0xAC1FFFFF,
            0xC0A80000,
            0xC0A8FFFF,
            0xC0000000,
            0xC000FFFF,
            0xC6336400,
            0xC63364FF,
            0xCB007100,
            0xCB0071FF,
            0xF0000000,
            0xFFFFFFFF,
        ]
    )

    RegexByteDecimalStrict1 = r"(25[0-5]|2[0-4]\d|1\d\d|[1-9]\d?|0)"
    RegexByteDecimalStrict2 = r"(25[0-5]|2[0-4]\d|1\d\d|[1-9]\d)"
    # 第一字节至少两位（10-255），后三字节不能全是一位（0-9）
    RegexIPStrict = re.compile(
        r"(?<!\d)%s\.(%s(\.%s){2}|%s\.%s\.%s|(%s\.){2}%s)"
        % (
            RegexByteDecimalStrict2,
            RegexByteDecimalStrict2,
            RegexByteDecimalStrict1,
            RegexByteDecimalStrict1,
            RegexByteDecimalStrict2,
            RegexByteDecimalStrict1,
            RegexByteDecimalStrict1,
            RegexByteDecimalStrict2,
        )
    )
    # .do、.py、.sh容易误识别，去掉了
    RegexDomainTop = re.compile(
        "(aa|ad|ae(ro)?|af|ag|ai|al|am|an|ao|ar(ts)?|as(ia)?|at|au|aw|az|ba|bb|bd|be|bf|bg|bh|biz?|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|ch|ci|ck|cl|club|cm|cn|com?|cq|cr|cu|cv|cx|cy|cz|de|dj|dk|dm|dz|ec|edu|ee|eg|eh|er|es|et|ev|fi(rm)?|fj|fk|fm|fo|fr|ga|gd|ge|gf|gg|gh|gi|gl|gm|gn|gov|gp|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|info|int|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mil|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|net|nf|ng|ni|nl|nom?|np|nr|nt|nu|nz|om|org|pa|pe|pf|pg|ph|pk|pl|pm|pn|post|pr|pro|pt|pw|qa|re|rec|red|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|si|sj|sk|sl|sm|sn|so|sr|st(ore)?|su|sv|sx|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|top?|tr|tt|tv|tw|tz|ua|ug|uk|um|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw)"
    )
    RegexDomainTopStrict = re.compile(
        "(aero|arts|asia|biz|cc|club|cn|com|edu|firm|gov|hk|im|info|io|jp|me|my|net|nom|org|post|pro|rec|red|store|top|tw|uk|us|web|de|fr|gb|in|kp|kr|mo|au)"
    )
    RegexDomain = re.compile(
        """((?<!%)(?<!%[42])(?<![\\w\u0080-\uFFFF])|(?<=%)(?!(40|2[fF]))|(?<=%4)(?!0)|(?<=%40)|(?<=%2)(?![fF])|(?<=%2[fF]))([a-zA-Z0-9'\u4e00-\u9fa5][a-zA-Z0-9'\u4e00-\u9fa5\-]*\\.)+"""
        + RegexDomainTop.pattern
        + """(?![\\w\u00FF-\uFFFF\\.])"""
    )
    RegexDomainStrict = re.compile(
        """((?<!%)(?<!%[42])(?<![\\w\u0080-\uFFFF])|(?<=%)(?!(40|2[fF]))|(?<=%4)(?!0)|(?<=%40)|(?<=%2)(?![fF])|(?<=%2[fF]))([a-zA-Z0-9'\u4e00-\u9fa5][a-zA-Z0-9'\u4e00-\u9fa5\-]*\\.)+"""
        + RegexDomainTop.pattern
        + """(?![\\w\u00FF-\uFFFF\\.])"""
    )
    # 因为是bytes正则，所以姑且忽略非ASCII的域名吧
    RegexDomainTopBinary = EncodingEx.GetBinary(RegexDomainTop)
    RegexDomainBinary = re.compile(
        b"""((?<!%)(?<!%[42])(?<![\\w\x80-\xFF])|(?<=%)(?!(40|2[fF]))|(?<=%4)(?!0)|(?<=%40)|(?<=%2)(?![fF])|(?<=%2[fF]))([a-zA-Z0-9'][a-zA-Z0-9'\-]*\\.)+"""
        + RegexDomainTopBinary.pattern
        + b"""(?![\\w\xFF\\.])"""
    )

    RegexContentLength = re.compile(b"\r\nContent-Length: (\d+)\r\n")

    @staticmethod
    def IsAvailable(address):
        return not address in {
            b"\x00\x00\x00\x00",
            b"\x7F\x00\x00\x01",
            b"\xFF\xFF\xFF\xFF",
        }  # 任意地址、回环地址、广播地址

    @staticmethod
    def IsInternet(address):
        if not Network.IsAvailable(address):
            return False
        if address >= b"\x0A\x00\x00\x00" and address <= b"\x0A\xFF\xFF\xFF":
            return False
        if address >= b"\xAC\x10\x00\x00" and address <= b"\xAC\x1F\xFF\xFF":
            return False
        if address >= b"\xC0\xA8\x00\x00" and address <= b"\xC0\xA8\xFF\xFF":
            return False
        return True

    @staticmethod
    def GetHostFromURL(url):
        start = url.find("//")
        if start < 0:
            start = None
        else:
            start += 2
        end = url.find(":", start)
        if end < 0:
            end = url.find("/", start)
        if end < 0:
            end = None
        return url[start:end]

    @staticmethod
    def ParseIPAddress(ip_str):
        if ip_str is None:
            return None
        return socket.inet_pton(
            socket.AF_INET6 if ":" in ip_str else socket.AF_INET, ip_str
        )

    @staticmethod
    def ToIPString(ip_bin):
        if ip_bin is None:
            return None
        return socket.inet_ntop(
            socket.AF_INET6 if len(ip_bin) > 4 else socket.AF_INET, ip_bin
        )

    @staticmethod
    def StandardlizeIPString(ip_str):
        return Network.ToIPString(Network.ParseIPAddress(ip_str))

    if Utils.IsPython3:  # 哎，就为了这个破ord，还得把函数写两遍...
        # 从一个以太网报文中提取出应用层部分。不能识别时返回原始内容。
        @staticmethod
        def GetApplicationData(ethernet):
            if not ethernet:
                return ethernet
            # 链路层
            # 以太网头部包括6字节目的MAC，6字节源MAC，2字节网络层协议（一般都是0x08 0x00的IP）
            # 但不知道为什么，有很多以太网首部是18字节的，好像源MAC和目的MAC各有8字节一样...
            if len(ethernet) > 14 and ethernet[12] == 8 and ethernet[13] == 0:
                # 固定记14字节
                offset = 14
            elif len(ethernet) > 18 and ethernet[16] == 8 and ethernet[17] == 0:
                # 固定记18字节
                offset = 18
            else:
                return ethernet

            # 网络层
            # IP头部长度位于第一个字节的低4位，乘以4即为IP头部字节数；第10个字节为传输层协议（ICMP->0x01、UDP->0x04、TCP->0x06）
            protocol = ethernet[offset + 9]
            offset += (ethernet[offset] & 0x0F) * 4

            # 传输层
            if protocol == 1:  # ICMP，首部长度固定16
                offset += 16
            elif protocol == 17:  # UDP，首部长度固定8
                offset += 8
            elif protocol == 6:  # TCP，首部长度为第13个字节的高4位，乘以4即为TCP头部字节数
                offset += (ethernet[offset + 12] >> 4) * 4
            else:  # 不知道什么鬼传输层协议，返回整个传输层吧
                pass
            return ethernet[offset:]

    else:
        # 从一个以太网报文中提取出应用层部分。不能识别时返回原始内容。
        @staticmethod
        def GetApplicationData(ethernet):
            if not ethernet:
                return ethernet
            # 链路层
            # 以太网头部包括6字节目的MAC，6字节源MAC，2字节网络层协议（一般都是0x08 0x00的IP）
            # 但不知道为什么，有很多以太网首部是18字节的，好像源MAC和目的MAC各有8字节一样...
            if (
                len(ethernet) > 14
                and ethernet[12] == b"\x08"
                and ethernet[13] == b"\x00"
            ):
                # 固定记14字节
                offset = 14
            elif (
                len(ethernet) > 18
                and ethernet[16] == b"\x08"
                and ethernet[17] == b"\x00"
            ):
                # 固定记18字节
                offset = 18
            else:
                return ethernet

            # 网络层
            # IP头部长度位于第一个字节的低4位，乘以4即为IP头部字节数；第10个字节为传输层协议（ICMP->0x01、UDP->0x04、TCP->0x06）
            protocol = ord(ethernet[offset + 9])
            offset += (ord(ethernet[offset]) & 0x0F) * 4

            # 传输层
            if protocol == 1:  # ICMP，首部长度固定16
                offset += 16
            elif protocol == 17:  # UDP，首部长度固定8
                offset += 8
            elif protocol == 6:  # TCP，首部长度为第13个字节的高4位，乘以4即为TCP头部字节数
                offset += (ord(ethernet[offset + 12]) >> 4) * 4
            else:  # 不知道什么鬼传输层协议，返回整个传输层吧
                pass
            return ethernet[offset:]

    @staticmethod
    def ReadHexNumber(data, offset):
        result = 0
        while offset < len(data):
            if data[offset] in b"0123456789":
                half_byte = data[offset] - ord(b"0")
            elif data[offset] in b"abcdef":
                half_byte = data[offset] - ord(b"a") + 10
            elif data[offset] in b"ABCDEF":
                half_byte = data[offset] - ord(b"A") + 10
            else:
                break
            result <<= 4
            result |= half_byte
            offset += 1
        return result, offset

    @staticmethod
    def DecodeChunked(data):
        try:
            if (
                not data.startswith(b"HTTP/")
                or not b"\r\nTransfer-Encoding: chunked\r\n" in data
            ):
                return data
            # 找到响应正文起始位置
            result = []
            begin_pos = data.find(b"\r\n\r\n")
            if begin_pos == -1:
                return data
            begin_pos += 4
            pos = begin_pos
            content_length = None
            # 开始解析chunked
            while pos < len(data):
                # 读取chunked长度
                chunked_length, offset = Network.ReadHexNumber(data, pos)
                if offset <= pos:
                    break
                if chunked_length == 0:
                    # 成功读取了整个chunked，恢复响应正文长度
                    content_length = sum(len(x) for x in result)
                    break
                if offset + 2 >= len(data):
                    break
                assert data[offset : offset + 2] == b"\r\n"
                pos = offset + 2
                # 读取chunked数据
                if pos + chunked_length < len(data):
                    result.append(data[pos : pos + chunked_length])
                else:
                    result.append(data[pos:])
                    break
                pos += chunked_length
                if pos + 2 >= len(data):
                    break
                assert data[pos : pos + 2] == b"\r\n"
                pos += 2
            return data[:begin_pos].replace(
                b"\r\nTransfer-Encoding: chunked\r\n",
                (b"Content-Length: " + str(content_length).encode() + b"\r\n")
                if content_length
                else b"\r\n",
            ) + b"".join(result)
        except Exception:
            # 任何错误都返回原始数据
            return data

    @staticmethod
    def DecodeGZip(data):
        try:
            if (
                not data.startswith(b"HTTP/")
                or not b"\r\nContent-Encoding: gzip\r\n" in data
            ):
                return data
            # 找到响应正文起始位置
            begin_pos = data.find(b"\r\n\r\n")
            if begin_pos == -1:
                return data
            begin_pos += 4
            # 开始解压gzip
            # 注意，这里的decompressed_data是解压后的数据，但decompressed_length是解压前的长度
            decompressed_data, decompressed_length = EncodingEx.GZipDecompressEx(
                data[begin_pos:]
            )
            if decompressed_length == 0:
                return data
            pos = begin_pos + decompressed_length
            headers = data[:begin_pos].replace(
                b"\r\nContent-Encoding: gzip\r\n", b"\r\n"
            )
            # 如果Content-Length存在，且等于len(data)-begin_pos，则修正为解压后的长度
            content_length_match = Network.RegexContentLength.search(data)
            if content_length_match:
                content_length = int(content_length_match.group(1))
                if content_length == len(data) - begin_pos:
                    headers = (
                        headers[: content_length_match.start(1)]
                        + str(
                            len(decompressed_data)
                            + len(headers)
                            - begin_pos
                            - decompressed_length
                        ).encode()
                        + headers[content_length_match.end(1) :]
                    )
            return (
                headers + decompressed_data + (data[pos:] if pos < len(data) else b"")
            )
        except Exception:
            # 任何错误都返回原始数据
            return data

    @staticmethod
    def PreprocessPacket(data, is_response=False):
        data = Network.GetApplicationData(data)
        return Network.DecodeGZip(Network.DecodeChunked(data)) if is_response else data


class TCPServer:
    def __init__(self, port, address="0.0.0.0", backlog=5):
        self.Clients = []
        self.ClientLock = threading.Lock()
        self.Listener = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.Listener.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.Listener.bind((address, port))
        self.Listener.listen(backlog)

    def ListenProc(self):
        try:
            while True:
                client, _ = self.Listener.accept()
                with self.ClientLock:
                    self.Clients.append(client)
                thread = threading.Thread(target=self.AcceptProc, args=(client,))
                thread.daemon = True
                thread.start()
        except:
            logging.error("Error in ListenProc: %s" % traceback.format_exc())

    def AcceptProc(self, client):
        try:
            self.ClientProc(client)
        except:
            logging.error("Error in ClientProc: %s" % traceback.format_exc())
        finally:
            with self.ClientLock:
                client.close()
                self.Clients.remove(client)

    def ClientProc(self, client):
        raise NotImplementedError()

    def Close(self):
        if self.Listener:
            self.Listener.close()
        with self.ClientLock:
            for i in self.Clients:
                i.close()
            # del self.Clients[:]
