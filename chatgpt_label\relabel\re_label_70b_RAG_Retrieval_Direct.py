# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





prompt = """Answer the question based on the reference information. 
Please answer step by step and provide the reasoning process for the answer. 
Please provide the answer in markdown format.

Reference Information: 
{Answer}

Question: 
{Question}
"""


use_prompt = """Answer the question based on the reference information. 

Reference Information: 
{Answer}

Question: 
{Question}
"""



async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.2:50002/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model":"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            # "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
data = []

data0 = load_dataset("json", data_files="/home/<USER>/llm_dataset/ConcurrentQA-Retrieval/Retriever_CQA_train_all_original.json")
data0 = data0["train"]

def combine_paras(point):
    paras_list = []
    paras_len = 0
    for i in point["pos_paras"]:
        line = "title: " + i["title"] + "\ntext: " + i["text"]
        paras_list.append(line)
        paras_len += len(line)

    for i in point["neg_paras"]:
        line = "title: " + i["title"] + "\ntext: " + i["text"]
        if paras_len + len(line) > 4000:
            break
        paras_list.append(line)
        paras_len += len(line)

    _ = random.shuffle(paras_list)
    
    refer_info = "\n\n".join(paras_list)
    question = point["question"]

    
    out = {"prompt": prompt.replace("{Question}", question).replace("{Answer}", refer_info),
           "use_prompt": use_prompt.replace("{Question}", question).replace("{Answer}", refer_info)}
    return out


data0 = data0.map(lambda x: combine_paras(x), num_proc=35)
for point in tqdm(data0):
    data.append(point)
    

with open("/home/<USER>/llm_dataset/MultiHiertt_retrieval/train.json", "r", encoding="utf-8") as file:
    data1 = json.load(file)

def combine_table(point):
    refer_info = "\n\n".join(point["paragraphs"])
    for i, t in enumerate(point["tables"]):
        refer_info = refer_info.replace("## Table %s ##" % i, t)
    
    question = point["qa"]["question"]
    out = {"prompt": prompt.replace("{Question}", question).replace("{Answer}", refer_info),
           "use_prompt": use_prompt.replace("{Question}", question).replace("{Answer}", refer_info)}
    return out

for point in data1:
    out = combine_table(point)
    point["prompt"] = out["prompt"]
    point["use_prompt"] = out["use_prompt"]
    data.append(point)


with open("/home/<USER>/llm_dataset/SQA/train.json", "r", encoding="utf-8") as file:
    data2 = json.load(file)
for point in data2:
    point["prompt"] = prompt.replace("{Question}", point["question"]).replace("{Answer}", point["struct_in"])
    point["use_prompt"] = use_prompt.replace("{Question}", point["question"]).replace("{Answer}", point["struct_in"])
    data.append(point)


with open("/home/<USER>/llm_dataset/WTQ/train.json", "r", encoding="utf-8") as file:
    data3 = json.load(file)
for point in data3:
    point["prompt"] = prompt.replace("{Question}", point["question"]).replace("{Answer}", str(point["table"]))
    point["use_prompt"] = use_prompt.replace("{Question}", point["question"]).replace("{Answer}", str(point["table"]))
    data.append(point)
    

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/RAG_Retrieval_Direct_relabel.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
