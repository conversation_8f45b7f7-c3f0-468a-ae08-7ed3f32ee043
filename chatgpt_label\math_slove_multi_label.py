# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd


def openai_query(messages, model="gpt-3.5-turbo-16k", temperature: float = 0, top_p: float = 1,
                       presence_penalty: float = 0.0, frequency_penalty: float = 0.0):
    # url = "http://gpt1.attackgraph.com:16180/openai"
    url = "http://10.24.45.223:%s/v1/chat/completions" % random.choice([11037, 11038])
    body = {
        "model": model,
        # "temperature": temperature,
        # "top_p": top_p,
        "messages": messages,
        # "presence_penalty": presence_penalty,
        # "frequency_penalty": frequency_penalty
    }

    try:
        # print("openai chatgpt query!")
        response = requests.post(url, json=body)
        # print("openai chatgpt response status: %s - text: %s" % (response.status_code, response.text))
        if response.status_code == 200:
            res = response.json()
            return res
        else:
            print("openai chatgpt api-- Error: %s" % response.json())
            return None
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        return None

prompt = """解决给出的数学题。首先分析解题思路（Thought），然后一步步详细写出具体的解题步骤（Steps），最后给出答案（Answer）。
解答以json的格式给出，如下：
{
    "Thought": 分析解题思路,
    "Steps": 一步步详细写出具体的解题步骤
    "Answer": 最后的答案，答案只要最后的数值，不要带上单位或其他的任何描述
}

这是一个示例：
问题：小王要将150千克含药量20%的农药稀释成含药量5%的药水．需要加水多少千克？
解答：
{example_answer}

问题：{question}
解答："""

example_ans = {
    "Thought": "此处先通过150*20%计算得到不含水的农药重量。然后使用不含水分的农药重量除以稀释后的含药量5%，得到稀释后的药水总重量。最后减去最初的药水重量，就可以得到需要加的水的重量。",
    "Steps": "1.首先，我们需要计算出不含水的农药重量。小王最初有的农药重150千克，含药量是20%。150*20%=30。即有30千克是不含水的农药的重量，有150-30=120千克是水。\n2.然后，我们需要计算出稀释后的药水总重量。有30千克是不含水的农药的重量，稀释后含药量降为5%。30/5%=600千克。即稀释后的药水重量为600千克。\n3.最后，使用稀释后的药水的重量减去原始的药水重量，我们可以得到需要增加的水的重量：600-150=450千克。\n4.所以，小王需要增加的水的重量是：150*20%/5%-150=450千克。",
    "Answer": "450"
}
example_ans_str = json.dumps(example_ans,  ensure_ascii=False)

prompt_replace = prompt.replace("{example_answer}", example_ans_str)

def chatgpt_math_solve_step(point):
    messages = [
        {"role": "user",
         "content": prompt_replace.replace("{question}", point['question_chinese'])}
    ]

    try:
        response = openai_query(messages)
        answer = response["choices"][0]["message"]['content']
        # json_answer = json.loads(answer)
        return answer
    except Exception as ex:
        print("chatgpt math solve step error: %s" % str(ex))
        return ""


path = r"D:\work\GPT\chatgpt_label/ape210k-train.jsonl"
math_question = []
with open(path, 'r', encoding='utf-8') as f:
    for line in f:
        line = json.loads(line.strip())
        if len(line['chain'].split('\n')) > 1:
            math_question.append(line)

print("data size: %s" %  len(math_question))
print("data example: ", math_question[0])

all_data = []
for row in tqdm(math_question):
    for i in range(10):
        new_point = copy.deepcopy(row)
        answer = chatgpt_math_solve_step(new_point)
        new_point['step_answer'] = answer
        try:
            json_answer = json.loads(answer)
            new_point['json_step_answer'] = json_answer
        except:
            pass
        all_data.append(new_point)


with open(r"D:\work\GPT\chatgpt_label/ape210k_step_labeled.json", 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

print("Done")
