import random
data = [
    {
        "en": "Hello!",
        "zh": "Bonjour !"
    },
    {
        "en": "How are you?",
        "zh": "Comment ça va ?"
    },
    {
        "en": "What is your name?",
        "zh": "Comment tu t'appelles ?"
    }
]


def Prompt1(point):
    if point['en'] is None or point['zh'] is None:
        return None

    if (len(point['en']) <= 0) or (len(point['zh']) <= 0):
        return None

    question_list = [
        "请把这段话翻译为中文：%s",
        "%s\n请把这段话翻译为中文",
        "%s\n翻译为中文",
        "翻译为中文：%s",
        "%s\n翻译：",
        "翻译：%s",
    ]
    question = random.choice(question_list) % point["en"]
    answer = point["zh"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


def Prompt2(point):
    if point['en'] is None or point['zh'] is None:
        return None

    if (len(point['en']) <= 0) or (len(point['zh']) <= 0):
        return None

    question_list = [
        "请把这段话翻译为英文：%s",
        "%s\n请把这段话翻译为英文",
        "%s\n翻译为英文",
        "翻译为英文：%s",
    ]
    question = random.choice(question_list) % point["zh"]
    answer = point["en"]

    prompt = {"question": question,
              "answer": answer}
    return prompt

data_path = "/home/<USER>/llm_dataset/wmt19/neu2017"

with open(data_path + '/NEU_cn.txt', 'r', encoding='utf-8') as f:
    cn_data = f.readlines()
    cn_data = [x.strip() for x in cn_data]

with open(data_path + '/NEU_en.txt', 'r', encoding='utf-8') as f:
    en_data = f.readlines()
    en_data = [x.strip() for x in en_data]

all_data = []
prompt_list = [Prompt1, Prompt2]

new_point = []
point_length = 0

for cn_line, en_line in zip(cn_data, en_data):
    point = {
        "en": en_line,
        "zh": cn_line,
    }
    prompt_func = random.choice(prompt_list)
    prompt = prompt_func(point)
    if prompt is not None:
        if point_length + len(prompt['question']) + len(prompt['answer']) > 1800:
            all_data.append(new_point)
            point_length = 0
            new_point = []

        new_point += [{"from": "human", "value": prompt['question']},
                      {"from": "assistant", "value": prompt['answer']}]
        point_length += len(prompt['question']) + len(prompt['answer'])

all_data = [{"conversation": x} for x in all_data]  # 141390
