# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random
import os
import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm

from typing import Type
import base64
import binascii
import urllib.parse
import re



data_list = []

data_dir1 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA"
data_list1 = os.listdir(data_dir1)
data_list += [(data_dir1, x) for x in data_list1]


data_dir2 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA2\DATA"
data_list2 = os.listdir(data_dir2)
data_list += [(data_dir2, x) for x in data_list2]


data_dir3 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\广西电信_ALL_DATA\DATA"
data_list3 = os.listdir(data_dir3)
data_list += [(data_dir3, x) for x in data_list3]

data_dir4 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\重庆移动_ALL_DATA\DATA"
data_list4 = os.listdir(data_dir4)
data_list += [(data_dir4, x) for x in data_list4]


data_dir5 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\DATA-0810\DATA"
data_list5 = os.listdir(data_dir5)
data_list += [(data_dir5, x) for x in data_list5]



all_data = []

for data_dir, data_name in data_list:
    raw_info = None
    file_data = []
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line == "":
                continue
            if raw_info is None:
                raw_info = line
            elif line[0] != "{":
                raw_info = line
            else:
                format_data = json.loads(line.strip())
                file_data.append("详细分析以下信息：" + raw_info)

    file_data = [x for x in file_data if len(x) <= 1000]
    all_data += random.sample(file_data, min([2, len(file_data)]))
    vender = format_data.get('=vender', "").strip()
    vender = vender if len(vender) > 0 else format_data.get('vender', "").strip()
    if (vender != "绿盟") and (format_data['product_type'] != "4A") and (
            format_data['product_type'] != "DAS"):
        for i in range(2):
            all_data += random.sample(file_data, min([5, len(file_data)]))

    if (format_data['product_type'] == "TAC") or (format_data['product_type'] == "EDR"):
        for i in range(3):
            all_data += random.sample(file_data, min([5, len(file_data)]))

all_data = [x for x in all_data]
log_data = random.sample(all_data, 150)

print("log data size: %s" % len(log_data))



# vul
all_data = []
with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset\知识库数据\vulnerabiliry/cnnvd-node-all-20230313.1.json", 'r', encoding='utf-8') as f:
    for line in f:
        point = json.loads(line)
        new_point = {}
        for k,v in point.items():
            #if k not in ["id", "object", "description_zh"] and "predict" not in k:
            new_point[k] = v
            if k == "external_references":
                new_point[k] = v if not v else v[:200]
        # if len(point.get("description_zh", "")) > 10:
        #     new_point["description"] = point["description_zh"]
        if (point.get("type", "") == "vulnerability") and ("CVE" in str(point.get("name", "1"))):
            all_data.append("详细分析以下信息：" + json.dumps(new_point, ensure_ascii=False))

all_data = [x for x in all_data if len(x)> 50 and len(x)< 1500]
vul_data = random.sample(all_data, 50)

print("vul data size: %s" % len(vul_data))



# table data
table_data = []
with open(r"C:\Users\<USER>\Desktop\train.json", 'r', encoding='utf-8') as f:
    all_data = json.load(f)
    for point in all_data:
        table = point.get("table", [])
        if len(table) < 2:
            continue
        table_data.append("详细分析以下信息：" + json.dumps(table, ensure_ascii=False))

all_data = [x for x in table_data if len(x)> 50 and len(x)< 1000]
table_data = random.sample(all_data, 100)

print("table data size: %s" % len(table_data))

all_data = log_data + vul_data + table_data

print("all data size: %s" % len(all_data))


use_data = [{"input": x} for x in all_data]



api_key = "ec4735370807779fffab0b79b320d307.ewzyS0ke5o2ellr8"
from zhipuai import ZhipuAI
client = ZhipuAI(api_key=api_key) # 填写您自己的APIKey
for i in tqdm(use_data):
    try:
        response = client.chat.completions.create(
            model="glm-4",  # 填写需要调用的模型名称
            messages=[
                {"role": "user", "content": i["input"]}
            ],
            top_p=0.7,
            temperature=0.7,
        )
        i['output'] = response.choices[0].message.content
    except:
        pass


async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://************:8080/v1/chat/completions"

        body = {
            "model":"secllm-v2-format",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.8,
            "frequency_penalty": 0.2,
            "max_tokens": 800,
            "messages": [{"role": "user", "content": point["input"]}],
            # "repetition_penalty": 1.1,
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""

        point['secllm_answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 30  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



print("all data size: %s" % len(use_data))


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))
#
with open(r"D:\work\GPT\chatgpt_label/log_parse_dpo_label_1-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

with open(r"D:\work\GPT\chatgpt_label/log_parse_dpo_label_1-o-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)

print("Done")




api_key = "ec4735370807779fffab0b79b320d307.ewzyS0ke5o2ellr8"
from zhipuai import ZhipuAI
client = ZhipuAI(api_key=api_key) # 填写您自己的APIKey
for i in tqdm(use_data):
    try:
        if "output" not in i:
            response = client.chat.completions.create(
                model="glm-4",  # 填写需要调用的模型名称
                messages=[
                    {"role": "user", "content": i["input"]}
                ],
                top_p=0.7,
                temperature=0.7,
            )
            i['output'] = response.choices[0].message.content
    except:
        pass
