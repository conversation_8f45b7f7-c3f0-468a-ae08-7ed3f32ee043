# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: z<PERSON><PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm



prompt = """解决给出的数学题。首先分析解题思路（Thought），然后一步步详细写出具体的解题步骤（Steps），最后给出答案（Answer）。
解答以json的格式给出，如下：
{
    "Thought": 分析解题思路,
    "Steps": 一步步详细写出具体的解题步骤
    "Answer": 最后的答案
}

这是一个示例：
问题：小王要将150千克含药量20%的农药稀释成含药量5%的药水．需要加水多少千克？
解答：
{example_answer}

问题：{question}
解答："""

example_ans = {
    "Thought": "此处先通过150*20%计算得到不含水的农药重量。然后使用不含水分的农药重量除以稀释后的含药量5%，得到稀释后的药水总重量。最后减去最初的药水重量，就可以得到需要加的水的重量。",
    "Steps": "1.首先，我们需要计算出不含水的农药重量。小王最初有的农药重150千克，含药量是20%。150*20%=30。即有30千克是不含水的农药的重量，有150-30=120千克是水。\n2.然后，我们需要计算出稀释后的药水总重量。有30千克是不含水的农药的重量，稀释后含药量降为5%。30/5%=600千克。即稀释后的药水重量为600千克。\n3.最后，使用稀释后的药水的重量减去原始的药水重量，我们可以得到需要增加的水的重量：600-150=450千克。\n4.所以，小王需要增加的水的重量是：150*20%/5%-150=450千克。",
    "Answer": "450kg"
}
example_ans_str = json.dumps(example_ans,  ensure_ascii=False)

prompt_replace = prompt.replace("{example_answer}", example_ans_str)



async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data, headers={"appkey": "1c9190ca8c5687605613191d61c0363585952e23423221e88ca294e0a7ac5665"}) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://10.67.15.11:5500/api/v1/secllm/secllm_server/completions"  # "http://10.24.45.213:8080/v1/chat/completions"
        messages = [
            {"role": "user",
             "content": prompt_replace.replace("{question}", point['question_chinese'])}
        ]

        body = {
            "model":"secllm-v2",
            "stream": False,
            "top_p": 0.3,
            "temperature": 0.1,
            # "frequency_penalty": 0.2,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point['answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 50  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


path = r"D:\work\GPT\chatgpt_label/ape210k-train.jsonl"
math_question = []
with open(path, 'r', encoding='utf-8') as f:
    for line in f:
        line = json.loads(line.strip())
        math_question.append(line)

print("data size: %s" %  len(math_question))
print("data example: ", math_question[0])

use_data = []
for i in range(50000):  # 抽取数据的前1W条
    # for _ in range(10):  # 每条数据标注10次
    new_point = copy.deepcopy(math_question[i])
    use_data.append(new_point)

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))

print("Done")


# with open(r"D:\work\GPT\chatgpt_label/ape210k_label_2.json", 'w', encoding='utf-8') as fp:
#     json.dump(results, fp, indent=4, ensure_ascii=False)
