# -*- encoding: utf-8 -*-
# @ModuleName: event_parse
# @Author: zhanglong
# @Time: 2023/7/13 14:53
import base64
import http.client
import re
import urllib.parse
import json


def is_json(text):
    try:
        json.loads(text)
        return True
    except ValueError:
        return False


def if_contain_chaos(keyword):  # detect the gibberish      检测乱码（类似正常字符串的乱码无法检验）
    try:
        keyword.encode("gb2312")
    except UnicodeEncodeError:
        return True
    return False


def check_payload_request_valid(dict1, item):  # 检测载荷和请求体是否有乱码，是否有GET等参数
    if "GET" in dict1[item] or "POST" in dict1[item] or "PUT" in dict1[item] or "OPTIONS" in dict1[item] or "HEAD" in \
            dict1[
                item]:
        index_list = []
        index1 = dict1[item].find("GET")
        index_list.append(index1)
        index2 = dict1[item].find("POST")
        index_list.append(index2)
        index3 = dict1[item].find("PUT")
        index_list.append(index3)
        index4 = dict1[item].find("OPTIONS")
        index_list.append(index4)
        index5 = dict1[item].find("HEAD")
        index_list.append(index5)
        index_list = list(filter(lambda x: x >= 0, index_list))
        index = min(index_list)
        dict1[item] = dict1[item][index:]
    if if_contain_chaos(dict1[item]):
        dict1[item] = ""
    if "GET" in dict1[item] or "POST" in dict1[item] or "PUT" in dict1[item] or "OPTIONS" in dict1[item] or "HEAD" in \
            dict1[item]:
        pass
    else:
        dict1[item] = ""


def keep_or_not(dict1, item1, item2):  # 载荷和请求体都为空或为乱码
    if dict1[item1] == "" and dict1[item2] == "":
        return False


def base64_converter(dict1, item):
    base64_string = dict1[item]
    base64_bytes = base64_string.encode("ascii")
    sample_string_bytes = base64.b64decode(base64_bytes)
    sample_string = sample_string_bytes.decode("utf-8")
    return sample_string


def decode_unicode(string):  # unicode翻译
    # 使用正则表达式提取所有的 Unicode 码
    unicode_list = re.findall(r'\\u[0-9a-fA-F]{4}', string)

    # 将每个 Unicode 码进行解码并替换原始字符串中的对应部分
    for unicode_str in unicode_list:
        decoded_str = bytes(unicode_str.encode('ascii')).decode('unicode-escape')
        string = string.replace(unicode_str, decoded_str)
    return string


def decode_url(url):  # url翻译
    pattern = r'%[0-9a-fA-F]{2}'  # 匹配URL编码的模式，如%20、%3A、%2F等

    def decode_match(match):
        encoded = match.group()  # 获取匹配到的URL编码
        decoded = urllib.parse.unquote(encoded)  # 解码URL编码
        return decoded

    decoded_url = re.sub(pattern, decode_match, url)  # 使用解码函数替换URL中的编码部分

    return decoded_url


def parse_request(request, split_sign1, split_sign2):  # 请求体解析
    # if split_sign1 == "\r\n\r\n":
    #     request = request.replace('\r', '')
    parse_result = {}

    # 拆分请求行和请求体
    if split_sign2 == "\r\n":  # 以防当内容中出现\n时，程序处理结果不理想
        request_line, headers_and_body = request.split('\r\n', 1)
    else:
        request_line, headers_and_body = request.split('\n', 1)

    # 解析请求行
    line_list = request_line.strip().split(' ')
    method = line_list[0].strip().upper()  # 得到GET和POST
    http_version = line_list[-1].strip().upper()  # 得到protocol
    path = ' '.join(line_list[1:-1]).strip()  # 得到中间部分，接下来对中间部分进行处理

    parse_result["Method"] = method
    parse_result["Path"] = path
    parse_result["HTTP_Version"] = http_version

    # 解析请求头
    headers, body = headers_and_body.split(split_sign1, 1)
    headers = headers.strip()
    body = body.strip()

    if split_sign2 == '\r\n':
        header_list = [x.strip() for x in headers.split('\r\n')]  # 将header中的其他参数提取成字典，并存入parse_result
    else:
        header_list = [x.strip() for x in headers.split('\n')]  # 将header中的其他参数提取成字典，并存入parse_result
    headers_dict = {x.split(':')[0].strip(): ':'.join(x.split(':')[1:]).strip() for x in header_list}
    parse_result['Headers_Dict'] = {}
    parse_result['Headers_Dict'] = headers_dict
    print(parse_result['Headers_Dict'])

    # 解析请求参数
    if method == 'GET':  # 对请求头中的参数进行解析（parsed_param）
        if path.find('?') != -1:  # 会出现没有?的情况
            parsed_url, parsed_param = path.split('?', 1)
            parse_result["URL"] = parsed_url

            parsed_param = parsed_param.strip().split('&')
            param_list = [""]
            for param in parsed_param:
                if '=' in param:
                    param_list.append(param)
                else:
                    param_list[-1] = param_list[-1] + "&" + param
            param_list = [x.strip() for x in param_list if len(x.strip()) > 0]

            parse_result['Query_Param'] = {x.split('=')[0].strip(): '='.join(x.split('=')[1:]).strip() for x in
                                           param_list}
        else:
            parsed_url = path
            parse_result["URL"] = parsed_url


    elif method == 'POST':

        if path.find('?') != -1:
            parsed_url, parsed_param = path.split('?', 1)
            parse_result["URL"] = parsed_url

            parsed_param = parsed_param.strip().split('&')
            param_list = [""]
            for param in parsed_param:
                if '=' in param:
                    param_list.append(param)
                else:
                    param_list[-1] = param_list[-1] + "&" + param
            param_list = [x.strip() for x in param_list if len(x.strip()) > 0]

            parse_result['Query_Param'] = {x.split('=')[0].strip(): '='.join(x.split('=')[1:]).strip() for x in
                                           param_list}
        else:
            parsed_url = path
            parse_result["URL"] = parsed_url

        if is_json(body):  # 是json格式
            json_dict = json.loads(body)
            parse_result['Body'] = {}
            parse_result['Body'] = json_dict
        else:  # 不是json格式
            para = []
            value = []
            para.append("string")
            value.append(body)
            para_as_string = dict(zip(para, value))
            parse_result['Body'] = {}
            parse_result['Body'] = para_as_string
    return parse_result


dict_list = []
error_count = 0
with open('威胁事件20230411-0420.txt', 'r', encoding='utf-8') as fp:
    # load()函数将fp(一个支持.read()的文件类对象，包含一个JSON文档)反序列化为一个Python对象
    for line in fp.readlines():
        data = json.loads(line)  # 经常报错的原因，两个json中间不能出现空行
        check_payload_request_valid(data, "载荷")
        check_payload_request_valid(data, "请求体")
        flag = keep_or_not(data, "载荷", "请求体")
        if flag == False:       # 若两个都为空，则跳过该json
            continue
        else:
            if "请求体_base64" in data.keys(): # 翻译base64
                base_64_string = base64_converter(data, "请求体_base64")
                data["请求体_txt"] = base_64_string
            data['载荷'] = decode_unicode(data['载荷'])     # 对整个字符串进行url和unicode翻译
            data['请求体'] = decode_unicode(data['请求体'])
            data['载荷'] = decode_url(data['载荷'])
            data['请求体'] = decode_url(data['请求体'])

            try:
                if data["请求体"].find('\r\n\r\n') != -1:
                    data_finished = parse_request(data["请求体"], '\r\n\r\n', '\r\n')
                else:
                    if data["请求体"].find('\n \n') != -1:
                        data_finished = parse_request(data["请求体"], '\n \n', '\n')
                    else:
                        if data["请求体"].find('\n\n') != -1:
                            data_finished = parse_request(data["请求体"], '\n\n', '\n')
                data["parse"] = {}
                data["parse"] = data_finished
                dict_list.append(data)
            except ValueError:  # 有一些会报错，数量不多，找不到具体原因
                error_count += 1
                print("Wrong test")

for dict1 in dict_list:
    print(dict1)

# print(len(dict_list))
print(error_count)

with open("威胁事件20230411-0420", "w", encoding="utf-8") as f:
    json.dump(dict_list, f)
