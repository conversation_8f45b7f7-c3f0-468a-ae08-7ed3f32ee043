name: Test Documentation on PR
on:
  pull_request:
    branches:
      - "main"
      - "develop"
      - "feature/**"
    # any change in the examples folder will trigger check for the corresponding example.
    paths:
      - "docs/source/**.md"

jobs:
  # This is for changed example files detect and output a matrix containing all the corresponding directory name.
  detect-changed-doc:
    if: |
      github.event.pull_request.draft == false &&
      github.event.pull_request.base.repo.full_name == 'hpcaitech/ColossalAI' && github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    outputs:
      any_changed: ${{ steps.changed-files.outputs.any_changed }}
      changed_files: ${{ steps.changed-files.outputs.all_changed_files }}
    concurrency:
      group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}-detect-change
      cancel-in-progress: true
    name: Detect changed example files
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Locate base commit
        id: locate-base-sha
        run: |
          curBranch=$(git rev-parse --abbrev-ref HEAD)
          commonCommit=$(git merge-base origin/main $curBranch)
          echo $commonCommit
          echo "baseSHA=$commonCommit" >> $GITHUB_OUTPUT

      - name: Get all changed example files
        id: changed-files
        uses: tj-actions/changed-files@v35
        with:
          base_sha: ${{ steps.locate-base-sha.outputs.baseSHA }}
          files: |
            ./docs/source/**/*.md

  # If no file is changed, it will prompt an error and shows the matrix do not have value.
  check-changed-doc:
    # Add this condition to avoid executing this job if the trigger event is workflow_dispatch.
    if: |
      github.event.pull_request.draft == false &&
      github.event.pull_request.base.repo.full_name == 'hpcaitech/ColossalAI' && github.event_name == 'pull_request' &&
      needs.detect-changed-doc.outputs.any_changed == 'true'
    name: Test the changed Doc
    needs: detect-changed-doc
    runs-on: [self-hosted, gpu]
    container:
      image: hpcaitech/pytorch-cuda:1.12.0-11.3.0
      options: --gpus all --rm
    timeout-minutes: 20
    defaults:
      run:
        shell: bash
    concurrency:
      group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}-run-doctest
      cancel-in-progress: true
    steps:
      - name: Checkout ColossalAI-Documentation
        uses: actions/checkout@v2
        with:
          path: "./ColossalAI-Documentation"
          repository: "hpcaitech/ColossalAI-Documentation"

      - name: Install Docer
        run: |
          pip install -v ./ColossalAI-Documentation/doc-build/third_party/hf-doc-builder
          pip install -v ./ColossalAI-Documentation/doc-build

      - name: Checkout ColossalAI
        uses: actions/checkout@v3

      - name: Install Doc Test Requirements
        run: |
          source activate pytorch
          conda env update --file docs/conda-doc-test-deps.yml --prune
          pip install -r docs/requirements-doc-test.txt

      - name: Install ColossalAI
        run: |
          source activate pytorch
          CUDA_EXT=1 pip install -v .

      - name: Test the Doc
        run: |
          source activate pytorch
          for file in ${{ needs.detect-changed-doc.outputs.changed_files }}; do
            echo "Testing $file now..."
            docer test -p $file
          done
        env:
          NCCL_SHM_DISABLE: 1
