# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhang<PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset




# prompt = """
# 我会给你一个问题和对问题的分析，你需要指出分析中存在的错误，给出修改建议，并给出修改后的正确分析和答案。
#
# 问题：Today I have three apples. Yesterday I ate one apple. How many apples do I have today?
#
# 分析：
# 这个问题是一个简单的数学问题，涉及到基本的减法运算。用户想要知道在昨天吃了一个苹果后，今天他还剩下多少个苹果。
#
# ### 潜在问题与提问意图分析
#
# - **潜在问题**：用户可能对基本的算术运算有疑问，或者是在教授孩子基础数学概念时需要一个清晰的解释。
# - **解决问题的关键点**：理解“昨天”和“今天”的时间概念，以及如何从总数中减去已消耗的数量。
# - **回答问题时可能出现的错误点**：忽略时间线（即昨天和今天的区别），直接将昨天吃的苹果数从今天的苹果数中减去，而没有考虑到昨天的苹果数可能与今天的苹果数不同。
#
# ### 解决思路
#
# 1. **确认当前状态**：今天你有三个苹果。
# 2. **考虑过去的行为**：昨天你吃了一个苹果。
# 3. **应用数学运算**：从今天的苹果总数中减去昨天吃的苹果数量。
# 4. **得出结论**：计算结果就是你现在拥有的苹果数量。
#
# ### 答案
#
# ```markdown
# 今天你有三个苹果。昨天你吃了一个苹果。这意味着你从昨天的苹果总数中减去了一个苹果。但是，题目中没有明确说明昨天你有多少苹果，只提到了今天的苹果数量。因此，我们只能基于今天的信息来计算。
#
# 既然今天你有三个苹果，而昨天你吃掉了一个苹果，并不会影响到今天你手上的苹果数量，因为你今天仍然有三个苹果。所以，今天你有三个苹果。
# ```
#
# 然而，如果问题是想表达昨天原本的苹果数量，再减去昨天吃掉的苹果数量，那么我们需要假设昨天你至少有一个苹果。在这种情况下，昨天你吃掉一个苹果后，如果你昨天开始时有三个苹果，那么昨天结束时你会剩下两个苹果。但根据题目描述，这个逻辑并不适用，因为我们只关心今天的苹果数量，即三个。
# """


prompt = """
我会给你一个问题和对问题的分析，你需要依据分析给出问题的答案。

问题：{question}

分析：
{analysis}


输出格式如下：
中文问题：[翻译成中文的问题]
中文答案: [依据分析给出中文的答案]
"""

async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="re_prompt", answer_name="re_answer"):
    async with semaphore:
        url = "http://10.37.4.2:8085/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model":"secllm-v2",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 1500  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



with open("/home/<USER>/llm_dataset/re_label/re_lable.json", "r", encoding="utf-8") as f:
    data = json.load(f)

for point in data:
    point["re_prompt"] = prompt.replace("{question}", point["question"]).replace("{analysis}", point["answer"])

# data = data

print("data size: %s" %  len(data))
print("data example: ", data[0])

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/re_label/re_lable_answer.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
