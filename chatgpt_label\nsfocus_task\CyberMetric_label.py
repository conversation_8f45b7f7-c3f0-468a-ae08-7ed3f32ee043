# -*- encoding: utf-8 -*-
"""
@File    :   custom_evaluator.py
@Time    :   2024/05/07 10:40:25
<AUTHOR>   <PERSON><PERSON><PERSON>
@Version :   1.0

@description :   使用CyberMetric评估我们自己的开源模型的基础安全能力
"""

# 梦想从这里启航，獭獭开！

from __future__ import print_function


import json
import re
import time
import asyncio
import aiohttp
from tqdm import tqdm


async def post_request(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer

def extract_answer(response):
    if response.strip():  # Checks if the response is not empty and not just whitespace
        # match = re.search(r"ANSWER:?\s*([A-D])", response, re.IGNORECASE)
        match = re.search(r"(?i)\n+ANSWER\s*:\s*([A-D])", response)
        # match = re.findall(r"(?i)ANSWER\s*:\s*([A-D])", response, re.IGNORECASE)
        # match = re.findall(r"ANSWER:?\s*([A-D])", response, re.IGNORECASE)

        if match:
            # return match[-1].upper()  # Return the matched letter in uppercase
            return match.group(1).upper()
    return None


async def ask_llm(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://************:59052/v1/chat/completions"

        body = {
            "model":"Qwen2.5-72B-Instruct",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            # "frequency_penalty": 0.2,
            "max_tokens": 2048,
            "messages": [
                {"role": "system", "content": "You are a security expert who answers questions."},
                {"role": "user", "content": point["prompt"]},
            ],
            "repetition_penalty": 1.05,
        }

        point["response"] = []

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request(session, url, body)
            point["response"].append(answer)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            point["evaluation"]= "API Error"
        else:
            llm_answer = extract_answer(answer)
            if not llm_answer:
                point["evaluation"]= "Answer Extract Error"
            elif llm_answer.strip().lower() == point["solution"].strip().lower():
                point["evaluation"]= "Correct"
            else:
                point["evaluation"]= "Incorrect"

        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [ask_llm(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


def calculate_scores(results):
    total_count = len(results)
    correct_count = sum(1 for result in results if result["evaluation"] == "Correct")
    incorrect_count = sum(1 for result in results if result["evaluation"] == "Incorrect")
    api_error_count = sum(1 for result in results if result["evaluation"] == "API Error")
    extract_error_count = sum(1 for result in results if result["evaluation"] == "Answer Extract Error")


    print(f"\nTotal: {total_count}\n"
          f"Correct: {correct_count}\t{correct_count / total_count * 100}%\n"
          f"Incorrect: {incorrect_count}\t{incorrect_count / total_count * 100}%\n"
          f"Answer Extract Error: {extract_error_count}\t{extract_error_count / total_count * 100}%\n"
          f"API Error: {api_error_count}\t{api_error_count / total_count * 100}%\n")

    print(f"\nFinal Accuracy: {correct_count / total_count * 100}%")



prompt_template_cot_raw = """
Answer the following choice question. Choose the correct answer (A, B, C, or D) only. The last line of your response should be of the following format: 'ANSWER: $LETTER' (without quotes) where LETTER is one of ABCD. Think step by step before answering.

Question: {Question}
Options: 
A) {A}
B) {B}
C) {C}
D) {D}
"""


prompt_template_cot = """
Answer the following choice question. Choose the correct answer (A, B, C, or D) only. 
The last line of your response should be of the following format: 'ANSWER: $LETTER' (without quotes) where LETTER is one of ABCD. 
Think step by step and analyze each option based on reference answer before answering. 

![Remember]: Do not mention the existence of reference answers in your answers and thoughts!

Question: {Question}
Reference Answer: {Reference_Answer}
Options: 
A) {A}
B) {B}
C) {C}
D) {D}
"""

prompt_template_few_shot = """
Answer the following choice question. Choose the correct answer (A, B, C, or D) only. Always return in this format: 'ANSWER: $LETTER' (without quotes) where LETTER is one of ABCD.

Examples:

Question: Which of the following refers to the secrecy of information?
Options: 
A) Availability
B) Authentication
C) Integrity
D) Confidentiality
Answer: D

Question: Which type of authentication uses multiple factors for verification, such as something you know, something you have, and something you are?
Options: 
A) Dual-factor authentication
B) Single-factor authentication
C) Multi-factor authentication
D) Verification authentication
Answer: C

---

YOUR TASK

Question: {Question}
Options: 
A) {A}
B) {B}
C) {C}
D) {D}
"""

prompt_template = """
Answer the following choice question. 
Question: {Question}
Options: 
A) {A}
B) {B}
C) {C}
D) {D}

Choose the correct answer (A, B, C, or D) only. Always return in this format: 'ANSWER: $LETTER' (without quotes) where LETTER is one of ABCD.
"""

def make_prompt(point, cot=False, few_shot=False):
    answers = point["answers"]
    question = point["question"]
    if cot:
        prompt = prompt_template_cot.format(Question=question, A=answers["A"], B=answers["B"], C=answers["C"], D=answers["D"], Reference_Answer=point["solution"]).strip()
        prompt_raw = prompt_template_cot_raw.format(Question=question, A=answers["A"], B=answers["B"], C=answers["C"], D=answers["D"]).strip()
        point["prompt_raw"] = prompt_raw
    elif few_shot:
        prompt = prompt_template_few_shot.format(Question=question, A=answers["A"], B=answers["B"], C=answers["C"], D=answers["D"]).strip()
    else:
        prompt = prompt_template.format(Question=question, A=answers["A"], B=answers["B"], C=answers["C"], D=answers["D"]).strip()
    point["prompt"] = prompt
    return point



start_time = time.time()
print("\n\nCoT Evaluation")

with open(r"D:\work\GPT\chatgpt_label\CyberMetric-10000-v1.json", 'r', encoding='utf-8') as f:
    data = json.load(f)["questions"]
    for point in data:
        make_prompt(point, cot=True)
print("data size: %s" %  len(data))

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

with open(r"D:\work\GPT\chatgpt_label/CyberMetric_cot_label_3.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

calculate_scores(results)
print("CoT Evaluation Done")

end_time = time.time()
print(f"Time Cost: {end_time - start_time} seconds.")


