# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhang<PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset




prompt = """
我会给你一段文本，你需要在文本的每个步骤(step)后面插入字符标识: <SPLIT>。
！注意，不允许修改文本原文！
！注意，仅仅返回标识后的文本，不要写任何其他的描述或说明！

需要标识切分的文本：
{text}
"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="split_answer"):
    async with semaphore:
        url = "http://10.37.4.2:50002/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 150  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
with open("/home/<USER>/llm_dataset/MetaMathQA/re_label_answer_0_100000_convert.json", 'r', encoding='utf-8') as fp:
    raw_data = json.load(fp)

with open("/home/<USER>/llm_dataset/MetaMathQA/re_label_answer_verify.json", 'r', encoding='utf-8') as fp:
    verify_data = json.load(fp)

all_data = []
for verify, raw in zip(verify_data[:len(raw_data)], raw_data):
    assert verify["query"] == raw["query"]
    raw["verify"] = verify["answer"]
    if ("Errors" not in verify["answer"]) and (len(verify["answer"].strip()) > 0) and (len(raw["answer"].strip()) > 0)and (len(raw["convert_answer"].strip()) > 0):
        all_data.append(raw)
    
def make_conv(point):
        output = point["convert_answer"].strip()
        output = output.replace("Chinese Question:", "Chinese Question：").replace("Chinese Answer:", "Chinese Answer：")
        if ("Chinese Question：" not in output) or ("Chinese Answer：" not in output):
            return []
        if not output.startswith("Chinese Question："):
            return []
        output = output.replace("Chinese Question：", "").strip()
        answer = output.split("Chinese Answer：")
        if len(answer) != 2:
            return []
        question = answer[0].strip()
        answer = answer[1].strip()
        if (len(question) <= 0) or (len(answer) <= 0):
            return []
        return [{"from": "human", "value":question}, {"from": "assistant", "value": answer}]


split_data_en = []
split_data_zh = []

for point in all_data:
    new_point = {
        "type": point["type"],
        "query": point["query"],
        "answer": point["answer"],
        "prompt": prompt.replace("{text}", point["answer"])
    }
    split_data_en.append(new_point)
    
for point in all_data:
    conv_list = make_conv(point)
    
    if len(conv_list) > 0:
        new_point = {
            "type": point["type"],
            "query": conv_list[0]["value"],
            "answer": conv_list[1]["value"],
            "prompt": prompt.replace("{text}", conv_list[1]["value"])
        }
        split_data_zh.append(new_point)


print("\nen data size: %s" % len(split_data_en))
print("\nzh data size: %s" % len(split_data_zh))

print("\n\nprompt over. prompt example:\n%s" % split_data_zh[0]["prompt"])


data = split_data_zh[:10000] + split_data_en[:10000]

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/MetaMathQA/re_label_answer_0_100000_step.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
