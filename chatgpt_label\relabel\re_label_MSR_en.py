# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import os
import yaml





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.2:50002/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Athene-V2-Chat", #"Mistral-Large-Instruct-2407",  #"secllm-v2",  #"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 80  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")

import pandas as pd
data = pd.read_csv("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/MSR_data_cleaned.csv")

data = data.to_dict(orient="records")

vul_data = [x for x in data if x["vul"] == 1]
vul_data = [x for x in vul_data if isinstance(x["CWE ID"], str) and isinstance(x["CVE ID"], str)]

non_vul_data = [x for x in data if x["vul"] == 0]

# vul_data = vul_data[:10]
# non_vul_data = non_vul_data[:10]

# Randomly select 50,000 non-vulnerability samples without replacement
import random
non_vul_data = random.sample(non_vul_data, min(50000, len(non_vul_data)))


print(f"读取到 {len(data)} 个数据条目, 其中 {len(vul_data)} 个漏洞数据, {len(non_vul_data)} 个非漏洞数据")

sys_prompt_analysis_MSR_vul_with_referenceId = """
Code:
{{func_before}}


==========================
Reference Information:

Patched Code:
{{func_after}}

{{Summary}}

Possible CVE ID: {{CVE ID}} 
(Note: This CVE ID should be considered as a reference priority but may be incorrect)

Possible CWE ID: {{CWE ID}} 
(Note: This CWE ID should be considered as a reference priority but may be incorrect)

==========================
Please analyze following these steps:
1. Code Content Analysis
Analyze the code content in detail to identify any potential vulnerabilities or bugs.
Your analysis must be thorough and comprehensive!

2. Vulnerability Analysis
If vulnerabilities/bugs are found, analyze their types and corresponding CWE IDs. Explain how these vulnerabilities form and their potential impact.
Your analysis must be detailed and comprehensive!

3. Vulnerability Type & CWE ID
Provide the vulnerability type and corresponding CWE ID.

4. JSON Format Result

Finally, provide the result in JSON format enclosed by triple backticks. Only list CWEs detected as "Present", exclude any "Not Present" results.

JSON format example:

vul_json = {
    "findings": [
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 1",
            "CWE_Number": "CWE ID 1",
            "Status": "Present"
        }, 
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 2",
            "CWE_Number": "CWE ID 2",
            "Status": "Present"
        }
    ]
}

============================
Important Notes:
- Analyze ONLY the provided code, do not consider external context.
- Use reference information for analysis but DO NOT mention it in your response.
- Reference CVE/CWE IDs might be incorrect - use your own judgment.
- Assign only ONE most relevant CWE per code block.
"""


sys_prompt_analysis_MSR_vul_without_referenceId = """
Code:
{{func_before}}


==========================
Reference Information:

Patched Code:
{{func_after}}

{{Summary}}

==========================
Please analyze following these steps:
1. Code Content Analysis
Analyze the code content in detail to identify any potential vulnerabilities or bugs.
Your analysis must be thorough and comprehensive!

2. Vulnerability Analysis
If vulnerabilities/bugs are found, analyze their types and corresponding CWE IDs. Explain how these vulnerabilities form and their potential impact.
Your analysis must be detailed and comprehensive!

3. Vulnerability Type & CWE ID
Provide the vulnerability type and corresponding CWE ID.

4. JSON Format Result

Finally, provide the result in JSON format enclosed by triple backticks. Only list CWEs detected as "Present", exclude any "Not Present" results.

JSON format example:

vul_json = {
    "findings": [
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 1",
            "CWE_Number": "CWE ID 1",
            "Status": "Present"
        }, 
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 2",
            "CWE_Number": "CWE ID 2",
            "Status": "Present"
        }
    ]
}

============================
Important Notes:
- Analyze ONLY the provided code, do not consider external context.
- Use reference information for analysis but DO NOT mention it in your response.
- Reference CVE/CWE IDs might be incorrect - use your own judgment.
- Assign only ONE most relevant CWE per code block.
"""

sys_prompt_analysis_MSR_non_vul = """
Code:
{{func_before}}

==========================
Reference Information:
This code contains no vulnerabilities

==========================
Please analyze following these steps:
1. Code Content Analysis
Analyze the code content in detail to identify any potential vulnerabilities or bugs.
Your analysis must be thorough and comprehensive!

2. Vulnerability Analysis
If vulnerabilities/bugs are found, analyze their types and corresponding CWE IDs. Explain how these vulnerabilities form and their potential impact.
Your analysis must be detailed and comprehensive!

3. Vulnerability Type & CWE ID
Provide the vulnerability type and corresponding CWE ID.

4. JSON Format Result

Finally, provide the result in JSON format enclosed by triple backticks. Only list CWEs detected as "Present", exclude any "Not Present" results.

JSON format example:

vul_json = {
    "findings": [
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 1",
            "CWE_Number": "CWE ID 1",
            "Status": "Present"
        }, 
        {
            "Description": "Vulnerability type summary and cause analysis",
            "Source_Code": "Vulnerable code segment 2",
            "CWE_Number": "CWE ID 2",
            "Status": "Present"
        }
    ]
}

============================
Important Notes:
- Analyze ONLY the provided code, do not consider external context.
- Use reference information for analysis but DO NOT mention it in your response.
- Reference CVE/CWE IDs might be incorrect - use your own judgment.
- Assign only ONE most relevant CWE per code block.
Important Note: The reference information indicates this code is non-vulnerable.
"""

# 生成prompt，vul no id数据复制5倍

all_data = []

for raw_point in vul_data:
    for i in range(5):
        point = copy.deepcopy(raw_point)
        point["prompt"] = sys_prompt_analysis_MSR_vul_with_referenceId.replace("{{func_before}}", point["func_before"]).replace("{{func_after}}", point["func_after"]).replace("{{Summary}}", point["Summary"]).replace("{{CVE ID}}", point["CVE ID"]).replace("{{CWE ID}}", point["CWE ID"])
        all_data.append(point)

    # for i in range(5):
    #     point = copy.deepcopy(raw_point)
    #     point["prompt"] = sys_prompt_analysis_MSR_vul_without_referenceId.replace("{{func_before}}", point["func_before"]).replace("{{func_after}}", point["func_after"]).replace("{{Summary}}", point["Summary"])
    #     all_data.append(point)

for point in non_vul_data:
    point["prompt"] = sys_prompt_analysis_MSR_non_vul.replace("{{func_before}}", point["func_before"])
    all_data.append(point)

print("\n\ndata size: %s" %  len(all_data))
print("\ndata example: \n\n%s" % all_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(all_data))

print("Done")


with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/relabel_MSR_vul_en.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)


with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/relabel_MSR_vul_en.json", 'r', encoding='utf-8') as fp:
    all_data = json.load(fp)

# 解析answer，并与原始CWE比对，过滤删除错误样本

def extract_vul_json(text):
    # Step 1: Split the text by the word "findings", and take the last part
    last_part = text.split("findings")[-1].split("```")[0]
    
    # Step 2: Use regex to find the content between [ and ] (inclusive)
    match = re.search(r"\[.*\]", last_part, re.DOTALL)
    
    if match:
        # Extract the content inside [ ] and form the correct JSON structure
        findings_content = match.group(0)
        
        # Construct the full JSON with "findings" key and the extracted content
        # vul_json_str = '{"findings": ' + findings_content + '}'
        vul_json_str = findings_content
        try:
            vul_json = json.loads(vul_json_str)
            vul_json = {"findings": vul_json}
            return vul_json
        except:
            return None
    else:
        return None
    
for point in all_data:
    json_str = extract_vul_json(point["answer"])
    if json_str is not None:
        # vul_json = json.loads(json_str)
        point["vul_json_output"] = json_str
        point["is_vul_output"] = True
    else:
        point["is_vul_output"] = False


filtered_data = []
for point in all_data:
    if not point["is_vul_output"]:
        continue
    if point["vul"] == 0 and len(point["vul_json_output"]["findings"]) == 0:
        filtered_data.append(point)
        continue
    if point["vul"] == 1 and len(point["vul_json_output"]["findings"]) > 0 and point["CWE ID"] == point["vul_json_output"]["findings"][0]["CWE_Number"]:
        filtered_data.append(point)


print("\n\nfiltered data size: %s, vul filtered: %s" % (len(filtered_data), len([x for x in filtered_data if x["vul"] == 1])))

with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/relabel_MSR_vul_en_filtered.json", 'w', encoding='utf-8') as fp:
    json.dump(filtered_data, fp, indent=4, ensure_ascii=False)
