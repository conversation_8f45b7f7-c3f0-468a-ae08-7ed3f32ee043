# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset



async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"

        # messages = [
        #     {"role": "user",
        #      "content": point[question_name]}
        # ]

        body = {
            "model": "Athene-V2-Chat",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 512,
            "messages": point[question_name],
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")


data_list = [
    ["/home/<USER>/llm_dataset/sec_sft.json"],
    ["/home/<USER>/llm_dataset/sec_sft_yibao.json"],
    ["/home/<USER>/llm_dataset/sec_sft_yidong.json"],
    ["/home/<USER>/llm_dataset/sec_sft_jinrong.json"],
    ["/home/<USER>/llm_dataset/sec_sft_zhongshan.json"],
    ["/home/<USER>/llm_dataset/sec_sft_all_four_0807.json"],
    ["/home/<USER>/llm_dataset/sec_sft_gdyd.json"]
]

all_data = []
for data_path in data_list:
    data_path = data_path[0]
    with open(data_path, "r", encoding="utf-8") as f:
        data = json.load(f)
    for point in data:
        point["data_source"] = data_path.split("/")[-1]
    all_data.extend(data)

print("\nraw data size: %s" % len(all_data))

for point in all_data:
    sys_prompt = point["instruction"]
    sys_prompt = sys_prompt.split("如你所见")[0].strip()
    sys_prompt = sys_prompt.split("这个列表数据如下：")[1].strip()
    sys_prompt = "你需要结合表信息和字段信息，将给定的字段分类，类别列表如下：" + sys_prompt + "\n你需要将给定的字段，根据类别列表进行分类，然后给出可解释性（分类原因），置信度(分类的置信度，0到100之间)。输出的数据格式是：{'类别': '分类结果', '可解释性': '分类原因', '置信度': '分类概率'}."
    point["prompt"] = [{"role": "system", "content": sys_prompt},
                       {"role": "user", "content": "输入：" + point["input"] + "\n" + "回答：" + point["output"] + "\n\n优化这个回答。\n注意！不允许修改答案中预测出的类别！\n注意！必须修改答案中的可解释性的表述，使其更加详细、自然，不要过于机械。然后请根据你自己的可解释性给出概率置信度。\n注意！不要添加代码块的标识，仅输出单行的json格式的结果，输出的结果需要能直接被json.loads()解析。"}]


print("\n\nprompt over. prompt example:\n%s" % all_data[0]["prompt"])


print("\n\ndata size: %s" %  len(all_data))
print("\ndata example: \n\n%s" % all_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(all_data))

print("Done")


with open("/home/<USER>/llm_dataset/sec_sft_all_relabel_json.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
