# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="instruction", answer_name="secllm_answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "secllm-v2",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 1000  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/MSR_CWE.json", "r") as f:
    data = json.load(f)
print(len(data))
# 25628

data = [x for x in data if ("source_code_fix" not in x["output"]) and ("patch" not in x["output"]) and (x["judge_cwe_label"] is True)]

for i in range(len(data)):
    data[i]["id"] = i

print(len(data))
# 24308

vul_data = [x for x in data if x["is_vul"]]  # 6159
no_vul_data = [x for x in data if not x["is_vul"]]  # 18149

all_data = []
for i in range(10):
    all_data += vul_data 

for i in range(3):
    all_data += no_vul_data 


print("\n\ndata size: %s" %  len(all_data))
print("\ndata example: \n\n%s" % all_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(all_data))

print("Done")


with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/re_lable_secllm_dpo.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)



import json

with open("/home/<USER>/llm_dataset/MSR_20_Code_vulnerability_CSV_Dataset/cwe_secllm_eval3.json", "r") as f:
    data = json.load(f)


all_data = {}

for prompt in ["prompt1", "prompt2", "prompt3", "prompt4"]:
    print("\n\n")
    print(prompt)
    all_data[prompt] = {}

    prompt_data = [x for x in data if x["prompt_id"]==prompt]
    all_data[prompt]["prompt_data"] = prompt_data
    print("data size %s: %s" % (prompt, len(prompt_data)))

    vul_data = [x for x in prompt_data if x["label"]]
    no_vul_data = [x for x in prompt_data if not x["label"]]
    all_data[prompt]["vul_data"] = vul_data
    all_data[prompt]["no_vul_data"] = no_vul_data
    print("vul_data size: %s, no_vul_data size: %s" % (len(vul_data), len(no_vul_data)))

    err_no_vul_data = [x for x in no_vul_data if "{'findings': []}" not in x["secllm_answer"]]
    correct_no_vul_data = [x for x in no_vul_data if "{'findings': []}" in x["secllm_answer"]]
    all_data[prompt]["err_no_vul_data"] = err_no_vul_data
    all_data[prompt]["correct_no_vul_data"] = correct_no_vul_data
    print("err_no_vul_data  size: %s, correct_no_vul_data size: %s, acc: %s" % (len(err_no_vul_data), len(correct_no_vul_data), len(correct_no_vul_data) / len(no_vul_data)))



    err_vul_data = [x for x in vul_data if x["kind"] not in x["secllm_answer"]]
    correct_vul_data = [x for x in vul_data if x["kind"] in x["secllm_answer"]]
    all_data[prompt]["err_vul_data"] = err_vul_data
    all_data[prompt]["correct_vul_data"] = correct_vul_data
    print("err_vul_data  size: %s, correct_vul_data size: %s, acc: %s" % (len(err_vul_data), len(correct_vul_data), len(correct_vul_data) / len(vul_data)))


    print("all cuttect size: %s, all err size: %s, acc: %s" % (len(correct_no_vul_data) + len(correct_vul_data), len(err_no_vul_data) + len(err_vul_data), (len(correct_no_vul_data) + len(correct_vul_data))/ len(prompt_data)))


