# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="create_answer_prompt", answer_name="create_answer_answer"):
    async with semaphore:
        url = "http://10.37.4.4:8080/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Qwen2-72B-Instruct",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": point[question_name],
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 250  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
raw_data = load_dataset("json", data_files="/home/<USER>/llm_dataset/slimorca-deduped-cleaned-corrected/re_label/re_label_answer_created.json")
raw_data = raw_data['train']
print("\nraw data size: %s" % len(raw_data))



def make_conver(point):
    assert len(point['conversations']) == 3


    system = point['conversations'][0]['value']
    user = point["created_answer"] + "\n- 请使用中文回答！\n- 回答请尽量详细且专业！"  # point['conversations'][1]['value']
    assistant = point['conversations'][2]['value']

    # use_prompt = prompt.replace("{Task}", user)
    conv_list = []
    conv_list.append({"role": "system", "content": system})
    conv_list.append({"role": "user", "content": user})
    return conv_list


raw_data = raw_data.map(lambda x: {"create_answer_prompt": make_conver(x)}, num_proc=35)


print("\n\nprompt over. prompt example:\n%s" % raw_data[0]["create_answer_prompt"])

print("\n\nmake list data")
data = []
for point in tqdm(raw_data):
    data.append(point)
    
data = data[:30000]

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/slimorca-deduped-cleaned-corrected/re_label/re_label_answer_created_ans_dpo_qwen_0_30000.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
