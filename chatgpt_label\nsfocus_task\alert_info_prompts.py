import json
import random
from tqdm import tqdm

key_map = {
    "msgtype": {
        "name": "msgtype",
        "description": "发送给ISOP的消息类型",
        "value_range": "13：入侵检测（吸星）\n14：入侵检测（zealot）\n17：DDos\n21：webshell\n31489：nti威胁情报\n63：SDK防病毒\n100：自定义情报\n101：自定义规则\n110：内网主机扫描\n130：翻墙行为告警\n131：违法网站告警\n132：违规应用告警",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "13": "入侵检测（吸星）",
            "14": "入侵检测（zealot）",
            "17": "DDos",
            "21": "webshell",
            "31489": "nti威胁情报",
            "63": "SDK防病毒",
            "100": "自定义情报",
            "101": "自定义规则",
            "110": "内网主机扫描",
            "130": "翻墙行为告警",
            "131": "违法网站告警",
            "132": "违规应用告警",
            "109": "钓鱼邮件告警",
            "3840": "waf"
        },
        "prompt_template": "这是安全日志的消息类型。",
        "trans_key": "msgtype"
    },
    "sip": {
        "name": "sip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "sport": {
        "name": "sport",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dip": {
        "name": "dip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dport": {
        "name": "dport",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "src_ip": {
        "name": "src_ip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "src_port": {
        "name": "src_port",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dst_ip": {
        "name": "dst_ip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dst_port": {
        "name": "dst_port",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "protocol": {
        "name": "protocol",
        "description": "协议",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "6": "HTTP",
            "17": "UDP"
        },
        "prompt_template": "这是告警事件的协议类型。",
        "trans_key": "protocol"
    },
    "direct": {
        "name": "direct",
        "description": "当前会话的方向",
        "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "内向外",
            "2": "外向内",
            "3": "内对内",
            "4": "其他"
        },
        "prompt_template": "这是告警事件的流量方向。",
        "trans_key": "direction"
    },
    "acted": {
        "name": "acted",
        "description": "实际所完成的动作",
        "type": "int",
        "prompt_template": "这是根据规则设备实际采取的动作。",
        "trans_key": "acted_action"
    },
    "alertlevel": {
        "name": "alertlevel",
        "description": "告警级别",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的告警级别。",
        "trans_key": "alertlevel"
    },
    "timestamp": {
        "name": "timestamp",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "stat_time": {
        "name": "stat_time",
        "description": "时间戳",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "rawlen": {
        "name": "rawlen",
        "description": "报文长度",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是报文的长度。",
        "trans_key": "payload_len"
    },
    "rawinfo": {
        "name": "rawinfo",
        "description": "原始报文信息",
        "value_range": "base64编码\n包含SDK防病毒日志中的md5信息",
        "type": "string",
        "prompt_template": "这是原始报文信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    "msg": {
        "name": "msg",
        "description": "存放对应该日志的描述",
        "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "ar": {
        "name": "ar",
        "description": "攻击是否成功",
        "value_range": "值为1(成功),2(失败),3(未知)",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "成功",
            "2": "失败",
            "3": "未知"
        },
        "prompt_template": "这是攻击结果，标识攻击是否成功。",
        "trans_key": "ar"
    },
    "q_body": {
        "name": "q_body",
        "description": "http请求消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http请求消息，这是一个Base64编码的字符串，我们可以对其解码，得到q_body_plain字段。",
        "trans_key": "q_body"
    },
    "r_body": {
        "name": "r_body",
        "description": "http回应消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http回应消息，这是一个Base64编码的字符串，我们可以对其解码，得到r_body_plain。",
        "trans_key": "r_body"
    },
    "domain": {
        "name": "domain",
        "description": "域名",
        "type": "string",
        "prompt_template": "这是域名信息。",
        "trans_key": "domain"
    },
    "event_type": {
        "name": "event_type",
        "description": "告警类型",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志的告警类型。",
        "trans_key": "event_type"
    },
    "alertinfo": {
        "name": "alertinfo",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "protocol_type": {
        "name": "protocol_type",
        "description": "协议类型",
        "value_range": "HTTP/HTTPS",
        "type": "string",
        "prompt_template": "这是协议类型。",
        "trans_key": "protocol_type"
    },
    "uri": {
        "name": "uri",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "block": {
        "name": "block",
        "description": "是否禁用ip",
        "value_range": "0：不启用\n1: 启用IP封禁",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "0": "不启用",
            "1": "启用IP封禁"
        },
        "prompt_template": "这是代表是否禁用ip。",
        "trans_key": "block_info"
    },
}


def clean_info(raw_info, format_info):
    new_raw_info = {}
    new_format_info = {}
    if isinstance(raw_info, dict):
        raw_info = [raw_info]
    for point in raw_info:
        for key, value in point.items():
            if key not in key_map or len(str(value).strip()) == 0:
                continue
            format_key_list = key_map[key]["trans_key"]
            if isinstance(format_key_list, str):
                format_key_list = [format_key_list]
            for format_key in format_key_list:
                if format_key in format_info and len(str(format_info[format_key]).strip()) > 0:
                    if isinstance(value, str) and len(value) > 150:
                        value = value[:150]
                    format_value = format_info[format_key]
                    if isinstance(format_value, str) and len(format_value) > 150:
                        format_value = format_value[:150]
                    new_raw_info[key] = value
                    new_format_info[format_key] = format_value
    return  new_raw_info, new_format_info


def make_log(raw_info, is_json=True, key_value_split="", params_split=""):
    if is_json:
        return json.dumps(raw_info, ensure_ascii=False)
    else:
        assert  len(key_value_split) > 0 and len(params_split) > 0
        if params_split == " ":
            param_list = [k + key_value_split + '"' + str(v) + '"' for k, v in raw_info.items()]
        else:
            param_list = [k + key_value_split + str(v) for k, v in raw_info.items()]
        return params_split.join(param_list)


def make_format_response(raw_info, format_info, key_value_split="", params_split="", pad_json=False):
    response = "这是一个安全告警日志，它记录了一个特定告警事件的详细信息。让我们逐个分析每个字段：\n"
    for key, value in raw_info.items():
        if isinstance(value, str) and len(value) > 100:
            value = value[:10] + "..."

        if len(key_value_split) == 0 and len(params_split) == 0:
            key_value_split = ": "

        if pad_json is True:
            param = key
        elif params_split == " ":
            param = key + key_value_split + '"' + str(value) + '"'
        else:
            param = key + key_value_split + str(value)

        param_key_map = key_map[key]
        param_desc = param_key_map['prompt_template']

        if param_key_map.get("is_enum", False) is True:
            new_value = param_key_map['enum_map'].get(str(value), "")
            if len(new_value.strip()) > 0:
                param_desc += "它是一个枚举值，根据绿盟的定义，它代表：%s。" % new_value

        new_key = param_key_map['trans_key']
        if not isinstance(new_key, list):
            new_key  = [new_key]
        if len(new_key) > 1 or new_key[0] != key:
            new_key = ['`%s`' % k for k in new_key]
            parse_list = [
                "它可以被解析为%s字段。",
                "它可以被规范化为%s字段。",
                "它可以被格式化为%s字段。",
                "它可以被结构化为%s字段。",
                "它可以被归一化为%s字段。"
            ]
            param_desc += random.choice(parse_list) % '和'.join(new_key)

        response += "- `{}`：{}\n".format(param, param_desc)

    if pad_json:
        parse_list2 = [
            "如下为解析后的json体：",
            "以下是规范化为json后的结果：",
            "格式化后的json体为：",
            "结构化的json是：",
            "归一化后的日志json是："
        ]
        new_format_info = {}
        for k, v in format_info.items():
            if isinstance(v, str) and len(v) > 100:
                v = v[:10] + "..."
            new_format_info[k] = v
        response += "\n" + random.choice(parse_list2) + "\n```\n" + json.dumps(new_format_info, ensure_ascii=False, indent=4) + "\n```\n"

    return response


def prompt_1(raw_info, format_info, pad_json=False):
    q_list = [
        "请分析一下这个告警日志中，这个事件的详细内容。日志：%s",
        "请给出这个告警日志对应的攻击事件的详细分析。日志：%s",
        "分析一下这个告警日志。%s",
        "详细分析一下这个日志。%s",
        "详细分析一下这个告警日志的内容。%s",
        "%s，给出这个这个日志的详细内容?",
        "%s，解析这个日志",
        "%s，详细分析这个告警日志。",
        "分析这个告警日志: %s",
        "解析这个告警日志的内容: %s"
    ]
    q_list_json = [
        "请分析一下这个告警日志中，这个事件的详细内容，并输出归一化后的json结果。日志：%s",
        "请给出这个告警日志对应的攻击事件的详细分析，并输出解析后的json结果。日志：%s",
        "分析一下这个告警日志，并输出规范化后的json结果。%s",
        "详细分析一下这个日志，并输出归一化后的json结果。%s",
        "详细分析一下这个告警日志的内容，并输出解析后的json结果。%s",
        "%s，给出这个这个日志的详细内容?并输出归一化后的json结果",
        "%s，解析这个日志，并输出归一化后的json结果",
        "%s，详细分析这个告警日志，并输出规范化后的json结果。",
        "分析这个告警日志，并输出解析后的json结果: %s",
        "解析这个告警日志的内容，并输出归一化后的json结果: %s"
    ]
    if pad_json:
        use_q_list = q_list_json
    else:
        use_q_list = q_list

    log_type_json = random.choice([True, False])
    key_value_split = ""
    params_split = ""
    if log_type_json != True:
        key_value_split = random.choice(["=", ":", "/"])
        params_split = random.choice(["&", ",", " ", "|"])

    question =  random.choice(use_q_list) % (make_log(raw_info, log_type_json, key_value_split, params_split))
    response = make_format_response(raw_info, format_info, key_value_split, params_split, pad_json=pad_json)

    prompts = {"question": question,
               "answer": response}
    return prompts


import os

data_dir1 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA"
data_list1 = os.listdir(data_dir1)

data_dir2 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA2\DATA"
data_list2 = os.listdir(data_dir2)


all_data = []

# data1
for data_name in data_list1:
    with open(data_dir1 + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            raw_info = json.loads(line['raw_data'].strip())
            raw_info, format_info = clean_info(raw_info, line)
            all_data.append({"raw_info": raw_info, "format_info": format_info})

print("data1 size: %s" %  len(all_data))

# data2
for data_name in data_list1:
    raw_info = None
    with open(data_dir1 + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            if raw_info is None:
                raw_info = line
            else:
                raw_info, format_info = clean_info(raw_info, line)
                all_data.append({"raw_info": raw_info, "format_info": format_info})
                raw_info = None

print("all data size: %s" %  len(all_data))


new_data = []
for point in tqdm(all_data):
    prompt = prompt_1(point["raw_info"], point["format_info"], pad_json=False)
    new_data.append(prompt)
    prompt = prompt_1(point["raw_info"], point["format_info"], pad_json=True)
    new_data.append(prompt)


print("use data size: %s" %  len(new_data))
with open('./alert_info_parse.json', 'w', encoding='utf-8') as fp:
    for line in new_data:
        line = json.dumps(line, ensure_ascii=False)
        fp.write(line + '\n')


