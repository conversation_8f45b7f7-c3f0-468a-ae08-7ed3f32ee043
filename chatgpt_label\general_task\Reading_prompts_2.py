# # 针对阅读理解任务
import json
import re

# ########## for SQuADData #########################
with open("SQuADData/train-v1.1.json", 'r', encoding='utf-8') as file:
    data = json.load(file)

def find_matching_string(sentence, string_list):
    for string in string_list:
        if string in sentence:
            return string
    return None

def Prompt1(context, qa):
    # pattern = r'Which NFL team|What color|What|Who|what season|Which team|What 2015 NFL team|Which Carolina Panthers player|How many|' \
    #           r'Where did|What city did|Where as|Which Carolina Panthers team member|Which study|whom|Where|What certificate|whom|' \
    #           r'What type of positions|what injury|who'

    string_list = ["What venue did", "Where did", "When did", "When was", "Where was", "What year did"]
    string_list1 = ["On what day was", "On what day does", "On what day were", "On what day was"]
    string_list2 = ["What city did", "In what city did"]
    string_list3 = ["How old was", "How many yards did", "How many sacks did", "How many picks did",
                    "What position does", "What did"]
    string_list4 = ["What was", "what day was", "How long was"]
    string_list5 = ["by which year", "by which year did"]
    string_list6 = ["In which year", "In what year did", "In what year was"]
    string_list7 = ["For how long did", "How long did", "How many days did"]
    string_list8 = ["Where is", "How high is", "What does", "What was"]
    string_list9 = ["Which NFL team", "What color", "Who", "Which company", "What famous snowbaorder", "What",
                    "how many", "what", "Who", "How many", "which one","How man"]
    string_list10 = ["Since what year did"]

    prompts = {"question": context + "\n" + qa["question"],
               "answer": (qa["answers"][0]["text"])}
    matched_string = find_matching_string(qa["question"], string_list)
    matched_string1 = find_matching_string(qa["question"], string_list1)
    matched_string2 = find_matching_string(qa["question"], string_list2)
    matched_string3 = find_matching_string(qa["question"], string_list3)
    matched_string4 = find_matching_string(qa["question"], string_list4)
    matched_string5 = find_matching_string(qa["question"], string_list5)
    matched_string6 = find_matching_string(qa["question"], string_list6)
    matched_string7 = find_matching_string(qa["question"], string_list7)
    matched_string8 = find_matching_string(qa["question"], string_list8)
    matched_string9 = find_matching_string(qa["question"], string_list9)
    matched_string10 = find_matching_string(qa["question"], string_list10)
    if matched_string:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string, " ", re.sub(r'[.?]', ' ', qa["question"])) + "at " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string1:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string1, " ", re.sub(r'[.?]', ' ', qa["question"])) + "on " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string2:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string2, " ", re.sub(r'[.?]', ' ', qa["question"])) + "in " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string3:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string3, " ", re.sub(r'[.?]', "", qa["question"])) + " " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string4:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string4, " ", re.sub(r'[.?]', " ", qa["question"])) + "was " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string5:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string5, " ", re.sub(r'[.?]', " ", qa["question"])) + "by " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string6:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string6, " ", re.sub(r'[.?]', " ", qa["question"])) + "in " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string7:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string7, " ", re.sub(r'[.?]', " ", qa["question"])) + "for " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string8:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string8, " ", re.sub(r'[.?]', " ", qa["question"])) + " " + \
                             qa["answers"][0]["text"] + "."
    elif matched_string9:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string9, re.sub(r'[.?]', " ", qa["answers"][0]["text"]),
                                    re.sub(r'[.?]', " ", qa["question"])) + "."
        return prompts
    elif matched_string10:
        prompts["answer"] = ""
        prompts["answer"] += re.sub(matched_string10, " ", re.sub(r'[.?]', " ", qa["question"])) + "since " + \
                             qa["answers"][0]["text"] + " " + "year" + "."
    return prompts


all_prompts = []
for item in data["data"]:
    for i, paragraph in enumerate(item["paragraphs"]):
        for j, qa in enumerate(paragraph["qas"]):
            prompt = Prompt1(paragraph["context"], qa)
            print(prompt)
            all_prompts.append(prompt)

with open("SQuADData_prompts_train.json", "w", encoding="utf-8") as f:
    json.dump(all_prompts, f, ensure_ascii=False, indent=4)


# ################ for CMRC2018  #############
# # 中文数据
with open("CMRC2018/cmrc2018_dev.json", 'r', encoding='utf-8') as file:
    data_1 = json.load(file)
'''
def Prompt2(item, qa):
    pattern = r'哪个车站|哪些地方|什么|谁|哪个机构|哪一征税系统|哪座山|哪些小湖|多少平方公里|哪里|哪个修道院|怎样的世界|有哪些说法|什么样的人' \
              r'|有什么特色|哪一年|哪一队|什么特点|什么地区|哪个原创机体|什么地方|什么语族|多少人|什么类型的动物|\b多|什么名字|几位|多少|' \
              r'哪里|哪个城市|哪家媒体|哪个地方|哪个国家|有什么特点|什么疾病|什么样的副作用|哪个国家|什么位置|什么时间|什么奖项|什么动物|什么颜色|' \
              r'有什么特点|哪个时代|哪个公司|哪一段时期|哪个国家|什么年代|什么样|多少岁|什么职位|哪些小项|什么赛事|多少名|哪些人|哪一部分|几|第几张|的主题是什么|' \
              r'哪个语族|有怎样的生活习性|多少组|哪座城市|哪些年|哪位好友|什么时期|多少面|多少天|几站|几分|哪个频道|哪一项运动|哪国|什么大学|哪一方|' \
              r'哪个学校|多少年|周围的景象是什么样的|什么时候|哪些政治主张|做了什么计划|的原因是什么|哪部原著|在什么情况下比较常见|哪些物质|' \
              r'在文化上有什么成就|分别在什么地方|规模如何|什么建筑用料|在外形上有什么特征|有哪些功效|哪些|来往什么地方的线路|哪两条线路|哪两条线路|几个|' \
              r'哪个出入口|何而得名|哪条线路|在什么地方|有什么组成|位于什么地方|是怎么设计的|这个名字怎么来的|呈什么走向|的名字怎么来的|哪些面值的货币|哪个单位制作的|' \
              r'主要是哪里人|成都道在香港有什么作用|哪个典故|哪些著名的建筑物|有什么地理价值|有什么外形特征|由什么材料制成|有什么使用价值|图案是怎么制作的|有什么食用价值|' \
              r'有什么地形特点|几个阶段|有什么意义|有什么不同|哪个单位|受什么影响|指什么|意味着什么|哪里的港口|哪部著作|的意图是什么|多久|哪部作品|哪部|哪里人|' \
              r'擅长什么|主要表达了什么|几只|\b什么性质的单位|什么派系|\哪四种语言|具有什么样的食用价值|怎么吃|什么原则|\b有哪些|如何|什么线路|哪几种|' \
              r'怎样|要怎么吃|什么水果|为什么叫挂绿|建立了什么战功|在哪些地方|哪些年限|哪两个地方|哪个|哪里的王朝|哪些行星|什么类型星|有什么指导意义|' \
              r'哪个剧组|说明了什么|什么活动|具有什么保护价值|为什么|主要包含哪些|什么宗教|是怎么说的|是怎么想的|是怎样的|哪个协议|什么手续|什么人|' \
              r'什么类型的游戏|是怎么玩的|有什么功能|哪三关|怎么样|什么书|执法怎么样|哪个组织|有什么药用价值|什么车型|哪条|开始怎么定位自身的|' \
              r'什么比赛|什么形式|多少处|第几届|为什么会来到战国时代|为何妻妾成群|有哪些东西|可能导致什么|哪一类|哪个学者|哪一科|喜欢在什么地方生活|' \
              r'是哪里到哪里|多少座|有什么政绩|什么性质的机场|多大|什么工作|有什么来历|有什么作用|什么短信|向宠有什么作为|哪次战役|有什么缺点|哪支球队|' \
              r'得到哪些好处|是怎么做的|什么季节|什么物质|哪个系|哪些博物馆|几球|什么美德|什么类型|什么学校|什么原因|哪些模式|哪个部落|哪一级|' \
              r'哪个海域|哪颗行星|多少倍|哪个电视台|什么剧|什么角色|多少位|哪些国家|什么电视台|哪个线路|哪些职业|什么著作|的生存习性是怎样的|哪个州|' \
              r'哪个政府|什么类型的网站|什么机构|什么气候|哪个香港艺人|什么规定|有什么特征|什么语言|什么奖|什么字|什么文件|什么朝代|什么时间什么地点|' \
              r'什么样的内容|哪个法国人|有着怎样的职责|哪两大体系|多少艘|什么篇章|哪部喜剧|哪|什么意思|哪个角度|什么类型的兵器|哪个比赛|' \
              r'什么战争|的什么方面|什么电视剧|什么文章|什么政治观点|哪部电视剧|什么项目|在国际赛事上取得了什么成就|什么小说|多少分钟|' \
              r'哪个案子|哪个世纪|到目前为止有着怎样的地位|谁的系统|几轮|哪个目|哪本经典著作|多少个班级|哪位著名作家|哪部美国电影|何时|哪些因素|' \
              r'几名|哪位苏联外长|哪项运动|第几部|哪位领导人|多少集|哪个党派|什么党派|哪位牧师|哪个帮派|什么帮派|哪位元老|多远|什么类型的恒星|' \
              r'怎样的结构|第几名|哪个政权|哪个省|会发生什么|什么溶液|有哪些要素|哪个大学|第多少届|什么队|多少个|什么荣誉|多长|哪位运动员|' \
              r'哪个县|哪一目哪一科|什么样的海域|哪个工厂|什么方式给药|导致什么后果|哪条公路|什么行业|什么科|几枚|几列|什么为食|多少米|' \
              r'哪个网站|哪层楼|哪一世达赖时期|多少分钟内|哪所小学|哪一部电影|哪一国|一个怎样的故事|谁与谁|什么事件|哪部小说|哪一部歌曲|哪一部小说|' \
              r'哪篇文章|哪些主要城市|哪两个|哪一部武侠小说|怎样回答|哪个海峡|哪些不同的地形构造|哪个完整的化石|哪些重大的遗迹|哪一大洋区|' \
              r'多少条|多少公分|哪一种音乐类型|哪一位前辈|谁对谁|哪个区|多少枚|什么食性|哪位剧作家|哪个角色|什么问题|哪句话|哪一个地层|' \
              r'哪些现在海狮的特征|什么研究|\b哪些俗名|多少公里|多少趟|哪一个消费贷款企业|哪段路|多少对|多少厘米|第几背鳍|多少公分以上|' \
              r'哪两个重要部分|哪些工具|哪些大气现象|哪些鱼类|多少小时|哪些大洋区|哪个部位|什么底质|什么拳|多少项|哪一地产|第几架|哪一个|哪个大洋区|' \
              r'什么形状|哪一部位|多少颗|哪种吃法|幼鱼和成鱼时的栖息地有什么不同|哪一频道|哪一条例|是怎样排列的|怎样分布的|哪一因素|哪一气体' \
              r'哪些症状|与其它蛇类有什么不同|哪一部|哪部作品的哪个角色|哪一教|多少间|哪些客席讲员|哪两个馆|哪一个脚趾|还涉及哪些议题|' \
              r'哪部书|干什么|什么方面|哪一带|什么村|哪一句俗语|哪篇散文|哪篇散文|什么观念|哪个因素|哪些患者|几个蛋|哪一公园|哪家选货店舖|' \
              r'哪一种|哪一宗教|哪档|第几对脚|哪所学校|哪一影片|哪一协定|哪个时期|哪一档公共电视台节目|哪部电影作品|哪一公园|怎么办|' \
              r'什么场所|什么场所|曾发生哪件最著名的事件|哪个皇宫|有哪些药用|多少米以上'

    pattern = "|".join(sorted(pattern.split('|'), key=len, reverse=True))
    prompts = {
        "question": "我这里有一段文字，我想知道：" + qa["query_text"] + ",文字:\n" + item["context_text"],
        "answer": ""
    }

    if type(qa["answers"][0]) == float:
        prompts["answer"] += re.sub(pattern, str(qa["answers"][0]), qa["query_text"].replace("？", ""))
    else:
        prompts["answer"] += re.sub(pattern, qa["answers"][0].replace("。", ""), re.sub(r'[.？]', '', qa["query_text"]))

    prompts["answer"] += "。"
    return prompts


all_prompts = []
for i, item in enumerate(data):
    for j, qa in enumerate(item["qas"]):
        print(i)
        prompt = Prompt2(item, qa)
        print(prompt)
        all_prompts.append(prompt)

with open("SQuADData_prompts_train.json", "w", encoding="utf-8") as f:
    json.dump(all_prompts, f, ensure_ascii=False, indent=4)
'''


