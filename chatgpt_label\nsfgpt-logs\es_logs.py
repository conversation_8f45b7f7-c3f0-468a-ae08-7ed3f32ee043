# -*- encoding: utf-8 -*-
# @ModuleName: es_logs
# @Author: zhanglong
# @Time: 2024/1/4 11:06


from elasticsearch import Elasticsearch
import json

def export_to_json(es, index_name, output_file):
    # 设置滚动查询
    scroll = '1m'
    size = 1000

    all_data = []

    # 初始搜索
    response = es.search(index=index_name, scroll=scroll, size=size, body={"query": {"match_all": {}}})
    scroll_id = response['_scroll_id']

    # 打开文件用于写入
    with open(output_file, 'w', encoding='utf-8') as json_file:
        while True:
            # 写入当前批次的数据到 JSON 文件
            for hit in response['hits']['hits']:
                line = json.dumps(hit['_source'], ensure_ascii=False)
                all_data.append(line)
                json_file.write(line + '\n')

            # 获取下一批数据
            response = es.scroll(scroll_id=scroll_id, scroll=scroll)

            # 结束条件
            if not response['hits']['hits']:
                break
    return all_data

if __name__ == "__main__":
    es = Elasticsearch(["http://10.67.15.11:9200"],
                  http_auth=("elastic", "owl@2019"),
                  timeout=30, max_retries=3, retry_on_timeout=True)

    # 要导出的索引名称
    index_name = 'secllm'

    # 输出 JSON 文件名称
    output_file = './secllm-es-log.json'

    # 执行导出
    all_data = export_to_json(es, index_name, output_file)

    print(f"Data from index '{index_name}' exported to '{output_file}' in JSON format.")
