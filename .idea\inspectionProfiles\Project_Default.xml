<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="153">
            <item index="0" class="java.lang.String" itemvalue="motor" />
            <item index="1" class="java.lang.String" itemvalue="tushare" />
            <item index="2" class="java.lang.String" itemvalue="bs4" />
            <item index="3" class="java.lang.String" itemvalue="pymongo" />
            <item index="4" class="java.lang.String" itemvalue="pytesseract" />
            <item index="5" class="java.lang.String" itemvalue="delegator.py" />
            <item index="6" class="java.lang.String" itemvalue="demjson" />
            <item index="7" class="java.lang.String" itemvalue="websocket-client" />
            <item index="8" class="java.lang.String" itemvalue="pyecharts_snapshot" />
            <item index="9" class="java.lang.String" itemvalue="gevent-websocket" />
            <item index="10" class="java.lang.String" itemvalue="async_timeout" />
            <item index="11" class="java.lang.String" itemvalue="zenlog" />
            <item index="12" class="java.lang.String" itemvalue="retrying" />
            <item index="13" class="java.lang.String" itemvalue="janus" />
            <item index="14" class="java.lang.String" itemvalue="apscheduler" />
            <item index="15" class="java.lang.String" itemvalue="pyecharts" />
            <item index="16" class="java.lang.String" itemvalue="pyconvert" />
            <item index="17" class="java.lang.String" itemvalue="pytdx" />
            <item index="18" class="java.lang.String" itemvalue="flask-moment" />
            <item index="19" class="java.lang.String" itemvalue="coverage" />
            <item index="20" class="java.lang.String" itemvalue="flask-migrate" />
            <item index="21" class="java.lang.String" itemvalue="werkzeug" />
            <item index="22" class="java.lang.String" itemvalue="alembic" />
            <item index="23" class="java.lang.String" itemvalue="six" />
            <item index="24" class="java.lang.String" itemvalue="pyflakes" />
            <item index="25" class="java.lang.String" itemvalue="flask-sqlalchemy" />
            <item index="26" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="27" class="java.lang.String" itemvalue="flask-ckeditor" />
            <item index="28" class="java.lang.String" itemvalue="pycodestyle" />
            <item index="29" class="java.lang.String" itemvalue="mako" />
            <item index="30" class="java.lang.String" itemvalue="python-editor" />
            <item index="31" class="java.lang.String" itemvalue="click" />
            <item index="32" class="java.lang.String" itemvalue="bootstrap-flask" />
            <item index="33" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="34" class="java.lang.String" itemvalue="flake8" />
            <item index="35" class="java.lang.String" itemvalue="jinja2" />
            <item index="36" class="java.lang.String" itemvalue="watchdog" />
            <item index="37" class="java.lang.String" itemvalue="faker" />
            <item index="38" class="java.lang.String" itemvalue="flask-wtf" />
            <item index="39" class="java.lang.String" itemvalue="wtforms" />
            <item index="40" class="java.lang.String" itemvalue="flask_httpauth" />
            <item index="41" class="java.lang.String" itemvalue="greenlet" />
            <item index="42" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="43" class="java.lang.String" itemvalue="Flask-WTF" />
            <item index="44" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="45" class="java.lang.String" itemvalue="Flask-CKEditor" />
            <item index="46" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="47" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="48" class="java.lang.String" itemvalue="Jinja2" />
            <item index="49" class="java.lang.String" itemvalue="Flask-Moment" />
            <item index="50" class="java.lang.String" itemvalue="WTForms" />
            <item index="51" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="52" class="java.lang.String" itemvalue="elasticsearch" />
            <item index="53" class="java.lang.String" itemvalue="Faker" />
            <item index="54" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="55" class="java.lang.String" itemvalue="zipp" />
            <item index="56" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="57" class="java.lang.String" itemvalue="elastic-transport" />
            <item index="58" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="59" class="java.lang.String" itemvalue="urllib3" />
            <item index="60" class="java.lang.String" itemvalue="Mako" />
            <item index="61" class="java.lang.String" itemvalue="Bootstrap-Flask" />
            <item index="62" class="java.lang.String" itemvalue="flask-cors-3.0.10" />
            <item index="63" class="java.lang.String" itemvalue="dataclasses" />
            <item index="64" class="java.lang.String" itemvalue="tqdm" />
            <item index="65" class="java.lang.String" itemvalue="transformers" />
            <item index="66" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="67" class="java.lang.String" itemvalue="torch" />
            <item index="68" class="java.lang.String" itemvalue="numpy" />
            <item index="69" class="java.lang.String" itemvalue="celery" />
            <item index="70" class="java.lang.String" itemvalue="faiss" />
            <item index="71" class="java.lang.String" itemvalue="tensorflow" />
            <item index="72" class="java.lang.String" itemvalue="gunicorn" />
            <item index="73" class="java.lang.String" itemvalue="xgboost" />
            <item index="74" class="java.lang.String" itemvalue="lightgbm" />
            <item index="75" class="java.lang.String" itemvalue="supervisor" />
            <item index="76" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="77" class="java.lang.String" itemvalue="tensorboard" />
            <item index="78" class="java.lang.String" itemvalue="keras" />
            <item index="79" class="java.lang.String" itemvalue="dnspython" />
            <item index="80" class="java.lang.String" itemvalue="python-docx" />
            <item index="81" class="java.lang.String" itemvalue="jieba" />
            <item index="82" class="java.lang.String" itemvalue="cffi" />
            <item index="83" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="84" class="java.lang.String" itemvalue="wordcloud" />
            <item index="85" class="java.lang.String" itemvalue="zope.interface" />
            <item index="86" class="java.lang.String" itemvalue="cycler" />
            <item index="87" class="java.lang.String" itemvalue="docxcompose" />
            <item index="88" class="java.lang.String" itemvalue="pybase62" />
            <item index="89" class="java.lang.String" itemvalue="pycparser" />
            <item index="90" class="java.lang.String" itemvalue="eventlet" />
            <item index="91" class="java.lang.String" itemvalue="docxtpl" />
            <item index="92" class="java.lang.String" itemvalue="matplotlib" />
            <item index="93" class="java.lang.String" itemvalue="lxml" />
            <item index="94" class="java.lang.String" itemvalue="gevent" />
            <item index="95" class="java.lang.String" itemvalue="pyparsing" />
            <item index="96" class="java.lang.String" itemvalue="zope.event" />
            <item index="97" class="java.lang.String" itemvalue="Pillow" />
            <item index="98" class="java.lang.String" itemvalue="spacy" />
            <item index="99" class="java.lang.String" itemvalue="en-core-web-sm" />
            <item index="100" class="java.lang.String" itemvalue="flask" />
            <item index="101" class="java.lang.String" itemvalue="tzlocal" />
            <item index="102" class="java.lang.String" itemvalue="Flask-APScheduler" />
            <item index="103" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="104" class="java.lang.String" itemvalue="tzdata" />
            <item index="105" class="java.lang.String" itemvalue="flask-whooshee" />
            <item index="106" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="107" class="java.lang.String" itemvalue="pytz" />
            <item index="108" class="java.lang.String" itemvalue="requests" />
            <item index="109" class="java.lang.String" itemvalue="APScheduler" />
            <item index="110" class="java.lang.String" itemvalue="idna" />
            <item index="111" class="java.lang.String" itemvalue="pydantic" />
            <item index="112" class="java.lang.String" itemvalue="PyYAML" />
            <item index="113" class="java.lang.String" itemvalue="asgiref" />
            <item index="114" class="java.lang.String" itemvalue="h11" />
            <item index="115" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="116" class="java.lang.String" itemvalue="httptools" />
            <item index="117" class="java.lang.String" itemvalue="fastapi" />
            <item index="118" class="java.lang.String" itemvalue="watchgod" />
            <item index="119" class="java.lang.String" itemvalue="starlette" />
            <item index="120" class="java.lang.String" itemvalue="aiomysql" />
            <item index="121" class="java.lang.String" itemvalue="uvicorn" />
            <item index="122" class="java.lang.String" itemvalue="websockets" />
            <item index="123" class="java.lang.String" itemvalue="uvloop" />
            <item index="124" class="java.lang.String" itemvalue="fastapi_utils" />
            <item index="125" class="java.lang.String" itemvalue="aiokafka" />
            <item index="126" class="java.lang.String" itemvalue="typer" />
            <item index="127" class="java.lang.String" itemvalue="python-jose" />
            <item index="128" class="java.lang.String" itemvalue="orjson" />
            <item index="129" class="java.lang.String" itemvalue="socketio" />
            <item index="130" class="java.lang.String" itemvalue="PyJWT" />
            <item index="131" class="java.lang.String" itemvalue="raven" />
            <item index="132" class="java.lang.String" itemvalue="Babel" />
            <item index="133" class="java.lang.String" itemvalue="aiofiles" />
            <item index="134" class="java.lang.String" itemvalue="sentry_sdk" />
            <item index="135" class="java.lang.String" itemvalue="toml" />
            <item index="136" class="java.lang.String" itemvalue="passlib" />
            <item index="137" class="java.lang.String" itemvalue="asttokens" />
            <item index="138" class="java.lang.String" itemvalue="async-timeout" />
            <item index="139" class="java.lang.String" itemvalue="backcall" />
            <item index="140" class="java.lang.String" itemvalue="amqp" />
            <item index="141" class="java.lang.String" itemvalue="atomicwrites" />
            <item index="142" class="java.lang.String" itemvalue="anyio" />
            <item index="143" class="java.lang.String" itemvalue="aiohttp" />
            <item index="144" class="java.lang.String" itemvalue="aiosignal" />
            <item index="145" class="java.lang.String" itemvalue="attrs" />
            <item index="146" class="java.lang.String" itemvalue="bcrypt" />
            <item index="147" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="148" class="java.lang.String" itemvalue="pandas" />
            <item index="149" class="java.lang.String" itemvalue="python-multipart" />
            <item index="150" class="java.lang.String" itemvalue="requests-async" />
            <item index="151" class="java.lang.String" itemvalue="python-cas" />
            <item index="152" class="java.lang.String" itemvalue="poetry" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="requests_async" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>