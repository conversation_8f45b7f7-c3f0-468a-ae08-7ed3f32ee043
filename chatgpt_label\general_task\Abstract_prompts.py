import random
from datasets import load_from_disk, load_dataset , Dataset

# data = [
#     {
#         "title": "Example Title 1",
#         "summary": "This is the summary of the first example.",
#         "date": "2020",
#         "url": "2023",
#         "article_content": "This is the content of the first example article."
#     },
#     {
#         "title": "Example Title 2",
#         "summary": "This is the summary of the second example.",
#         "date": "2020",
#         "url": "2023",
#         "article_content": "This is the content of the second example article."
#     },
#     {
#         "title": "Example Title 3",
#         "summary": "This is the summary of the third example.",
#         "date": "2020",
#         "url": "2023",
#         "article_content": "This is the content of the third example article."
#     }
# ]

def Prompt1(point):
    if point['article_content'] is None or point['summary'] is None or point['title'] is None:
        return None

    if len(point['article_content']) <= len(point['summary']):
        return None

    question_list = [
        "简单总结一下这段话：%s",
        "%s\n简单总结一下这段话",
        "%s\n总结一下这段话",
        "%s\n总结:",
        "总结：%s",
        "总结文章\n文章：%s",
        "%s\n总结文章：",
        "文章：%s\n文章总结：",

        "文章：%s\n摘要：",
        "文章：%s\n文章的摘要是：",
        "文章：%s\n输出文章的摘要：",
        "%s\n摘要：",
        "%s\n文章的摘要是：",
        "%s\n输出文章的摘要：",

        "摘要：%s\n",
        "对以下文章作出摘要：%s\n",
        "对给出的文章摘要：%s\n",
        "输出摘要：%s\n",

        "文章：%s\n文章摘要：",
        "文章：%s\n这篇文章讲了什么？",
        "%s\n这段话讲了什么？",
    ]
    question = random.choice(question_list) % point["article_content"]
    answer = point["summary"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


def Prompt2(point):
    if point['article_content'] is None or point['summary'] is None or point['title'] is None:
        return None

    if len(point['article_content']) <= len(point['summary']):
        return None
    question_list = [
        "给这段话取一个合适的标题：%s",
        "%s\n给这段话取一个合适的标题",
        "%s\n取一个标题",
        "%s\n标题:",
        "取一个标题：%s",
        "取标题\n文章：%s",
        "文章：%s\n取一个标题：",

        "文章：%s\n标题：",
        "文章：%s\n文章的标题是：",
        "文章：%s\n输出文章的标题：",
        "%s\n文章的标题是：",
        "%s\n输出文章的标题：",

        "生成标题：%s\n",
        "对以下文章生成标题：%s\n",
        "对给出的文章生成标题：%s\n",
        "输出标题：%s\n",

        "文章：%s\n文章标题：",
    ]
    content = point[random.choice(["article_content", "summary"])]
    question = random.choice(question_list) % content
    answer = point["title"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


def Prompt3(point):
    if point['article_content'] is None or point['summary'] is None or point['title'] is None:
        return None

    if len(point['article_content']) <= len(point['summary']):
        return None

    question_list = [
        "以%s为题写一篇文章",
        "以%s为中心，撰写一篇文章",
        "用%s为主题撰写一篇作文",
        "围绕%s写一篇文章",
        "用%s为标题创作一篇文章",
    ]

    question = random.choice(question_list) % point["title"]
    answer = point["article_content"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


def Prompt4(point):
    if point['article_content'] is None or point['summary'] is None or point['title'] is None:
        return None

    if len(point['article_content']) <= len(point['summary']):
        return None

    question_list = [
        "基于这个标题的要点撰写摘要:%s",
        "依据标题%s，总结要点并写摘要",
        "请基于这个标题写一段摘要:%s",
        "请根据标题%s提供一段简短的总结",
        "请依据这个标题编写一段简要概述:%s",
        "请据标题%s撰写一段摘要",
        "请以这个标题为基础，撰写一段摘要内容:%s",
        "请按照这个标题的要求，写一段摘要内容:%s"
    ]

    question = random.choice(question_list) % point["title"]
    answer = point["summary"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


def Prompt5(point):
    if point['article_content'] is None or point['summary'] is None or point['title'] is None:
        return None

    if len(point['article_content']) <= len(point['summary']):
        return None

    question_list = [
        "请以%s为摘要为基础撰写一篇文章",
        "请根据这段摘录写一篇文章:%s",
        "请以这个摘要为蓝本写一篇文章:%s",
        "请参考%s这个摘要来写一篇文章",
        "基于这个摘要:%s，请就此写一篇文章",
        "请根据摘要内容，完成一篇文章的写作,摘要:%s",
        "根据这段摘要的内容，写一篇文章，摘要：%s"
    ]

    question = random.choice(question_list) % point["summary"]
    answer = point["article_content"]

    prompt = {"question": question,
              "answer": answer}
    return prompt


data_path = "/home/<USER>/llm_dataset/curation-corpus"

data = load_dataset("parquet", data_dir=data_path)
# DatasetDict({
#     train: Dataset({
#         features: ['title', 'summary', 'url', 'date', 'article_content'],
#         num_rows: 30455
#     })
# })


data = data['train']

all_data = []
prompt_list = [Prompt1, Prompt2, Prompt3, Prompt4, Prompt5]
for point in data:
    prompt_func = random.choice(prompt_list)
    prompt = prompt_func(point)
    if prompt is not None:
        all_data.append([{"from": "human", "value": prompt['question']},
                          {"from": "assistant", "value": prompt['answer']}])

all_data = [{"conversation": x} for x in all_data]  # 28951




