# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





prompt = """I will give you a task described in English. You need to refer to this task to create a task described in Chinese.
Note that the created Chinese Task are used to test the ability of AI models. Please do not simply translate the English Task into Chinese.

- Please ensure that the created tasks still have a high level of complexity.
- Please ensure that the created task has the same task type as the original task.
- Please ensure that the created task still meets the additional requirements of the original task for the results (such as output format, multiple languages).
- For multilingual related task (such as translation, analysis of other languages, etc.), please do not modify the language type and use the same language, ensure that the generated task can still test the model's multilingual ability.

Just return the Created Task, please do not output any other instructions or descriptions.

Original Task: 
{Question}

Created Task: 
"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="created_prompt", answer_name="created_answer"):
    async with semaphore:
        url = "http://*********:50002/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
raw_data = load_dataset("parquet", data_files="/home/<USER>/llm_dataset/LogiCoT/parquet/0000.parquet")
raw_data = raw_data['train']
print("\nraw data size: %s" % len(raw_data))


data = []
for i, point in tqdm(enumerate(raw_data)):
    point["created_prompt"] = prompt.replace("{Question}", point["instruction"] + "\n\n" + point["input"])
    data.append(point)


print("\n\nprompt over. prompt example:\n%s" % data[0]["created_prompt"])

data = data[:100000]

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/LogiCoT/re_label_answer_0_100000_created.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
