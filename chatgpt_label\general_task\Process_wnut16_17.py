import json

def bio_to_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    sentences = []
    current_sentence = {'sentence': '', 'entity': []}
    current_entity = None

    for line in lines:
        line = line.strip()
        if not line:
            if current_entity:
                current_sentence['entity'].append(current_entity)
            if current_sentence['sentence']:
                sentences.append(current_sentence)
            current_sentence = {'sentence': '', 'entity': []}
            current_entity = None
        else:
            parts = line.split('\t')
            if len(parts) == 2:
                word, tag = parts[0], parts[1]
                if current_sentence['sentence']:
                    current_sentence['sentence'] += ' ' + word
                else:
                    current_sentence['sentence'] = word

                if tag != 'O':
                    entity_type = tag.split('-')[1]
                    if current_entity and entity_type == current_entity['type']:
                        current_entity['token'] += ' ' + word
                        current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                    else:
                        if current_entity:
                            current_sentence['entity'].append(current_entity)
                        current_entity = {'token': word, 'type': entity_type, 'start': len(current_sentence['sentence'].split()) - 1, 'end': len(current_sentence['sentence'].split()) - 1}
                else:
                    if current_entity:
                        current_sentence['entity'].append(current_entity)
                        current_entity = None

    if current_entity:
        current_sentence['entity'].append(current_entity)
    if current_sentence['sentence']:
        sentences.append(current_sentence)

    json_data = json.dumps(sentences, indent=4, ensure_ascii=False)
    return json_data


bio_file_path = "wnut17命名实体识别数据集/dev.txt"
json_data = bio_to_json(bio_file_path)
with open("wnut17命名实体识别数据集/dev.json", 'w',encoding='utf-8') as file:
    file.write(json_data)
