# -*- encoding: utf-8 -*-
# @ModuleName: label_event.py
# @Author: zhang<PERSON>
# @Time: 2023/6/21 14:55


import requests
import pandas as pd
from tqdm import tqdm
import json


template_query = """
1、给出请求体对应的事件名称和事件类型
2、请一步步分析给出的请求体的内容、进行的行为
3、一步步分析请求体具体的攻击类型和使用的漏洞
4、提取请求体中的payload，分析payload的内容
5、一步步的分析判断攻击是否成功，并写出详细的推理过程。

请求体;
{请求体}

响应体：
{响应体}

请一步步的分析，并写出详细的推理过程。
"""


def openai_query(messages, model="gpt-3.5-turbo-16k", temperature: float = 0, top_p: float = 1,
                       presence_penalty: float = 0.0, frequency_penalty: float = 0.0):
    url = "http://gpt1.attackgraph.com:16180/openai"
    body = {
        "model": model,
        "temperature": temperature,
        "top_p": top_p,
        "messages": messages,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty
    }

    try:
        # print("openai chatgpt query!")
        response = requests.post(url, json=body)
        # print("openai chatgpt response status: %s - text: %s" % (response.status_code, response.text))
        if response.status_code == 200:
            res = response.json()
            return res
        else:
            print("openai chatgpt api-- Error: %s" % response.json())
            return None
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        return None


def chatgpt_event_label(point):
    try:
        msg = template_query.replace("{请求体}", point["request"]).replace("{响应体}", point["response"])
        messages = [
            {"role": "system", "content": "你是一名网络安全专家，需要根据IDS安全告警载荷，对其所指示的网络活动进行分析。"},
            {"role": "user", "content": msg}]

        response = openai_query(messages, frequency_penalty=0.1, temperature=0.8)
        return response["choices"][0]["message"]['content']
    except Exception as ex:
        print("chatgpt event label error: %s" % str(ex))
        return ""


path = r'D:\work\GPT\chatgpt_label/alarms_labeled_splited-20230621.xlsx'
threat_data = pd.read_excel(path, sheet_name=None, dtype=str)
threat_data = threat_data["Sheet1"].fillna("")
print("threat data size: %s" % threat_data.shape[0])
print("data columns: ")
for col in threat_data.columns:
    print("%s: %s" % (col, threat_data[col].values[0]))


all_data = []

for index, row in tqdm(threat_data.iterrows()):
    row = row.to_dict()
    answer = chatgpt_event_label(row['model_input'])
    row['model_output'] = answer
    all_data.append(row)


with open('./alarms_labeled_output-20230621-2.json', 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

use_data = [x for x in all_data if len(x['model_output']) > 0]
print("success data size: %s" %  len(use_data))
print("fail data size: %s" %  (len(all_data) - len(use_data)))
print("Done")
