# -*- coding: utf-8 -*-

import json
import requests_async
import requests
from fastapi import FastAPI, Request
from pydantic import BaseModel
from typing import Optional, List, Union
import enum
from typing import Optional

from fastapi.responses import ORJSONResponse
from starlette.requests import Request
import uvicorn

import logging
from logging.handlers import RotatingFile<PERSON>and<PERSON>, TimedRotatingFileHandler
from sse_starlette.sse import ServerSentEvent, EventSourceResponse



logger = logging.getLogger()
logger.setLevel(level=logging.DEBUG)
formatter = logging.Formatter(fmt='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                              datefmt='%m/%d/%Y %H:%M:%S')


# 日志流输出
console = logging.StreamHandler()
console.setLevel(logging.INFO)
console.setFormatter(formatter)
logger.addHandler(console)


# app = Flask('openai')

app = FastAPI()
app.state.api_keys = [
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    #"Bearer ***************************************************", # <EMAIL>
    # "Bearer ***************************************************", # <EMAIL>
    # "Bearer 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", # <EMAIL>
    # "Bearer ***************************************************", # <EMAIL>
    # "Bearer ***************************************************", # <EMAIL>
    # "Bearer ***************************************************", # <EMAIL>
    # "Bearer ***************************************************", # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    # "Bearer ***************************************************",  # <EMAIL>
    "Bearer ***************************************************",  # <EMAIL>
    "Bearer ***************************************************",  # <EMAIL>
    "Bearer ***************************************************",  # <EMAIL>
    "Bearer ***************************************************",  # <EMAIL>
    "Bearer ***************************************************",  # <EMAIL>


]


app.state.logger = logger


class HttpCodeEnum(enum.Enum):
    OK = 200
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    SERVER_ERROR = 500


class BizCodeEnum(enum.Enum):
    # 业务状态码
    OK = "2000"
    FAIL = "5000"
    NOT_EXISTS = "4004"


class ApiException(Exception):
    status_code = HttpCodeEnum.BAD_REQUEST.value
    biz_code = BizCodeEnum.FAIL
    code: Optional[int]
    detail: Optional[str] = None

    def __init__(self, detail, status_code=None, biz_code=None, errors=None):
        self.status_code = status_code or self.status_code
        self.code = self.status_code or self.code
        self.biz_code = biz_code or self.biz_code
        self.detail = detail or self.detail
        self.errors = errors or []

    def to_result(self):
        rv = {"message": self.detail}
        if self.code:
            rv["code"] = self.code
        if self.biz_code:
            rv["biz_code"] = self.biz_code
        if self.errors:
            rv["errors"] = self.errors
        return ORJSONResponse(rv, status_code=self.status_code)


async def request_stream(url, header, params):
    s = requests.session()
    s.keep_alive = False
    response = s.post(url, headers=header, json=params, stream=True)
    for line in response.iter_lines():
        line = line.decode("utf-8")
        if line[:4] == 'data':
            line = line[5:]
            line = line.strip()
            yield line
        else:
            pass


@app.post('/openai')
async def openai_completions(params: Request):
    try:
        params = await params.json()
        app.state.logger.info("openai query: %s" % params)
        url = "https://api.openai.com/v1/chat/completions"
        if "url" in params:
            url =  params['url']
            params.pop('url')
        app.state.api_keys.append(app.state.api_keys.pop(0))
        header = {
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9,hi;q=0.8',
            'Authorization': app.state.api_keys[0],
            'Connection': 'close',  # 'close', #'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://platform.openai.com',
            'Referer': 'https://platform.openai.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'}
        app.state.logger.info("use key: %s" % header["Authorization"])
        s = requests.session()
        s.keep_alive = False

        stream = params.get("stream", False)
        if stream:
            generate = request_stream(url, header, params)
            return EventSourceResponse(generate, media_type="text/event-stream")
        else:
            response = s.post(url, headers=header, json=params)
            out = response.json()
            app.state.logger.info("openai response: %s" % out)
            return out
    except Exception as ex:
        app.state.logger.info("open ai error: %s" % ex)
        raise ApiException(str(ex), 500)


@app.post('/test')
async def test():
    return "ok"


uvicorn.run(app, host="0.0.0.0", port=16180, reload=False, log_level="debug")

