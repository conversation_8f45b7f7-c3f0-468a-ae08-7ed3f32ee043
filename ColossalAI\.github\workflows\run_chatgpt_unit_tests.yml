name: Run ChatGPT unit tests

on:
  pull_request:
    types: [synchronize, opened, reopened]
    paths:
      - 'applications/Chat/coati/**'
      - 'applications/Chat/requirements.txt'
      - 'applications/Chat/setup.py'
      - 'applications/Chat/requirements-test.txt'
      - 'applications/Chat/tests/**'
      - 'applications/Chat/pytest.ini'

jobs:
  tests:
    name: Run ChatGPT unit tests
    if: |
      github.event.pull_request.draft == false &&
      github.base_ref == 'main' &&
      github.event.pull_request.base.repo.full_name == 'hpcaitech/ColossalAI'
    runs-on: [self-hosted, gpu]
    container:
      image: hpcaitech/pytorch-cuda:1.12.0-11.3.0
      options: --gpus all --rm -v /data/scratch/chatgpt:/data/scratch/chatgpt
    timeout-minutes: 30
    defaults:
      run:
        shell: bash
    steps:
      - name: Checkout ColossalAI
        uses: actions/checkout@v2

      - name: Install ChatGPT
        run: |
          cd applications/Chat
          pip install -v .
          pip install -r requirements-test.txt

      - name: Execute Unit Testing
        run: |
          cd applications/Chat
          rm -rf ~/.cache/colossalai
          pytest tests/
        env:
          NCCL_SHM_DISABLE: 1
          MAX_JOBS: 8
