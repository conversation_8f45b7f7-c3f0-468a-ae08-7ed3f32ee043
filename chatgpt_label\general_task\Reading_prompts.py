# 针对阅读理解任务
import json
with open("CMRC2018/cmrc2018_train.json", 'r', encoding='utf-8') as file:
    data = json.load(file)

########## for SQuADData #########################3
def Prompt1(context, qa):
    prompts = {"question": "Here is a passage from which you can tell me:" + qa[
        "question"] + ",and the passage is as follows:\n" + context,
               "answer": ("Based on what starts with " + context[
                   qa["answers"][0]["answer_start"]] + "the paragraph, the answer is:" + qa["answers"][0]["text"])}
    return prompts

for item in data["data"]:
    for i, paragraph in enumerate(item["paragraphs"]):
        for j, qa in enumerate(paragraph["qas"]):
            prompt = Prompt1(paragraph["context"], qa)
            print(prompt)


################ for CMRC2018  #############
def Prompt2(item, qa):
    prompts = {"question": "我这里有一段文字，我想知道：" + qa["query_text"] + ",文字:\n" + item["context_text"],
               "answer": "好的，经过分析，这个问答的答案是:"}
    if type(qa["answers"][0]) == float:
        prompts["answer"] += str(qa["answers"][0])
    else:
        prompts["answer"] += qa["answers"][0]
    return prompts


for i, item in enumerate(data):
    print(i)
    for j, qa in enumerate(item["qas"]):
        prompt = Prompt2(item, qa)
        print(prompt)
