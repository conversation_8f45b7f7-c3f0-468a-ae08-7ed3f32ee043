name: Build Documentation On Schedule & After Release

on:
  workflow_dispatch:
  schedule:
    - cron: "0 12 * * *" # build doc every day at 8pm Singapore time (12pm UTC time)
  release:
    types: [published]

jobs:
  build-doc:
    name: Trigger Documentation Build Workflow
    if: github.repository == 'hpcaitech/ColossalAI'
    runs-on: ubuntu-latest
    steps:
      - name: trigger workflow in ColossalAI-Documentation
        run: |
          curl \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${GH_TOKEN}"\
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/hpcaitech/ColossalAI-Documentation/actions/workflows/deploy.yml/dispatches \
            -d '{"ref":"main"}'
        env:
          GH_TOKEN: ${{secrets.DOC_REPO_TOKEN}}
