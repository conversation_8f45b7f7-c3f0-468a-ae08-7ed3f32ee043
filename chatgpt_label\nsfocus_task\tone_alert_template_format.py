import json
import random
from tqdm import tqdm
import os

block = [{
    "事件名称": "蠕虫病毒BlueHero_FromVM传播",
    "载荷": "HTTP/1.1 200 OK \nDate: Tue, 17 Dec 2019 03:08:30 GMT \nServer: Apache/2.4.18 (Ubuntu) \nVary: Accept-Encoding \nContent-Length: 143 \nConnection: close \nContent-Type: text/html; charset=UTF-8 \n \n<html>\n<script async=\"async\" type=\"text/java/script\" src=\"www.testiijdj.com/tpl/https.js\"></script>\n<body>\n<h1>TEST</h1>\n</body>\n</html>\n",
    "响应体": "GET /xinge/ttt.php HTTP/1.1\r\nHost: *************\r\nUpgrade-Insecure-Requests: 1\r\nUser-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36\r\nAccept: text/html,application/xhtml xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: PHPSESSID=doto8omr4v0jdcfmsb1vmos5a5\r\nConnection: close\r\n\r\n",
    "请求体": "HTTP/1.1 200 OK\r\nDate: Tue, 17 Dec 2019 03:08:30 GMT\r\nServer: Apache/2.4.18 (Ubuntu)\r\nVary: Accept-Encoding\r\nContent-Length: 143\r\nConnection: close\r\nContent-Type: text/html; charset=UTF-8\r\n\r\n<html>\n<script async=\"async\" type=\"text/java/script\" src=\"www.testiijdj.com/tpl/https.js\"></script>\n<body>\n<h1>TEST</h1>\n</body>\n</html>\n",
    "研判结果": "真实攻击",
    "攻击结果": "攻击成功",
    "研判结果校正": "准确",
    "处置结果": "应急",
    "事件类型": "蠕虫病毒"
}]


def prompt_1(point):
    question_list = [f"这里有一个请求体: {point['请求体']}，这个请求体属于一次什么攻击事件?",
                     f"请分析一下这个请求体中，这个事件属于什么类型的威胁？请求体：{point['请求体']}",
                     f"请给出这个请求体对应的攻击事件的处置建议。请求体：{point['请求体']}",
                     f"分析这个请求体对应的事件类型与攻击结果，分析造成的影响，并给出处置建议。请求体：{point['请求体']}",
                     f"分析一下这个请求体。{point['请求体']}",
                     f"这个请求体代表了什么事件？攻击者是否攻击成功？{point['请求体']}",

                     f"{point['请求体']}，这个请求体属于一次什么攻击事件?",
                     f"请求体：{point['请求体']}，分析一下这个请求体属于什么类型的威胁？",
                     f"请求体：{point['请求体']}，这个请求体是什么类型的攻击？",

                     f"{point['请求体']}，请给出这个请求体对应的攻击事件的处置建议。",
                     f"{point['请求体']}，分析这个请求体对应的事件类型与攻击结果，分析造成的影响，并给出处置建议。",
                     f"{point['请求体']}，分析一下这个请求体。",
                     f"{point['请求体']}，这个请求体代表了什么事件？攻击者是否攻击成功？"
                     ]
    question = random.choice(question_list)
    answer = '这个请求体是一次'

    if point.get('研判结果', '') == "真实攻击":
        if len(point.get('攻击结果', '')) > 0:
            answer += f"攻击者{point['攻击结果']}的"

        if len(point.get('事件名称', '')) > 0:
            answer += f"{point['事件名称']}"

        answer += f"网络攻击事件。\n经过分析，看起来这是一次"

        if len(point.get('关键词', '')) > 0:
            answer += f"与{point['关键词']}有关的"

        if len(point.get('事件类型', '')) > 0:
            answer += f"{point['事件类型']}事件。\n"

        if len(point.get('影响', '')) > 0:
            answer += f"此次攻击可能造成如下后果：{point['影响']}\n"

        if len(point.get('处置建议', '')) > 0:
            answer += f"针对此次事件，我们有如下处置建议：{point['处置建议']}\n"

    else:
        if len(point.get('事件名称', '正常的网络事件')) > 0:
            answer += f"{point['事件名称']}"

        answer += "。\n"

        if len(point.get('关键词', '')) > 0:
            answer += f"这个网络事件看起来与{point['关键词']}有关。"

        if len(point.get('事件别名', '')) > 0:
            answer += f"此次事件可能会被误报为{point['事件别名']}攻击事件。\n"

        if len(point.get('处置建议', '')) > 0:
            answer += f"针对此次事件，我们有如下处置建议：{point['处置建议']}"

    prompts = {"question": question,
               "answer": answer}
    return prompts


def prompt_2(point):
    question_list = [f"这里有一个请求体: {point['请求体']}，这个请求体属于一次什么攻击事件?",
                     f"请分析一下这个请求体中，这个事件属于什么类型的威胁？请求体：{point['请求体']}",
                     f"请给出这个请求体对应的攻击事件的处置建议。请求体：{point['请求体']}",
                     f"分析这个请求体对应的事件类型与攻击结果，分析造成的影响，并给出处置建议。请求体：{point['请求体']}",
                     f"分析一下这个请求体。{point['请求体']}",
                     f"这个请求体代表了什么事件？攻击者是否攻击成功？{point['请求体']}",

                     f"{point['请求体']}，这个请求体属于一次什么攻击事件?",
                     f"请求体：{point['请求体']}，分析一下这个请求体属于什么类型的威胁？",
                     f"请求体：{point['请求体']}，这个请求体是什么类型的攻击？",

                     f"{point['请求体']}，请给出这个请求体对应的攻击事件的处置建议。",
                     f"{point['请求体']}，分析这个请求体对应的事件类型与攻击结果，分析造成的影响，并给出处置建议。",
                     f"{point['请求体']}，分析一下这个请求体。",
                     f"{point['请求体']}，这个请求体代表了什么事件？攻击者是否攻击成功？"
                     ]
    question = random.choice(question_list)
    answer = '看起来，这个请求体似乎'

    if point.get('研判结果', '') == "真实攻击":
        if len(point.get('关键词', '')) > 0:
            answer += f"与{point['关键词']}相关。"

        if len(point.get('事件名称', '')) > 0:
            answer += f"是一次{point['事件名称']}网络攻击事件。"

        if len(point.get('事件类型', '')) > 0:
            answer += f"属于{point['事件类型']}类型的攻击事件。\n"

        if len(point.get('攻击结果', '')) > 0:
            answer += f"看上去，攻击者可能{point['攻击结果']}了。"

        if len(point.get('影响', '')) > 0:
            answer += f"此次攻击可能造成如下后果：{point['影响']}\n"

        if len(point.get('处置建议', '')) > 0:
            answer += f"针对此次事件，我们有如下处置建议：{point['处置建议']}\n"

    else:
        if len(point.get('关键词', '')) > 0:
            answer += f"与{point['关键词']}有关。"
        else:
            answer = ""

        if len(point.get('事件别名', '')) > 0:
            answer += f"这个事件经常可能会被误报为{point['事件别名']}攻击事件。\n"

        if len(point.get('事件名称', '正常的网络事件')) > 0:
            answer += f"实际上，这个请求体是一次{point['事件名称']}。"

        if len(point.get('处置建议', '')) > 0:
            answer += f"针对此次事件，我们有如下处置建议：{point['处置建议']}"

    prompts = {"question": question,
               "answer": answer}
    return prompts


def prompt_3(point):
    question_list = [f"这里有一个请求体: {point['请求体']}，这个请求体属于一次什么攻击事件?结果以json的格式给出。",
                     f"请分析一下这个请求体中，这个事件属于什么类型的威胁？结果以json的格式给出。请求体：{point['请求体']}",
                     f"请给出这个请求体对应的攻击事件的处置建议，结果以json的格式给出。请求体：{point['请求体']}",
                     f"分析这个请求体对应的事件类型与攻击结果，分析造成的影响，并给出处置建议。结果以json的格式给出。请求体：{point['请求体']}",
                     f"分析一下这个请求体，结果以json的格式给出。{point['请求体']}",
                     f"这个请求体代表了什么事件？攻击者是否攻击成功？结果以json的格式给出。{point['请求体']}"]
    question = random.choice(question_list)

    json_point = {
        "事件名称": point['事件名称'],
        "事件类型": point["事件类型"],
        "攻击结果": point.get("攻击结果", ""),
    }
    if  len(point.get('关键词', '')) > 0:
        json_point["关键词"] = point['关键词']
    if len(point.get('影响', '')) > 0:
        json_point["影响"] = point['影响']
    if len(point.get('处置建议', '')) > 0:
        json_point["处置建议"] = point['处置建议']
    answer = "```\n"
    answer += json.dumps(json_point, ensure_ascii=False, indent=4)
    answer += "\n```\n"
    prompts = {"question": question,
               "answer": answer}
    return prompts



all_data = []

# data_dir = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-06\T1-事件_0630_json"
data_dir = "/home/<USER>/llm_dataset/nsfocus/md/T1-事件_0630_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            q = line['响应体']
            a = line['请求体']
            line['请求体'] = q
            line['响应体'] = a
            all_data.append(line)


data_dir = "/home/<USER>/llm_dataset/nsfocus/md/t1-威胁事件_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            q = line['响应体']
            a = line['请求体']
            line['请求体'] = q
            line['响应体'] = a
            all_data.append(line)


# data_dir = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-06\T1-事件_0616_json"
data_dir = "/home/<USER>/llm_dataset/nsfocus/md/T1-事件_0616_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            all_data.append(line)

print("all data size: %s" %  len(all_data))


new_data = []
for point in tqdm(all_data):
    for key, value in point.items():
        if value is None:
            point[key] = ""

    q =  point.get('请求体', "")
    if len(q.strip()) <= 10:
        continue
    prompt_list = [prompt_1, prompt_2, prompt_3]
    use_prompt = random.choice(prompt_list)
    prompt = use_prompt(point)
    if prompt is not None:
        new_data.append(prompt)


print("use data size: %s" %  len(new_data))
with open('/home/<USER>/llm_dataset/T1_alert_prompt_data3.json', 'w', encoding='utf-8') as fp:
    for line in new_data:
        line = json.dumps(line, ensure_ascii=False)
        fp.write(line + '\n')
