# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import os
import yaml





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Athene-V2-Chat", #"Mistral-Large-Instruct-2407",  #"secllm-v2",  #"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
# 读取/home/<USER>/nuclei-POC下全部的yaml配置文件

def read_poc_files(directory):
    poc_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(".yaml"):
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    poc_content = f.read()
                    if "id:" in poc_content and "name:" in poc_content and "workflows:" not in poc_content:
                        poc_content = poc_content.split("\n")
                        poc_content = [line for line in poc_content if "# digest" not in line]
                        poc_content = "\n".join(poc_content)
                        for i in range(3):
                            poc_files.append({
                                "poc_content": poc_content,
                                "poc_file": file_path
                            })
    return poc_files

poc_directory = "/home/<USER>/nuclei-POC"
poc_data = read_poc_files(poc_directory)

print(f"读取到 {len(poc_data)} 个POC配置文件")

sys_prompt_gen_vuln_desc = """
这是一个nuclei中漏洞检测的POC配置文件。请帮我生成一个漏洞描述。

！注意，漏洞描述务必详细，需要包括详细的漏洞分析、漏洞的实现原理、利用思路、攻击链路、漏洞存在性验证思路说明。
！注意，不要直接给出具体的POC文件内容，但是要确保能根据该漏洞描述生成对应的POC配置文件。

{poc_content}
"""

for poc in poc_data:
    poc_content = poc["poc_content"]
    sys_prompt = sys_prompt_gen_vuln_desc.format(poc_content=poc_content)
    poc["prompt"] = sys_prompt

print("\n\ndata size: %s" %  len(poc_data))
print("\ndata example: \n\n%s" % poc_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(poc_data))

print("Done")


with open("/home/<USER>/nuclei-POC/relabel_poc_vuln_desc.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)



## 生成POC

with open("/home/<USER>/nuclei-POC/relabel_poc_vuln_desc.json", 'r', encoding='utf-8') as fp:
    poc_data = json.load(fp)

poc_data = [x for x in poc_data if x["answer"] is not None and len(x["answer"]) > 10]

for poc in poc_data:
    poc["vuln_desc_prompt"] = poc["prompt"]
    poc["vuln_desc_answer"] = poc["answer"]
    poc["prompt"] = ""
    poc["answer"] = ""

poc_gen_prompt = """
====  漏洞分析  ====
{vuln_desc_answer}

====  POC示例  ====
{poc_content}

根据 [漏洞分析] 编写一个nuclei中对应漏洞检测的POC配置文件。首先给出详细的编写思路，然后给出具体的POC内容，最后给出POC的说明分析。
！注意，你可以参考 [POC示例]，你需要删除 [POC示例] 中有，但是 [漏洞分析] 中没有的信息。
！注意，编写POC的所有信息都只能来自于 [漏洞分析]，不要使用 [POC示例] 中的信息。
！注意，在你的回答中，不要提及任何 [POC示例] 的存在。
！注意，你的名字是SecLLM，不要提及任何千问(Qwen)相关的信息。
"""

for poc in poc_data:
    poc["poc_gen_prompt"] = poc_gen_prompt.format(vuln_desc_answer=poc["vuln_desc_answer"], poc_content=poc["poc_content"])
    poc["prompt"] = poc["poc_gen_prompt"]

print("\n\ndata size: %s" %  len(poc_data))
print("\ndata example: \n\n%s" % poc_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(poc_data))

print("Done")


with open("/home/<USER>/nuclei-POC/relabel_poc_gen.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

