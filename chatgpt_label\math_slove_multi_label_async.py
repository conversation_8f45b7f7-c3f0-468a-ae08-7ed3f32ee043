# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: z<PERSON><PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm



prompt = """解决给出的数学题。首先分析解题思路（Thought），然后一步步详细写出具体的解题步骤（Steps），最后给出答案（Answer）。
解答以json的格式给出，如下：
{
    "Thought": 分析解题思路,
    "Steps": 一步步详细写出具体的解题步骤
    "Answer": 最后的答案，答案只要最后的数值，不要带上单位或其他的任何描述
}

这是一个示例：
问题：小王要将150千克含药量20%的农药稀释成含药量5%的药水．需要加水多少千克？
解答：
{example_answer}

问题：{question}
解答："""

example_ans = {
    "Thought": "此处先通过150*20%计算得到不含水的农药重量。然后使用不含水分的农药重量除以稀释后的含药量5%，得到稀释后的药水总重量。最后减去最初的药水重量，就可以得到需要加的水的重量。",
    "Steps": "1.首先，我们需要计算出不含水的农药重量。小王最初有的农药重150千克，含药量是20%。150*20%=30。即有30千克是不含水的农药的重量，有150-30=120千克是水。\n2.然后，我们需要计算出稀释后的药水总重量。有30千克是不含水的农药的重量，稀释后含药量降为5%。30/5%=600千克。即稀释后的药水重量为600千克。\n3.最后，使用稀释后的药水的重量减去原始的药水重量，我们可以得到需要增加的水的重量：600-150=450千克。\n4.所以，小王需要增加的水的重量是：150*20%/5%-150=450千克。",
    "Answer": "450"
}
example_ans_str = json.dumps(example_ans,  ensure_ascii=False)

prompt_replace = prompt.replace("{example_answer}", example_ans_str)




async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://10.24.45.213:8000/v1/chat/completions"
        messages = [
            {"role": "user",
             "content": prompt_replace.replace("{question}", point['question_chinese'])}
        ]
        body = {
            "model": "secllm-v2",
            # "temperature": temperature,
            # "top_p": top_p,
            "messages": messages,
            # "presence_penalty": presence_penalty,
            # "frequency_penalty": frequency_penalty
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""

        point['step_answer'] = answer

        if answer:
            try:
                json_answer = json.loads(answer)
                point['json_step_answer'] = json_answer
            except:
                pass
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 30  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


has_label = set()
with open(r"D:\work\GPT\chatgpt_label/ape210k_step_labeled_0.json", 'r', encoding='utf-8') as fp:
    data = json.load(fp)
    for point in data:
        has_label.add(point['id'])


path = r"D:\work\GPT\chatgpt_label/ape210k-train.jsonl"
math_question = []
with open(path, 'r', encoding='utf-8') as f:
    for line in f:
        line = json.loads(line.strip())
        if (len(line['chain'].split('\n')) > 1) and (line['id'] not in has_label):
            math_question.append(line)

print("data size: %s" %  len(math_question))
print("data example: ", math_question[0])

use_data = []
for i in range(10000):
    for _ in range(10):
        new_point = copy.deepcopy(math_question[i])
        use_data.append(new_point)

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))

with open(r"D:\work\GPT\chatgpt_label/ape210k_step_labeled_1.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

with open(r"D:\work\GPT\chatgpt_label/ape210k_step_labeled_1-o.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)

print("Done")


# 增加到data中
for i in results:
    if len(i.get("step_answer", "")) >  0:
        data.append(i)
with open(r"D:\work\GPT\chatgpt_label/ape210k_step_labeled_0.json", 'w', encoding='utf-8') as fp:
    json.dump(data, fp, indent=4, ensure_ascii=False)


# 找出结构正常的数据
new_data = []
bad_data = []
for point in data:
    if len(point.get("json_step_answer", {})) == 3:
        new_data.append(point)
    else:
        step_answer = point.get("step_answer", "")
        if len(step_answer) <= 0:
            bad_data.append(point)
            continue
        if step_answer[0] != "{":
            step_answer = "{" + step_answer
        if step_answer[-1] != "}":
            step_answer = step_answer + "}"
        try:
            json_step_answer = json.loads(step_answer)
        except:
            json_step_answer = {}
        if len(json_step_answer) == 3:
            point["step_answer"] = step_answer
            point["json_step_answer"] = json_step_answer
            new_data.append(point)
        else:
            bad_data.append(point)


# 判断是否是正确的答案，并根据id聚合
use_data = {}
for point in data:
    if point["id"] not in use_data:
        use_data[point["id"]] = {
            "id": point["id"],
            "question_chinese": point["question_chinese"],
            "question_english_mt": point["question_english_mt"],
            "equation": point["equation"],
            "result_orig_format": point["result_orig_format"],
            "result_new_format": point["result_new_format"],
            "result_float": point["result_float"],
            "chain": point["chain"],
            "bad_answer": [],
            "good_answer": []
        }


for point in new_data:
    json_step_answer =  point["json_step_answer"]
    result_orig_format = str(point["result_orig_format"])
    result_new_format = str(point["result_new_format"])
    model_result = str(json_step_answer["Answer"])

    result_orig_format_clean = result_orig_format.replace("(", "").replace(")", "").replace(" ", "").replace("_", "").replace(",", "")
    result_new_format_clean = result_new_format.replace("(", "").replace(")", "").replace(" ", "").replace("_", "").replace(",", "")
    model_result_clean = model_result.replace("(", "").replace(")", "").replace(" ", "").replace("_", "").replace(",", "")

    if (result_orig_format == model_result) or (result_new_format == model_result) or (result_orig_format_clean == model_result_clean) or (result_new_format_clean == model_result_clean):
        use_data[point["id"]]["good_answer"].append(json_step_answer)
    else:
        use_data[point["id"]]["bad_answer"].append(json_step_answer)


dpo_data = []
add_data = []
for id, point in use_data.items():
    if (len(point["bad_answer"]) >= 2) and (len(point["good_answer"]) >= 2):
        dpo_data.append(point)

    if len(point["good_answer"]) >= 7:
        continue
    if (len(point["bad_answer"]) < 2) or (len(point["good_answer"]) < 2):
        add_data.append(point)


with open(r"D:\work\GPT\chatgpt_label/ape210k_dpo.json", 'w', encoding='utf-8') as fp:
    json.dump(dpo_data, fp, indent=4, ensure_ascii=False)
