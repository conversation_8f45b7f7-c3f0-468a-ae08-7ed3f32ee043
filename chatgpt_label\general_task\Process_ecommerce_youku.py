import json

# 此部分适用于Ecommerce数据集下的conll2003、ecommerce的dev及test
'''
def bio_to_json(file_path):
    with open(file_path, 'r',encoding='utf-8') as file:
        lines = file.readlines()

    sentences = []
    current_sentence = {'sentence': '', 'entity': []}
    current_entity = None

    for line in lines:
        line = line.strip()
        if not line:
            if current_entity:
                current_sentence['entity'].append(current_entity)
            if current_sentence['sentence']:
                sentences.append(current_sentence)
            current_sentence = {'sentence': '', 'entity': []}
            current_entity = None
        else:
            word, tag = line.split(' ')

            if current_sentence['sentence']:
                current_sentence['sentence'] += ' ' + word
            else:
                current_sentence['sentence'] = word

            if tag.startswith('B-'):
                if current_entity:
                    current_sentence['entity'].append(current_entity)
                current_entity = {'token': word, 'type': tag[2:], 'start': len(current_sentence['sentence'].split()) - 1}
            elif tag.startswith('I-'):
                if current_entity:
                    current_entity['token'] += ' ' + word
            else:
                if current_entity:
                    current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                    current_sentence['entity'].append(current_entity)
                    current_entity = None

    if current_entity:
        current_entity['end'] = len(current_sentence['sentence'].split()) - 1
        current_sentence['entity'].append(current_entity)
    if current_sentence['sentence']:
        sentences.append(current_sentence)

    json_data = json.dumps(sentences, indent=4, ensure_ascii=False)
    return json_data

# 调用函数并将转换后的JSON写入文件
bio_file_path = "EcomData/conll2003/dev.txt"
json_data = bio_to_json(bio_file_path)
with open("EcomData/conll2003/dev.json", 'w',encoding='utf-8') as file:
    file.write(json_data)
'''


'''
# 此部分适用于youku数据
import json
def bio_to_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    sentences = []
    current_sentence = {'sentence': '', 'entity': []}
    current_entity = None

    for line in lines:
        line = line.strip()
        if not line:
            if current_entity:
                current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                current_sentence['entity'].append(current_entity)
                current_entity = None
            if current_sentence['sentence']:
                sentences.append(current_sentence)
            current_sentence = {'sentence': '', 'entity': []}
        else:
            if line.startswith('[') and line.endswith(']'):
                continue  # Skip lines with square brackets

            line_parts = line.split(' ', 1)  # Split line into two parts at the first occurrence of a space
            if len(line_parts) < 2:
                continue  # Skip lines that don't have a tag

            word = line_parts[0]
            tag = line_parts[1]

            if current_sentence['sentence']:
                current_sentence['sentence'] += ' ' + word
            else:
                current_sentence['sentence'] = word

            if tag.startswith('B-'):
                if current_entity:
                    current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                    current_sentence['entity'].append(current_entity)
                current_entity = {'token': word, 'type': tag[2:], 'start': len(current_sentence['sentence'].split()) - 1}
            elif tag.startswith('I-'):
                if current_entity:
                    current_entity['token'] += ' ' + word
            else:
                if current_entity:
                    current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                    current_sentence['entity'].append(current_entity)
                    current_entity = None

    if current_entity:
        current_entity['end'] = len(current_sentence['sentence'].split()) - 1
        current_sentence['entity'].append(current_entity)
    if current_sentence['sentence']:
        sentences.append(current_sentence)

    json_data = json.dumps(sentences, indent=4, ensure_ascii=False)
    return json_data


# 调用函数并将转换后的JSON写入文件
bio_file_path = "EcomData/youku/train.txt"
json_data = bio_to_json(bio_file_path)
with open("EcomData/youku/train.json", 'w',encoding='utf-8') as file:
    file.write(json_data)
'''

import json

def bio_to_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    sentences = []
    current_sentence = {'sentence': '', 'entity': []}
    current_entity = None

    for line in lines:
        line = line.strip()
        if not line:
            if current_entity:
                current_sentence['entity'].append(current_entity)
            if current_sentence['sentence']:
                sentences.append(current_sentence)
            current_sentence = {'sentence': '', 'entity': []}
            current_entity = None
        else:
            parts = line.split(' ')
            if len(parts) == 2:
                word, tag = parts[0], parts[1]
                if current_sentence['sentence']:
                    current_sentence['sentence'] += ' ' + word
                else:
                    current_sentence['sentence'] = word

                if tag != 'O':
                    entity_type = tag.split('-')[1]
                    if current_entity and entity_type == current_entity['type']:
                        current_entity['token'] += ' ' + word
                        current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                    else:
                        if current_entity:
                            current_sentence['entity'].append(current_entity)
                        current_entity = {'token': word, 'type': entity_type, 'start': len(current_sentence['sentence'].split()) - 1, 'end': len(current_sentence['sentence'].split()) - 1}
                else:
                    if current_entity:
                        current_sentence['entity'].append(current_entity)
                        current_entity = None

    if current_entity:
        current_sentence['entity'].append(current_entity)
    if current_sentence['sentence']:
        sentences.append(current_sentence)

    json_data = json.dumps(sentences, indent=4, ensure_ascii=False)
    return json_data

# 调用函数并将转换后的JSON写入文件
bio_file_path = "EcomData/ecommerce/test.txt"
json_data = bio_to_json(bio_file_path)
with open("EcomData/ecommerce/test.json", 'w',encoding='utf-8') as file:
    file.write(json_data)