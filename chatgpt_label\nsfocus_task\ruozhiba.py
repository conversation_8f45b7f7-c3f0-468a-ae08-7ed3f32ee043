# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhang<PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm


# 简单模板
TEMPLATE = '''
### Task Description:
Provided an instruction (which may include input), a code fragment response (with vulnerability), a code fragment response (without vulnerability), and a brief description of the code vulnerability.
1. Please provide a detailed explanation of the code response (with vulnerability and without vulnerability), including its functionality and implementation method.
2. Analyze in detail the types of vulnerabilities that exist in the code (with vulnerability), as well as the causes and formation methods of vulnerabilities, based on the code content. You should refer to the description of the code vulnerability.
3. Provide specific attack request parameters or attack code to exploit vulnerabilities in the code.
4. Provide the fix code. You should refer to the code fragment response (without vulnerability) and optimize it

### The instruction:
{question}

### Code Response (with vulnerability):
{rejected}

### Code Response (without vulnerability):
{chosen}

### Brief description of the code vulnerability:
{vulnerability}

'''


TEMPLATE0 = """
我会提供给你一个问题，你需要完成以下任务。
1. 一步步思考，仔细分析理解问题中的观点和逻辑。
2. 一步步仔细分析问题中可能存在的语义陷阱或文字游戏，并仔细解释问题中的陷阱，给出纠正的思路。 
    注意：问题中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 回答问题。

问题：{question}

"""

TEMPLATE1 = """
我会提供给你一个问题和一个简单的参考答案，你需要完成以下任务。
你可以综合参考答案的观点和逻辑来分析或思考，但是参考答案可能过于简短或包括错误，请不要直接复制参考答案。
1. 一步步思考，仔细分析理解问题中的观点和逻辑。
2. 一步步仔细分析问题中可能存在的语义陷阱或文字游戏，并仔细解释问题中的陷阱，给出纠正的思路。 
    注意：问题中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 回答问题，请不要直接复制参考答案，给出你自己的答案，或者丰富、优化参考答案。

问题：{question}

参考答案：{reference}
"""

TEMPLATE2 = """
我会提供给你一个段落，你需要完成以下任务。
1. 一步步思考，仔细分析理解段落中的观点和逻辑。
2. 一步步仔细分析段落中可能存在的语义陷阱或文字游戏，并仔细解释段落中的陷阱，给出纠正的思路。 
    注意：段落中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 如果段落是问题的形式，回答段落中的问题。

段落：{question}
"""


TEMPLATE3 = """
我会提供给你一个问题和一个简单的参考答案，你需要完成以下任务。
你可以综合参考答案的观点和逻辑来分析或思考，但是参考答案可能过于简短或包括错误，请不要直接复制参考答案。
1. 一步步思考，仔细分析理解解决问题的思路和方法。
2. 给出分步的解决方案，每一步都给出详细的解释和理由。
3. 给出答案，请不要直接复制参考答案，给出你自己的答案，或者丰富、优化参考答案。

问题：{question}

参考答案：{reference}
"""


TEMPLATE4 = """
我会提供给你一个问题，你需要完成以下任务。
1. 一步步思考，仔细分析理解解决问题的思路和方法。
2. 给出分步的解决方案，每一步都给出详细的解释和理由。
3. 给出答案。

问题：{question}

"""


async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://10.24.45.213:8080/v1/chat/completions"

        body = {
            "model":"wizardlm-2-8x22b",
            "stream": False,
            "top_p": 0.3,
            "temperature": 0.1,
            # "frequency_penalty": 0.2,
            "max_tokens": 4000,
            "messages": [{"role": "user", "content": point["check_prompt"]}],
            # "repetition_penalty": 1.1,
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""
        print("answer\n" + answer)

        point['check_answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 50  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


data_list = []

with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset/ruozhiba_ruozhiba_ruozhiba.jsonl", 'r', encoding='utf-8') as f:
    for line in f:
        point = json.loads(line)
        point["prompt1"] = TEMPLATE1.replace("{question}", point["instruction"] + "\n" + point["input"]).replace("{reference}", point["output"])
        point["prompt2"] = TEMPLATE0.replace("{question}", point["instruction"] + "\n" + point["input"])
        data_list.append(point)


# with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset/ruozhiba-title-norm.json", 'r', encoding='utf-8') as f:
#     da = json.load(f)
#     for point in da:
#         point["prompt1"] = TEMPLATE2.replace("{question}", point["title"] + "\n" + point["abs"])
#         data_list.append(point)


with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset/logi_qa_logi-qa.jsonl", 'r', encoding='utf-8') as f:
    for line in f:
        point = json.loads(line)
        point["prompt1"] = TEMPLATE3.replace("{question}", point["instruction"] + "\n" + point["input"]).replace("{reference}", point["output"])
        point["prompt2"] = TEMPLATE4.replace("{question}", point["instruction"] + "\n" + point["input"])
        data_list.append(point)

print("data size: %s" %  len(data_list))
print("data example: ", data_list[0])


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data_list))

with open(r"D:\work\GPT\chatgpt_label/ruozhiba_logic.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

print("Done")

data2 = [x for x in results if len(x["model_output"]) <=0]
loop = asyncio.get_event_loop()
results2 = loop.run_until_complete(main(data2))

data3 = [x for x in results if len(x["model_output"]) <=0]


logic = [x for x in results if "每一步都给出详细的解释和理由" in x["prompt1"]]
tempt = """
我会给你一些输入，包括一个逻辑推理的问题、一个正确答案、一个验证答案。
你的任务是根据参考答案判断验证答案是否正确，请仔细写出验证判断的思路。
并在最后一行写出验证答案是否正确。

输入：
逻辑推理问题：
{question}

正确答案：
{Reference answer}

验证答案：
{model_output}

请按照以下格式输出：
# 验证过程：
一步步仔细写出验证判断的思路

# 验证结果：[是否正确。如果验证答案正确，请输出yes，否则输出no]
"""

for i in logic:
    i["check_prompt"] = tempt.replace("{question}", i["instruction"] + "\n" + i["input"]).replace("{Reference answer}", i["output"]).replace("{model_output}", i["model_output"])


loop = asyncio.get_event_loop()
results3 = loop.run_until_complete(main(logic))

check4 = [x for x in logic if "# 验证结果：yes" not in x["check_answer"]]



TEMPLATE_a = """
我会提供给你一个问题和一个简单的正确答案，你需要完成以下任务。
你可以综合正确答案的观点和逻辑来分析或思考，但不要直接复制正确答案。注意你的任何分析都不要提及"正确答案"或"参考答案"等类似词汇。
1. 一步步思考，仔细分析理解解决问题的思路和方法。可以参考正确答案，帮助你思考分析，但是不要直接复制正确答案。
2. 给出分步的解决方案，每一步都给出详细的解释和理由。
3. 给出答案，请不要直接复制正确答案，给出你自己的答案，或者丰富、优化正确答案。

问题：{question}

参考答案：{reference}
"""


for i in check4:
    i["prompt3"] = TEMPLATE_a.replace("{question}", i["instruction"] + "\n" + i["input"]).replace("{reference}", i["output"])


loop = asyncio.get_event_loop()
results4 = loop.run_until_complete(main(check4))

ttt = [x for x in results if "prompt3" in x]



for i in results4:
    i["check_prompt"] = tempt.replace("{question}", i["instruction"] + "\n" + i["input"]).replace("{Reference answer}", i["output"]).replace("{model_output}", i["model_output"])


loop = asyncio.get_event_loop()
results5 = loop.run_until_complete(main(results4))
check5 = [x for x in logic if "# 验证结果：yes" not in x["check_answer"]]


a = """问题：The tail of a 1-mile long train exits a tunnel exactly 3 minutes after the front of the train entered the tunnel.  If the train is moving 60 miles per hour, how many miles long is the tunnel?
使用python代码的解决方案：Let's represent the unknown variables by letters.\n- L - length of the train\n- V - train speed in miles per hour\n- T - number of hours since the front of the train entered the tunnel\n- E - number of hours since the tail of the train entered the tunnel\n- t - time from the moment the front of the train entered the tunnel to the moment it exited the tunnel\n\nWe know that $L = 1$ mile and $t = 3$ minutes. We need to find the length of the tunnel, which we denote as $T$.\nTo find $T$, we can write two equations:\n- $T = L + V * E$, because the train moved by $V$ miles per hour for $E$ hours\n- $V * T = L + V * t$, because the front of the train exited the tunnel after traveling for $t$ hours\n\nWe need to express $T$ only through $V$ and $t$, which can be done using the second equation.\nLet's implement the problem in python code and solve it with sympy.\n<llm-code>\nfrom sympy import symbols, Eq, solve\n\nV = symbols('V')\nt = 3/60\nL = 1\nT = symbols('T')\nE = T - t\n\neq1 = Eq(T, L + V * E)\neq2 = Eq(V * T, L + V * t)\n\nT_value = solve((eq1, eq2), T)[0]\n\n# Calculate the length of the tunnel as the difference between $T$ and $L$\ntunnel_length = T_value - L\n\nprint(f\"The length of the tunnel is {round(tunnel_length, 3)} miles\")\n</llm-code>

解决方案中会先分析解决思路，然后使用python代码来解决问题，python代码部分使用<llm-code>和</llm-code>包裹住，格式为<llm-code>python代码</llm-code>。

请问解决方案中的解决思路有哪些错误。python代码有哪些错误，实际执行的话python解释器会报什么错？"""
