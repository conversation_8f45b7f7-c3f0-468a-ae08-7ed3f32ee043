# -*- encoding: utf-8 -*-
# @ModuleName: label_event.py
# @Author: zhanglong
# @Time: 2023/6/21 14:55


import requests
import pandas as pd
from tqdm import tqdm
import json


def openai_query(messages, model="gpt-3.5-turbo-16k", temperature: float = 0, top_p: float = 1,
                       presence_penalty: float = 0.0, frequency_penalty: float = 0.0):
    url = "http://gpt1.attackgraph.com:16180/openai"
    body = {
        "model": model,
        "temperature": temperature,
        "top_p": top_p,
        "messages": messages,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty
    }

    try:
        # print("openai chatgpt query!")
        response = requests.post(url, json=body)
        # print("openai chatgpt response status: %s - text: %s" % (response.status_code, response.text))
        if response.status_code == 200:
            res = response.json()
            return res
        else:
            print("openai chatgpt api-- Error: %s" % response.json())
            return None
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        return None


def chatgpt_event_label(input):
    try:
        message_list = [
            "%s这是一条告警日志，帮我生成一个对其进行解析和结构化的python代码",

        ]
        messages = [
            {"role": "system", "content": """%s这是一条告警日志，帮我生成一个对其进行解析和结构化的python代码"""},
            {"role": "user", "content": """分析以下告警载荷：%s""" % input}]

        response = openai_query(messages, frequency_penalty=0.1, temperature=0.8)
        return response["choices"][0]["message"]['content']
    except Exception as ex:
        print("chatgpt event label error: %s" % str(ex))
        return ""


path = r'D:\work\GPT\chatgpt_label/alarms_labeled_splited-20230621.xlsx'
threat_data = pd.read_excel(path, sheet_name=None, dtype=str)
threat_data = threat_data["Sheet1"].fillna("")
print("threat data size: %s" % threat_data.shape[0])
print("data columns: ")
for col in threat_data.columns:
    print("%s: %s" % (col, threat_data[col].values[0]))


all_data = []

for index, row in tqdm(threat_data.iterrows()):
    row = row.to_dict()
    answer = chatgpt_event_label(row['model_input'])
    row['model_output'] = answer
    all_data.append(row)


with open('./alarms_labeled_output-20230621-2.json', 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

use_data = [x for x in all_data if len(x['model_output']) > 0]
print("success data size: %s" %  len(use_data))
print("fail data size: %s" %  (len(all_data) - len(use_data)))
print("Done")









import json
log_path_list = [
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log2.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log3.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log4.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log5.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log6.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log7.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log8.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log9.log",

    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log1.log",
    r"D:\work\GPT\chatgpt_label\nsfgpt-logs\***********/log2.log",
]
def parse_log(log_path):
    with open(log_path, 'r', encoding="utf-8") as file:
        data = file.read()

    data = data.split("root - Creating chat completion")
    data = data[1:]
    print(data[0])
    print(data[1])

    data_2 = [x.split("input_ids shape: ")[0].strip() for x in data]
    print(data_2[0])
    print(data_2[1])

    da = [x.split(" - INFO - root - ") for x in data_2]
    da = [x for x in da if len(x) == 5]
    print(da[0])
    print(da[10])
    dd = [{"time": x[0], "query": x[1], "history": x[2], "answer": x[4]} for x in da]
    return dd

all_log = []
for  path in log_path_list:
    all_log.extend(parse_log(path))
with open(r"D:\work\GPT\chatgpt_label\nsfgpt-logs\nsfgpt.log", 'w', encoding="utf-8") as file:
    json.dump(all_log, file, ensure_ascii=False, indent=4)



from elasticsearch import Elasticsearch


es_hosts = ["***********:9200"]
es = Elasticsearch(es_hosts, http_auth=('elastic', 'owl@2019'), timeout=60)
for i in all_log:
    es.index(index='nsfgpt_log', doc_type='_doc', body=i)








