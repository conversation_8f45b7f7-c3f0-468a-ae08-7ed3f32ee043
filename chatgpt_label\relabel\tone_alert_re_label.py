
import os
import json
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset, Dataset

prompt = """我会给你一个攻击事件告警和一些攻击研判参考信息。你需要对该攻击事件进行分析。

注意：你的分析可以参考【攻击研判参考信息】。但是【攻击研判参考信息】中可能存在错误，请不要完全照抄，写出你自己的分析判断。
注意：不要在回答中的任何位置提及【攻击研判参考信息】的存在。


请按如下格式回答：
### 攻击事件分析

#### 1. 解码攻击载荷
[根据攻击载荷或者请求体，给出这个攻击事件的解码结果。]

#### 2. 解读攻击事件内容和含义
[一步步思考，详细的解读这个攻击事件的内容和含义。分别从请求体的内容和进行的行为，请求体会导致具体的操作，漏洞的实现原理，攻击的实现方式等方面进行分析。注意，你的分析要详细且专业。]

#### 3. 攻击类型、利用的漏洞与危害分析
- **攻击类型**: [攻击的类型]
- **利用的漏洞**: [攻击事件利用的漏洞以及版本]
- **攻击危害**: [攻击成功可能导致的危害]

#### 4. 攻击链路分析
[一步步思考，从技术上分析攻击具体的生效流程。注意，结合漏洞具体的实现原理、漏洞的利用流程及漏洞如何生效来详细分析。]

#### 5. 判断是否真实攻击
[一步步思考，分析并判断这次攻击事件是否是一次真实的攻击，请结合攻击载荷或者请求体分析判断。]

#### 6. 攻击结果分析
[一步步思考，分析并判断这次攻击事件的结果是否攻击成功，结合请求体和响应体分析判断。]

#### 7. 修复建议
[给出修复建议]


【攻击事件】：
{q_body}


以下是一些【攻击研判参考信息】:
{a_body}


"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.4:59052/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Qwen2.5-72B-Instruct",  # "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



all_data = []

# data_dir = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-06\T1-事件_0630_json"
data_dir = "/home/<USER>/llm_dataset/nsfocus/md/T1-事件_0630_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            q = line['响应体']
            a = line['请求体']
            line['请求体'] = q
            line['响应体'] = a
            all_data.append(line)


data_dir = "/home/<USER>/llm_dataset/nsfocus/md/t1-威胁事件_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            if ("威胁事件2023-05-19_5K" in data_name) or ("威胁事件2023-05-23_3K" in data_name):
                q = line['请求体']
                a = line['响应体']
            else:
                q = line['响应体']
                a = line['请求体']
            line['请求体'] = q
            line['响应体'] = a
            all_data.append(line)


# data_dir = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-06\T1-事件_0616_json"
data_dir = "/home/<USER>/llm_dataset/nsfocus/md/T1-事件_0616_json"
data_list = os.listdir(data_dir)
for data_name in data_list:
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = json.loads(line.strip())
            all_data.append(line)

print("\n\nall data size: %s" %  len(all_data))


print("\n\nmake list data")
new_data = []
for point in tqdm(all_data):
    for key, value in point.items():
        if value is None:
            point[key] = ""

    q =  point.get('请求体', "")
    if len(q.strip()) <= 10:
        continue

    q = point.get('请求体', "").strip()
    a = point.get('响应体', "").strip()
    d = point.get('载荷', "").strip()

    q_body = ""
    if (len(d.strip()) > 10) and (d not in q):
        q_body = q_body + "攻击载荷: \n%s\n\n" % d
    q_body = q_body + "请求体: \n%s\n\n响应体: \n%s\n\n" % (q, a)

    a_body = ""
    for keys in ["事件名称", "事件类型", "事件别名", "关键词", "研判结果", "攻击结果", "研判结果校正", "处置结果", "影响", "检测思路", "采集证据", "处置建议"]:
        value = point.get(keys, "")
        if len(value.strip()) > 0:
            a_body = a_body + "%s: %s\n" % (keys, value)

    point['prompt'] = prompt.replace("{q_body}", q_body).replace("{a_body}", a_body)

    new_data.append(point)

print("\n\nuse data size: %s" % len(new_data))


new_data = new_data[:50000]


print("\n\ndata size: %s" %  len(new_data))
print("\ndata example: \n\n%s" % new_data[0])

print("\n\nprompt example:\n%s" % new_data[0]["prompt"])


print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(new_data))

print("\n\nDone")


with open("/home/<USER>/llm_dataset/tone_alert_re_label/tone_alert_re_label_v1_50000_qwen25.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)



