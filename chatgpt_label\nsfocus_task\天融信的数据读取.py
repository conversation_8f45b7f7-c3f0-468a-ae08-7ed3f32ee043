import re
# 读取天融信的原始数据raw
def read_raw(test_text):
    # 用于天融信
    test_text = test_text.strip()
    test_text = re.sub(" ddev=", '', test_text)
    test_text = re.sub("= ", "='' ", test_text)
    data = test_text
    d = re.split("=| ", data)
    result = {}
    index = 0
    while index < 2:
        result[d[index]] = d[index + 1]
        index = index + 2
    while index < 5:
        result[d[index]] = re.sub('"', '', d[index + 1] + " " + d[index + 2])
        index = index + 3
    while index < 29:
        result[d[index]] = d[index + 1]
        index = index + 2
    while index < 33:
        result[d[index]] = re.sub('"', '', d[index + 1] + " " + d[index + 2] + " " + d[index + 3])
        index = index + 4
    while index < 41:
        result[d[index]] = d[index + 1]
        index = index + 2
    return(result)

with open("F:\data_process\重庆移动_ALL_DATA.tar\DATA\天融信_IDPS.txt","r",encoding='utf-8') as f:
    index=0
    for line in f.readlines():
        # line = re.sub("\n", '', line)
        if index==0:
            raw=line
            index=index+1
        else:
            format=line
            break

raw_dict = read_raw(raw)