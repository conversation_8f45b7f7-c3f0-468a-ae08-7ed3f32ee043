# coding:utf-8
import io
import re
import sys
import time
import pickle

if "NoneType" not in globals():
    NoneType=type(None)

try:
    import copyreg
except:
    import copy_reg as copyreg
from datetime import datetime,timedelta,tzinfo
##20210625修改：跨版本RPC通信时，一边使用native的timezone，一边使用自定义的timezone，会出错
#try:
#    from datetime import timezone
#except:
ZERO_TIMEDELTA=timedelta()
class timezone(tzinfo):
    def __init__(self,offset=None,name="UTC"):
        self.Delta=offset or ZERO_TIMEDELTA
        self.Name=name
    def utcoffset(self,dt):
        return self.Delta
    def dst(self,dt):
        return ZERO_TIMEDELTA
    def tzname(self,dt):
        return self.Name
    def __str__(self):
        return self.Name
    def __repr__(self):
        return self.Name
timezone.utc=timezone(timedelta())#UTC时间
#datetime.timezone=timezone#硬Hook，解决其它模块使用时区时序列化类型不一致的问题

class Recognition:
    #能够确认目标数据应当由当前模块处理。
    Confirmed = 0x7F
    #基本上可以排除目标数据会有其它模块处理的可能性。
    ExcludeOthers = 0x70
    #目标数据完全符合当前模块的输入规范，但可能会有其它更适合的模块。
    Standardized = 0x40
    #目标数据不完全具备，但也不违背当前模块的输入规范，当前模块可以进行处理。
    Uncertain = 0x30
    #当前模块几乎能够处理所有的输入，并且具有优于其它模块的通用性，因此可以作为默认值使用。
    MostUniversal = 0x1F
    #当前模块几乎能够处理所有的输入，如无其它模块可用时可以考虑使用。
    Universal = 0x10
    #目标数据违背了当前模块的输入规范，不能使用当前模块进行处理（或即使处理也没有效果）。
    Irregularities = 0x00
    #当前模块能够处理输入数据，但可能会造成不可逆的损失。这是一个附加项，不能单独使用。
    Treatable = 0x80

PICKLER_CLASS=getattr(pickle,"_Pickler",pickle.Pickler)
UNPICKLER_CLASS=getattr(pickle,"_Unpickler",pickle.Unpickler)
class _Pickler(PICKLER_CLASS):
    def save(self,obj,*args,**kwargs):
        for i,j in Utils.PICKLERS.items():
            if isinstance(obj,i):
                return PICKLER_CLASS.save(self,j(obj),*args,**kwargs)
        return PICKLER_CLASS.save(self,obj,*args,**kwargs)
#作为一个copyreg的reduce函数，它不能是某个类的成员；它需要在Utils类中调用，不能双下划线开头，要不就得__Utils_FromTimeStamp
def _FromTimeStamp(timestamp):
    if timestamp is None:
        return None
    if timestamp<=253402272000:
        return datetime.fromtimestamp(timestamp,Utils.LocalTimeZone)
    if timestamp<=253402272000000:#10000/01/01 00:00:00的毫秒时间戳
        return datetime.fromtimestamp(timestamp/1000,Utils.LocalTimeZone)
    #微秒时间戳，偶尔也会在一些地方遇到。老天爷保佑我们不要遇到纳秒/皮秒时间戳  >_<..
    return datetime.fromtimestamp(timestamp/1000000,Utils.LocalTimeZone)
class Utils:
    #基础功能、文本相关
    IsPython3=sys.version_info>=(3,0)
    if IsPython3:
        BinaryType=bytes
        TextType=str
        
        IntToWChar=chr
        IntToByte=lambda x:x
        IntToBinary=lambda x:x.to_bytes(1,"little")
        BinaryToInts=lambda x:x#list#迭代器也可以吧？
        
        #python3中，bytes[xx]/for i in bytes的类型为int，反而无法和其它bytes拼接/join，还得先转换回bytes才行
        ByteArrayToBinary=bytes
    else:
        BinaryType=str
        TextType=unicode
        
        IntToWChar=unichr
        IntToByte=chr
        IntToBinary=chr
        BinaryToInts=lambda x:[ord(i) for i in x]
        
        #python2中的str怎么取下标依然是str所以没关系
        ByteArrayToBinary=lambda x:b"".join(x)
    RegexAllNumber=re.compile(u"^\\d+$")
    RegexOctASCII=re.compile(u"[01]?[0-7]{1,2}")
    #输入是int，字符型请提前ord好
    @staticmethod
    def IsAsciiTextChar(a):
        if a>0x7E:
            return False
        if a>0x1F:
            return True
        if a>0x0D:
            return False
        return a>0x08
    @staticmethod
    def Trim(a):
        return None if a is None else a.strip(u"\x08\x09\x0A\x0D\x20")
    @staticmethod
    def Omit(a,max_length=256):
        if a is None:
            return None
        if len(a)<=max_length:
            return a
        if max_length<=3:
            return "."*max_length
        return a[:max_length//2-1]+"."*(2+(max_length&1))+a[-(max_length-1)//2+1:]

    #集合相关
    @staticmethod
    def GetOrAdd(dict,key,onAdd):
        if key in dict:
            return dict[key]
        else:
            dict[key]=onAdd(key)
            return dict[key]
    @staticmethod
    def AddOrUpdate(dict,key,onAdd,onUpdate):
        tres=None
        if key in dict:
            tres=onUpdate(key,dict[key])
        else:
            tres=onAdd(key)
        dict[key]=tres
        return tres
    
    #日期时间相关
    LocalTimeZone=timezone(timedelta(seconds=-time.timezone),time.tzname[0])#跟随本地系统
    UTCTimeZone=timezone.utc#UTC时间
    CSTTimeZone=timezone(timedelta(seconds=28800),"CST")#中国标准时间
    @staticmethod
    def Now():
        return datetime.now(Utils.LocalTimeZone)
    @staticmethod
    def UTCNow():
        return datetime.now(Utils.UTCTimeZone)
    #python2中没有datetime.timestamp()，要手动计算一下。反正都算了，也不差个python3了。
    @staticmethod
    def TimeStamp(dt):
        return None if dt is None else (dt.astimezone(Utils.UTCTimeZone)-datetime(1970,1,1,tzinfo=Utils.UTCTimeZone)).total_seconds()
    @staticmethod
    def DateTime(time_object):
        if time_object is None:
            return None
        type_of_object=type(time_object)
        if type_of_object==datetime:
            return time_object
        if type_of_object in {int,float}:
            return _FromTimeStamp(time_object)
        if type_of_object==Utils.BinaryType:
            time_object=time_object.decode("ASCII")#不加ignore，出错就出错
            type_of_object=type(time_object)
        if type_of_object==Utils.TextType:
            try:
                return _FromTimeStamp(float(time_object))
            except ValueError:
                pass
            try:#ISO8601
                if time_object[-1]=="Z":
                    time_object=time_object[:-1]+"+00:00"
                return datetime.fromisoformat(time_object)
            except:
                pass
        raise Exception("Fail to parse %s to datetime"%repr(time_object))
    @staticmethod
    def FromTimeStamp(timestamp):
        return _FromTimeStamp(timestamp)
    copyreg.pickle(datetime,lambda x:(_FromTimeStamp,(Utils.TimeStamp(x),)))
    
    #序列化和反序列化相关
    PICKLERS={}
    ##说实话，这里的*args并不靠谱，因为dumps/loads的参数顺序与Pickler/Unpickler.__init__不同。
    ##但这俩函数的签名有2.7、3.6-3.7、3.8-3.9三个版本，这根本没法玩
    @staticmethod
    def Dumps(obj,*args,**kwargs):
        f=io.BytesIO()
        _Pickler(f,*args,**kwargs).dump(obj)
        res=f.getvalue()
        assert isinstance(res,Utils.BinaryType)
        return res
    @staticmethod
    def Loads(s,*args,**kwargs):
        ##注：要劫持pickle._Unpickler的load方法，可能需要修改该类的dispatch（是一个dict）？感觉非常麻烦，暂时跳过吧，反正也不着急
        if isinstance(s,Utils.TextType):
            #raise TypeError("Can't load pickle from unicode string")
            s=s.encode("UTF-8","ignore")
        file=io.BytesIO(s)
        #如果不加下面这行，那么python2中的str类型包含非ASCII字符时，将无法在python3中反序列化
        if Utils.IsPython3 and "encoding" not in kwargs:
            kwargs["encoding"]="bytes"
        return UNPICKLER_CLASS(file,*args,**kwargs).load()