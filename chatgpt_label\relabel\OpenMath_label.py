# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: z<PERSON><PERSON>
# @Time: 2021/11/22 16:07


import json
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset




prompt = """我会给你一个问题，一个使用Python代码的解决方案，和实际执行解决方案中的python代码，python解释器的报错内容。
解决方案中会先分析解决思路，然后使用python代码来解决问题，python代码部分使用<llm-code>和</llm-code>包裹住，格式为<llm-code>python代码</llm-code>。
你需要完成以下任务：
1. 一步步仔细分析解决方案中的解决思路有哪些错误。
2. 一步步仔细分析python代码中有哪些错误，实际执行时python解释器会报什么错？并给出报错的原因。
3. 给出修正后的正确代码。

你的回答请满足以下要求：
1. 你的回答需要使用markdown格式。
2. 你的回答需要使用中文。


问题：{question}

使用python代码的解决方案：
{generated_solution}

python解释器的报错内容: 
{error_message}"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.4:59052/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model":"Qwen2.5-72B-Instruct",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


print("read data...")
raw_data = load_dataset("json", data_files="/home/<USER>/llm_dataset/OpenMathInstruct-1/incorrect_solutions/train.jsonl")
raw_data = raw_data['train']
print("\nraw data size: %s" % len(raw_data))

raw_data = raw_data.filter(lambda x: (x['error_message'] != "<not_executed>") and (len(x['error_message']) > 0), num_proc=35)
print("\nfilter data size: %s" % len(raw_data))

raw_data = raw_data.map(lambda x: {"prompt": prompt.replace("{question}", x["question"]).replace("{generated_solution}", x["generated_solution"]).replace("{error_message}", x["error_message"])}, num_proc=35)
print("\n\nprompt over. prompt example:\n%s" % raw_data[0]["prompt"])

print("\n\nmake list data")
data = []
for point in tqdm(raw_data):
    data.append(point)
data = data[:20000]

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("\n\nDone")


with open("/home/<USER>/llm_dataset/OpenMathInstruct_relabel/OpenMathInstruct_re_lable_2w_qwen25.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)


