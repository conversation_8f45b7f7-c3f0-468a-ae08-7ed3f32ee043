# coding:utf-8
import os
import platform
import subprocess

import logging
import traceback

try:
    from .EncodingEx import *
except:
    from EncodingEx import *

#在引号外使用、在单引号内使用、在双引号内使用、在$''内使用
LINUX_SHELL_ESCAPE_TABLE=[
    [     None,     None,     None, u'\\x00'],[     None,     None,     None, u'\\x01'],[     None,     None,     None, u'\\x02'],[     None,     None,     None, u'\\x03'],
    [     None,     None,     None, u'\\x04'],[     None,     None,     None, u'\\x05'],[     None,     None,     None, u'\\x06'],[     None,     None,     None,   u'\\a'],
    [     None,     None,     None,   u'\\b'],[     None,     None,     None,   u'\\t'],[     None,     None,     None,   u'\\n'],[     None,     None,     None,   u'\\v'],
    [     None,     None,     None,   u'\\f'],[     None,     None,     None,   u'\\r'],[     None,     None,     None, u'\\x0E'],[     None,     None,     None, u'\\x0F'],
    [     None,     None,     None, u'\\x10'],[     None,     None,     None, u'\\x11'],[     None,     None,     None, u'\\x12'],[     None,     None,     None, u'\\x13'],
    [     None,     None,     None, u'\\x14'],[     None,     None,     None, u'\\x15'],[     None,     None,     None, u'\\x16'],[     None,     None,     None, u'\\x17'],
    [     None,     None,     None, u'\\x18'],[     None,     None,     None, u'\\x19'],[     None,     None,     None, u'\\x1A'],[     None,     None,     None,   u'\\e'],
    [     None,     None,     None, u'\\x1C'],[     None,     None,     None, u'\\x1D'],[     None,     None,     None, u'\\x1E'],[     None,     None,     None, u'\\x1F'],
    [   u'\\ ',     u' ',     u' ',     u' '],[   u'\\!',     u'!',     u'!',     u'!'],[   u'\\"',     u'"',   u'\\"',   u'\\"'],[   u'\\#',     u'#',     u'#',     u'#'],#特注：$'"'和$'\"'是一样的，但为了避免不同引号嵌套产生问题，凡是可以转义的字符都应最大限度转义
    [   u'\\$',     u'$',     u'$',     u'$'],[     u'%',     u'%',     u'%',     u'%'],[   u'\\&',     u'&',     u'&',     u'&'],[   u"\\'",     None,     u"'",   u"\\'"],
    [   u'\\(',     u'(',     u'(',     u'('],[   u'\\)',     u')',     u')',     u')'],[     u'*',     u'*',     u'*',     u'*'],[     u'+',     u'+',     u'+',     u'+'],
    [   u'\\,',     u',',     u',',     u','],[     u'-',     u'-',     u'-',     u'-'],[     u'.',     u'.',     u'.',     u'.'],[     u'/',     u'/',     u'/',     u'/'],
    [     u'0',     u'0',     u'0',     u'0'],[     u'1',     u'1',     u'1',     u'1'],[     u'2',     u'2',     u'2',     u'2'],[     u'3',     u'3',     u'3',     u'3'],
    [     u'4',     u'4',     u'4',     u'4'],[     u'5',     u'5',     u'5',     u'5'],[     u'6',     u'6',     u'6',     u'6'],[     u'7',     u'7',     u'7',     u'7'],
    [     u'8',     u'8',     u'8',     u'8'],[     u'9',     u'9',     u'9',     u'9'],[   u'\\:',     u':',     u':',     u':'],[   u'\\;',     u';',     u';',     u';'],
    [   u'\\<',     u'<',     u'<',     u'<'],[     u'=',     u'=',     u'=',     u'='],[   u'\\>',     u'>',     u'>',     u'>'],[   u'\\?',     u'?',     u'?',     u'?'],
    [     u'@',     u'@',     u'@',     u'@'],[     u'A',     u'A',     u'A',     u'A'],[     u'B',     u'B',     u'B',     u'B'],[     u'C',     u'C',     u'C',     u'C'],
    [     u'D',     u'D',     u'D',     u'D'],[     u'E',     u'E',     u'E',     u'E'],[     u'F',     u'F',     u'F',     u'F'],[     u'G',     u'G',     u'G',     u'G'],
    [     u'H',     u'H',     u'H',     u'H'],[     u'I',     u'I',     u'I',     u'I'],[     u'J',     u'J',     u'J',     u'J'],[     u'K',     u'K',     u'K',     u'K'],
    [     u'L',     u'L',     u'L',     u'L'],[     u'M',     u'M',     u'M',     u'M'],[     u'N',     u'N',     u'N',     u'N'],[     u'O',     u'O',     u'O',     u'O'],
    [     u'P',     u'P',     u'P',     u'P'],[     u'Q',     u'Q',     u'Q',     u'Q'],[     u'R',     u'R',     u'R',     u'R'],[     u'S',     u'S',     u'S',     u'S'],
    [     u'T',     u'T',     u'T',     u'T'],[     u'U',     u'U',     u'U',     u'U'],[     u'V',     u'V',     u'V',     u'V'],[     u'W',     u'W',     u'W',     u'W'],
    [     u'X',     u'X',     u'X',     u'X'],[     u'Y',     u'Y',     u'Y',     u'Y'],[     u'Z',     u'Z',     u'Z',     u'Z'],[   u'\\[',     u'[',     u'[',     u'['],
    [  u'\\\\',  u'\\\\',  u'\\\\',  u'\\\\'],[   u'\\]',     u']',     u']',     u']'],[   u'\\^',     u'^',     u'^',     u'^'],[     u'_',     u'_',     u'_',     u'_'],
    [   u'\\`',     u'`',     u'`',     u'`'],[     u'a',     u'a',     u'a',     u'a'],[     u'b',     u'b',     u'b',     u'b'],[     u'c',     u'c',     u'c',     u'c'],
    [     u'd',     u'd',     u'd',     u'd'],[     u'e',     u'e',     u'e',     u'e'],[     u'f',     u'f',     u'f',     u'f'],[     u'g',     u'g',     u'g',     u'g'],
    [     u'h',     u'h',     u'h',     u'h'],[     u'i',     u'i',     u'i',     u'i'],[     u'j',     u'j',     u'j',     u'j'],[     u'k',     u'k',     u'k',     u'k'],
    [     u'l',     u'l',     u'l',     u'l'],[     u'm',     u'm',     u'm',     u'm'],[     u'n',     u'n',     u'n',     u'n'],[     u'o',     u'o',     u'o',     u'o'],
    [     u'p',     u'p',     u'p',     u'p'],[     u'q',     u'q',     u'q',     u'q'],[     u'r',     u'r',     u'r',     u'r'],[     u's',     u's',     u's',     u's'],
    [     u't',     u't',     u't',     u't'],[     u'u',     u'u',     u'u',     u'u'],[     u'v',     u'v',     u'v',     u'v'],[     u'w',     u'w',     u'w',     u'w'],
    [     u'x',     u'x',     u'x',     u'x'],[     u'y',     u'y',     u'y',     u'y'],[     u'z',     u'z',     u'z',     u'z'],[   u'\\{',     u'{',     u'{',     u'{'],
    [   u'\\|',     u'|',     u'|',     u'|'],[   u'\\}',     u'}',     u'}',     u'}'],[   u'\\~',     u'~',     u'~',     u'~'],[     None,     None,     None, u'\\x7F']]
LINUX_SHELL_ESCAPE_BEGIN=[u"",u"'",u'"',u"$'"]
LINUX_SHELL_ESCAPE_END=[u"",u"'",u'"',u"'"]

class OperatingSystemEx:
    IsWindows=platform.system()=="Windows"
    
    @staticmethod
    def LinuxShellEscapeIterate(text):
        mode=0
        for i in text:
            if i>u'\x7f':
                yield i
                continue
            escape_chars=LINUX_SHELL_ESCAPE_TABLE[ord(i)]
            if not escape_chars[mode] is None:
                yield escape_chars[mode]
            #当前字符没有办法转义到当前编码模式里，好在bash支持四种模式的无缝衔接：bash -c "l"'s'\ $'\x2F'
            #首先结束当前模式
            yield LINUX_SHELL_ESCAPE_END[mode]
            #然后寻找可用的新模式
            for mode in range(4):
                if not escape_chars[mode] is None:
                    break
            #然后开始新模式
            yield LINUX_SHELL_ESCAPE_BEGIN[mode]
            yield escape_chars[mode]
        yield LINUX_SHELL_ESCAPE_END[mode]
    @staticmethod
    def LinuxShellEscape(text):
        return u"".join(OperatingSystemEx.LinuxShellEscapeIterate(EncodingEx.GetText(text)))
    @staticmethod
    def ShellEscape(text):
        if OperatingSystemEx.IsWindows:
            raise NotImplementedError("尚未支持windows平台的命令行转义  T_T..")
        return OperatingSystemEx.LinuxShellEscape(text)

    @staticmethod
    def CmdShellSOE(cmd):
        process=subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        process.wait()
        return process.returncode,process.stdout.read(),process.stderr.read()
    @staticmethod
    def CmdShell(cmd):
        process=subprocess.Popen(cmd,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)
        process.wait()
        tout=process.stdout.read()
        terr=process.stderr.read()
        return tout+b"\n"+terr if terr else tout
    @staticmethod
    def CmdShellAsync(cmd):
        return subprocess.Popen(cmd,close_fds=True,stdout=subprocess.PIPE,stderr=subprocess.PIPE,shell=True)