import requests
import asyncio
import websockets
import json


def get_prompt(text, hash_="oub8sopw81b"):
    payload = {
        # "fn_index": 0,
        "data": [text+"\n请一步步分析这个请求体的内容、进行的行为、危害的程度和可能造成的影响。写出详细的推理过程，给出相应的处置建议",  # 问题
            # "",  # 选择的知识库。需要先选择知识库生成对应的session_hash才能生效
            [],
            2048,
            0.8,
            50,
            0.01,
            1.02,
            1,
            None
        ],
        "event_data": None,
        "fn_index": 0,  # 对应的接口索引，由前端框架自动生成，调整前端会发生变化
        "session_hash": hash_  # 建立连接后的session_hash
    }
    return payload


async def send_poc(poc_data, session_hash):
    uri = "ws://10.24.45.201:10081/queue/join"
    print("poc data: ", poc_data)
    print("session_hash: ", session_hash)
    try:
        async with websockets.connect(uri) as websocket:
            while True:
                data = await websocket.recv()
                data = json.loads(data)

                if data['msg'] == "send_hash":
                    await websocket.send(json.dumps({"fn_index": 0, "session_hash": session_hash}))
                elif data['msg'] == "send_data":
                    await websocket.send(json.dumps(get_prompt(poc_data, hash_= session_hash)))
                elif data['msg'] == "process_completed":
                    return data['output']["data"][0][0][1]
                else:
                    print(data['msg'])
    except Exception as ex:
        print("ex", ex, data)


line_list = [
    'POST /index?id=1&_ZQA_TEST_ID=8d28c2d90d218dfa HTTP/1.1Host: hostnameUser-Agent: Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.89 Safari/537.36Accept-Encoding: gzip, deflateAccept: */*Connection: closeContent-Type: application/x-www-form-urlencodedContent-Length: 41username=/proc/self/fd/35/etc/passwd%2500',
    'GET /plugins/editors/jckeditor/plugins/jtreelink/dialogs/links.php?extension=menu&view=menu&parent=" UNION SELECT NULL,NULL,CONCAT_WS(0x203a20,USER(),DATABASE(),VERSION(),0x6337303862366464646633653834616262643438623733386464663931343864),NULL,NULL,NULL,NULL,NULL-- aa',
    "GET /AgentBoard.XGI?user='||'1&cmd=UserLogin",
    "125 or 1=(select top 1 quotename(cc_auth_code ':' cc_auth_date) from orders) --",
    'POST /default.action HTTP/1.1\nHost: 127.0.0.1:8080\nUser-Agent: python-requests/2.18.4\nAccept-Encoding: gzip, deflate\nAccept: */*\nConnection: keep-alive\nContent-Type: application/x-www-form-urlencoded\nContent-Length: 23\n\n111=../../../etc/passwd'

]
out = []
for index, line in enumerate(line_list):
    print(index)
    l = asyncio.run(send_poc(line, "oub8sopw81" + str(index % 10)))
    out.append(l)
    print(l)
