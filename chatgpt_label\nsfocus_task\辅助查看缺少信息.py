import json
import random
from tqdm import tqdm
import re
import os

# 这里是一些数据处理的辅助工具
# target是文档中标注必需的字段
target = {'raw_data', 'threat_type', 'dport', 'bytes_all', 'alertlevel', 'log_type', 'timestamp', 'post_data', 'r_body', 'log_name', 'sip', 'payload', 'q_body', 'log_result', 'begin_time', 'end_time', 'dip', 'log_message', 'sport'}
# 此处为最初版本的key_map
key_map = {
    "msgtype": {
        "name": "msgtype",
        "description": "发送给ISOP的消息类型",
        "value_range": "13：入侵检测（吸星）\n14：入侵检测（zealot）\n17：DDos\n21：webshell\n31489：nti威胁情报\n63：SDK防病毒\n100：自定义情报\n101：自定义规则\n110：内网主机扫描\n130：翻墙行为告警\n131：违法网站告警\n132：违规应用告警",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "13": "入侵检测（吸星）",
            "14": "入侵检测（zealot）",
            "17": "DDos",
            "21": "webshell",
            "31489": "nti威胁情报",
            "63": "SDK防病毒",
            "100": "自定义情报",
            "101": "自定义规则",
            "110": "内网主机扫描",
            "130": "翻墙行为告警",
            "131": "违法网站告警",
            "132": "违规应用告警",
            "109": "钓鱼邮件告警"
        },
        "prompt_template": "这是安全日志的消息类型。",
        "trans_key": "msgtype"
    },
    "sip": {
        "name": "sip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "sport": {
        "name": "sport",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dip": {
        "name": "dip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dport": {
        "name": "dport",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "src_ip": {
        "name": "src_ip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "src_port": {
        "name": "src_port",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dst_ip": {
        "name": "dst_ip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dst_port": {
        "name": "dst_port",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "protocol": {
        "name": "protocol",
        "description": "协议",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "6": "HTTP",
            "17": "UDP"
        },
        "prompt_template": "这是告警事件的协议类型。",
        "trans_key": "protocol"
    },
    "direct": {
        "name": "direct",
        "description": "当前会话的方向",
        "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "内向外",
            "2": "外向内",
            "3": "内对内",
            "4": "其他"
        },
        "prompt_template": "这是告警事件的流量方向。",
        "trans_key": "direction"
    },
    "acted": {
        "name": "acted",
        "description": "实际所完成的动作",
        "type": "int",
        "prompt_template": "这是根据规则设备实际采取的动作。",
        "trans_key": "acted_action"
    },
    "alertlevel": {
        "name": "alertlevel",
        "description": "告警级别",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的告警级别。",
        "trans_key": "alertlevel"
    },
    "timestamp": {
        "name": "timestamp",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "stat_time": {
        "name": "stat_time",
        "description": "时间戳",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "rawlen": {
        "name": "rawlen",
        "description": "报文长度",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是报文的长度。",
        "trans_key": "payload_len"
    },
    "rawinfo": {
        "name": "rawinfo",
        "description": "原始报文信息",
        "value_range": "base64编码\n包含SDK防病毒日志中的md5信息",
        "type": "string",
        "prompt_template": "这是原始报文信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    "msg": {
        "name": "msg",
        "description": "存放对应该日志的描述",
        "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "ar": {
        "name": "ar",
        "description": "攻击是否成功",
        "value_range": "值为1(成功),2(失败),3(未知)",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "成功",
            "2": "失败",
            "3": "未知"
        },
        "prompt_template": "这是攻击结果，标识攻击是否成功。",
        "trans_key": "ar"
    },
    "q_body": {
        "name": "q_body",
        "description": "http请求消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http请求消息，这是一个Base64编码的字符串，我们可以对其解码，得到q_body_plain字段。",
        "trans_key": "q_body"
    },
    "r_body": {
        "name": "r_body",
        "description": "http回应消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http回应消息，这是一个Base64编码的字符串，我们可以对其解码，得到r_body_plain。",
        "trans_key": "r_body"
    },
    "domain": {
        "name": "domain",
        "description": "域名",
        "type": "string",
        "prompt_template": "这是域名信息。",
        "trans_key": "domain"
    },
    "event_type": {
        "name": "event_type",
        "description": "告警类型",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志的告警类型。",
        "trans_key": "event_type"
    },
    "alertinfo": {
        "name": "alertinfo",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "protocol_type": {
        "name": "protocol_type",
        "description": "协议类型",
        "value_range": "HTTP/HTTPS",
        "type": "string",
        "prompt_template": "这是协议类型。",
        "trans_key": "protocol_type"
    },
    "uri": {
        "name": "uri",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "block": {
        "name": "block",
        "description": "是否禁用ip",
        "value_range": "0：不启用\n1: 启用IP封禁",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "0": "不启用",
            "1": "启用IP封禁"
        },
        "prompt_template": "这是代表是否禁用ip。",
        "trans_key": "block_info"
    },
}

def clean_info_(raw_info, format_info):
    '''
    这是一个记录了一些详细输出结果的clean_info，需要搭配全局变量来使用，用来记录在某个设备的输出日志在处理时所使用的数据
    :param raw_info:
    :param format_info:
    :return:
    '''
    global good_keys
    new_raw_info = {}
    new_format_info = {}
    if isinstance(raw_info, dict):
        raw_info = [raw_info]
    for point in raw_info:
        for key, value in point.items():
            if key not in key_map or len(str(value).strip()) == 0:
                continue
            format_key_list = key_map[key]["trans_key"]
            if isinstance(format_key_list, str):
                format_key_list = [format_key_list]
            sign = True
            for format_key in format_key_list:
                if format_key in format_info and len(str(format_info[format_key]).strip()) > 0:
                    sign = False
                    if isinstance(value, str) and len(value) > 150:
                        value = value[:150]
                    format_value = format_info[format_key]
                    if isinstance(format_value, str) and len(format_value) > 150:
                        format_value = format_value[:150]
                    new_raw_info[key] = value
                    new_format_info[format_key] = format_value
            if sign:
                ignore_keys.append(key)
            else:
                good_keys.add(key)
    return new_raw_info, new_format_info

# 以下是一个示例
data_dir1 = r"F:\data_process\广西电信_ALL_DATA.tar\DATA"
data_list1 = os.listdir(data_dir1)
all_data = []

good_keys = set([]) # 记录raw中用到的信息
name_list = []
ignore_keys = []
for data_name in data_list1:
    if re.search("IPS", data_name):
        raw_info = None
        with open(data_dir1 + '/' + data_name, 'r', encoding='utf-8') as f:
            for line in f:
                line = json.loads(line.strip())
                if raw_info is None:
                    raw_info = line
                else:
                    raw_info, format_info = clean_info_(raw_info, line)
                    all_data.append({"raw_info": raw_info, "format_info": format_info})
                    raw_info = None

format_keys = set([]) # 记录对应format中用到的信息
for key in good_keys:
    if isinstance(key_map[key]['trans_key'],list):
        for kkk in key_map[key]['trans_key']:
            format_keys.add(kkk)
    else:
        format_keys.add(key_map[key]['trans_key'])
print("raw中涉及的字段包括:{}".format(str(good_keys)))
print("format输出中涉及的字段包括:{}".format(str(format_keys)))
print("未涉及的字段包括:{}".format(str((target-good_keys)-format_keys)))



