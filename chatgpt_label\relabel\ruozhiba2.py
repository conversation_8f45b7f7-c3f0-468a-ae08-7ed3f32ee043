# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm



TEMPLATE0 = """
我会提供给你一个问题，你需要完成以下任务。
1. 一步步思考，仔细分析理解问题中的观点和逻辑。
2. 一步步仔细分析问题中可能存在的语义陷阱或文字游戏，并仔细解释问题中的陷阱，给出纠正的思路。 
    注意：问题中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 回答问题。

问题：{question}

"""

TEMPLATE1 = """
我会提供给你一个问题和一个简单的参考答案，你需要完成以下任务。
你可以综合参考答案的观点和逻辑来分析或思考，但是参考答案可能过于简短或包括错误，请不要直接复制参考答案。
1. 一步步思考，仔细分析理解问题中的观点和逻辑。
2. 一步步仔细分析问题中可能存在的语义陷阱或文字游戏，并仔细解释问题中的陷阱，给出纠正的思路。 
    注意：问题中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 回答问题，请不要直接复制参考答案，给出你自己的答案，或者丰富、优化参考答案。

![注意]: 不要在你的回答中提及参考答案的存在。

问题：{question}

参考答案：{reference}
"""

TEMPLATE2 = """
我会提供给你一个段落，你需要完成以下任务。
1. 一步步思考，仔细分析理解段落中的观点和逻辑。
2. 一步步仔细分析段落中可能存在的语义陷阱或文字游戏，并仔细解释段落中的陷阱，给出纠正的思路。 
    注意：段落中可能存在的陷阱或文字游戏，包括且不限于：语义陷阱、逻辑谬误、逻辑冲突、常识误导、预设谬误、隐喻双关、同词异意、场景替换、因果颠倒、时序颠倒、错误类比、谐音误导、字面主义、结果主义、情境忽视、过程忽视 等等。 
3. 如果段落是问题的形式，回答段落中的问题。

段落：{question}
"""

TEMPLATE3 = """
注意，这个问题中有一个逻辑陷阱、文字游戏或幽默的玩笑，请一步步的分析并指出来。最后再回答问题。
问题：{question}
"""



async def post_request(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        answer = ""
    return answer



async def ask_llm(session, point, semaphore, progress_bar, in_name, out_name, max_retries=5):
    async with semaphore:
        url = "http://10.37.4.4:59052/v1/chat/completions"

        body = {
            "model": "Qwen2.5-72B-Instruct",  # "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            # "frequency_penalty": 0.2,
            "max_tokens": 4000,
            "messages": [
                {"role": "user", "content": point[in_name]},
            ],
            # "repetition_penalty": 1.1,
        }

        for attempt in range(max_retries):
            answer = await post_request(session, url, body)
            point[out_name] = answer
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        progress_bar.update(1)  # 更新进度条
        return point



async def main(data_list, in_name, out_name):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [ask_llm(session, data, semaphore, progress_bar, in_name, out_name) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



all_data = []
with open("/home/<USER>/llm_dataset/ruozhiba/ruozhiba_ruozhiba_ruozhiba.jsonl", 'r', encoding='utf-8') as f:
    for line in f:
        point = json.loads(line)
        point = {
            "instruction": point["instruction"],
            "input": point["input"],
            "output": point["output"]
        }
        all_data.append(point)

with open("/home/<USER>/llm_dataset/ruozhiba/ruozhiba_qa2449_gpt4turbo.json", 'r', encoding='utf-8') as f:
    data_list = json.load(f)

all_data = all_data + data_list

data_list = []
for point in all_data:
        point1 = copy.deepcopy(point)
        point1["prompt"] = TEMPLATE1.replace("{question}", point["instruction"] + "\n" + point["input"]).replace("{reference}", point["output"])
        point1["model_input"] = point1["prompt"]
        data_list.append(point1)

        point2 = copy.deepcopy(point)
        point2["prompt"] = TEMPLATE0.replace("{question}", point["instruction"] + "\n" + point["input"])
        point2["model_input"] = point2["prompt"]
        data_list.append(point2)

        point3 = copy.deepcopy(point)
        point3["prompt"] = TEMPLATE3.replace("{question}", point["instruction"] + "\n" + point["input"])
        point3["model_input"] = point3["prompt"]
        data_list.append(point3)


# with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset/ruozhiba-title-norm.json", 'r', encoding='utf-8') as f:
#     da = json.load(f)
#     for point in da:
#         point["prompt1"] = TEMPLATE2.replace("{question}", point["title"] + "\n" + point["abs"])
#         data_list.append(point)


print("data size: %s" %  len(data_list))
print("data example: ", data_list[0])


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data_list, "prompt", "model_response"))

with open("/home/<USER>/llm_dataset/ruozhiba/ruozhiba_label_4.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

print("Done")



TEMPLATE_n = """
我会提供给你一个问题和对问题的参考分析，你需要参考分析回答问题。记住，不要在回答中提及参考分析的存在\n
问题：{question}
参考分析：{reference}
"""

data_list2 = []
for point in results:
    point = copy.deepcopy(point)
    point["prompt"] = TEMPLATE_n.replace("{question}", point["instruction"] + "\n" + point["input"]).replace("{reference}", point["model_response"])
    point["model_input"] = point["instruction"] + "\n" + point["input"]
    point["model_response"] = ""
    data_list2.append(point)


print("data size: %s" %  len(data_list2))
print("data example: ", data_list2[0])


loop = asyncio.get_event_loop()
results2 = loop.run_until_complete(main(data_list2, "prompt", "model_response"))

with open("/home/<USER>/llm_dataset/ruozhiba/ruozhiba_label_4_answer.json", 'w', encoding='utf-8') as fp:
    json.dump(results2, fp, indent=4, ensure_ascii=False)

print("Done")
