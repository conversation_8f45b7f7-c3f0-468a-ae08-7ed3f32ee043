import json

def bio_to_json(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    sentences = []
    current_sentence = {'sentence': '', 'entity': []}
    current_entity = None
    start_index = 0

    for line in lines:
        line = line.strip()
        if not line:
            if current_entity:
                current_entity['end'] = len(current_sentence['sentence'].split()) - 1
                current_sentence['entity'].append(current_entity)
            if current_sentence['sentence']:
                sentences.append(current_sentence)
            current_sentence = {'sentence': '', 'entity': []}
            current_entity = None
            start_index = 0
        else:
            word, tag = line.split('\t')

            if current_sentence['sentence']:
                current_sentence['sentence'] += ' ' + word
            else:
                current_sentence['sentence'] = word

            if tag.startswith('B-'):
                if current_entity:
                    current_entity['end'] = start_index - 1
                    current_sentence['entity'].append(current_entity)
                current_entity = {'token': word, 'type': tag[2:], 'start': start_index}
            elif tag.startswith('I-'):
                if current_entity:
                    current_entity['token'] += ' ' + word
            else:
                if current_entity:
                    current_entity['end'] = start_index
                    current_sentence['entity'].append(current_entity)
                    current_entity = None

            start_index += 1

    if current_entity:
        current_entity['end'] = start_index - 1
        current_sentence['entity'].append(current_entity)
    if current_sentence['sentence']:
        sentences.append(current_sentence)

    json_data = json.dumps(sentences, indent=4)
    return json_data


# 调用函数并将转换后的JSON写入文件
bio_file_path = "CrossData/science/dev.txt"
json_data = bio_to_json(bio_file_path)
with open("CrossData/science/dev.json", 'w') as file:
    file.write(json_data)



