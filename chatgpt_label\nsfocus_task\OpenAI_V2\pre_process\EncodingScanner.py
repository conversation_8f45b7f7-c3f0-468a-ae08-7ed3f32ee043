# coding:utf-8
import re
import bisect
import itertools

import torch

try:
    from .Utils import *
    from .EncodingEx import *
except:
    from Utils import *
    from EncodingEx import *


# 识别网络流量中的chr、char、String.fromCharCode等混淆模式
# 实际格式可能像“cHr(0x40).ChR(0x69).ChR(0x6e).ChR(0x69).ChR(0x5f)”这样，用“.”、“+”、“|”等符号连接
REGEX_CHAR_OBFUSCATION = re.compile(
    r"(?i)(?<![0-9a-z_])((chr|char|String\.fromCharCode)\s*\(\s*(0x([0-9a-f]{2}){1,2}|[0-9]{1,5})\s*\)\s*[\.\+\|]*)+(?!%(2e|2b|7c))",
    re.IGNORECASE,
)


def _StringToBytes(data, order="big"):
    result_int = None
    if data.startswith("0x"):
        result_int = int(data, 16)
    else:
        result_int = int(data)
    # result_int可能为两个字节，也可能为一个字节
    return result_int.to_bytes(2 if result_int > 255 else 1, order)


def CharObfuscationDecoder(data):
    data = data.lower()
    # 提取所有的十进制或十六进制数字
    numbers = [
        _StringToBytes(i)
        for i in re.findall(
            r"(?i)(?<![0-9a-z_])(0x[0-9a-f]{2,4}|[0-9]{1,5})(?![0-9a-z_])", data
        )
    ]
    # 返回bytes类型
    return b"".join(numbers)


# 如果不包含百分号，则必须位于URL路径或参数中（即，左侧必须是[/?&=]，右侧必须是[/&=]或字符串结束）
# 20231117修改：右侧还得加上单双引号和小于号，以匹配HTML文档中的URL
# 如果不加这个限制，就会把所有包含加号的字符串全都给匹配进去，这显然是不对的
def CheckURLEncode(match, session):
    data = match.group(0)
    return "%" in data or (
        (match.start() > 0 and match.string[match.start() - 1] in "/?&=")
        and (match.end() >= len(match.string) or match.string[match.end()] in "/&='\"<")
    )


# 不易误识别的在前，容易误识别的在后，具体顺序再议
"""
之前的想法是，特征这块儿只需要把各种编码处理一下就行，SIP、DIP等只需以文本形式另外提供即可，不应干扰预训练LLM模型本身的内置知识。
但现在如果不先完成彻底的切词，就不太容易进行乱码检测。而乱码直接进入模型显然是百弊而无一利的。
这块儿需要在不切词的情况下，直接用正则式匹配原来的最终单词，然后直接予以替换
再之后，对chr、char、String.fromCharCode、rot13等编码方法也得想想办法
[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]][([][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]]+[])[!+[]+!+[]+!+[]]+(!![]+[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]])[+!+[]+[+[]]]+([][[]]+[])[+!+[]]+(![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[+!+[]]+([][[]]+[])[+[]]+([][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+([![]]+[][[]])[+!+[]+[+[]]]+(![]+[])[!+[]+!+[]]+(!![]+[])[+[]]+(!![]+[])[!+[]+!+[]+!+[]]+(!![]+[])[+!+[]]])[+!+[]+[+[]]]+(!![]+[])[+!+[]]]([][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]][([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]]((!![]+[])[+!![]]+(!![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+([][[]]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+!![]]+(![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+[+[]]]+([][[]]+[])[!![]+!![]]+(!![]+[])[!![]+!![]+!![]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[!![]+!![]]+(!![]+[])[!![]+!![]+!![]]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]][([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]]((!![]+[])[+!![]]+(!![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+([][[]]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+!![]]+(![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+[+[]]]+([][(!![]+[])[!![]+!![]+!![]]+([][[]]+[])[+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([![]]+[][[]])[+!![]+[+[]]]+(!![]+[])[!![]+!![]+!![]]+(![]+[])[!![]+!![]+!![]]]()+[])[!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(![]+[])[+!![]])()(+!![]+[+[![]]])[+!![]]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]][([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]]((!![]+[])[+!![]]+(!![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+([][[]]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+!![]]+(![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+[+[]]]+([][(!![]+[])[!![]+!![]+!![]]+([][[]]+[])[+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([![]]+[][[]])[+!![]+[+[]]]+(!![]+[])[!![]+!![]+!![]]+(![]+[])[!![]+!![]+!![]]]()+[])[!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(![]+[])[+!![]])()(+[]+[!![]])[!![]+!![]]+(+(+!![]+(!![]+[])[!![]+!![]+!![]]+(+!![])+(+[])+(+[])+(+[]))+[])[+[]]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]][([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]]((!![]+[])[+!![]]+(!![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+([][[]]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+!![]]+(![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+[+[]]]+(![]+[])[+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][(!![]+[])[!![]+!![]+!![]]+([][[]]+[])[+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([![]]+[][[]])[+!![]+[+[]]]+(!![]+[])[!![]+!![]+!![]]+(![]+[])[!![]+!![]+!![]]]()+[])[!![]+!![]])()(+[]+[+[]]+(+[![]])+![])[+!![]]+(+(!![]+!![]+[+[]]+(+[])+(!![]+!![]+!![]+!![]+!![])+(!![]+!![]+!![]+!![]+!![]+!![]+!![])+(!![]+!![])+(!![]+!![]+!![]+!![])+(!![]+!![]+!![])))[(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([]+[])[([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]][([][[]]+[])[+!![]]+(![]+[])[+!![]]+([]+(+[])[([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+([][[]]+[])[+!![]]+(![]+[])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[])[+!![]]+([][[]]+[])[+[]]+([]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[!![]+!![]+!![]]+(!![]+[])[+[]]+(!![]+[][(![]+[])[+[]]+(![]+[])[!![]+!![]]+(![]+[])[+!![]]+(!![]+[])[+[]]])[+!![]+[+[]]]+(!![]+[])[+!![]]])[+!![]+[+!![]]]+(!![]+[])[!![]+!![]+!![]]]](!![]+!![]+!![]+[+[]])+(!![]+[])[!![]+!![]+!![]]+([][[]]+[])[+!![]]+(!![]+[])[+[]])()("alert%281%29"))()
"""
TextSpliters = [
    # ###########考虑一下扫描模式中怎么处理
    # {
    #     # UUID的格式已经非常严格了，根本用不着零宽断言吧，内部各个版本的严格约束也用不着吧...
    #     "pattern": re.compile(
    #         "(?i)[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}", re.ASCII
    #     ),
    #     "callback": lambda match, session: "UUID_%s" % Hash(match.group()),
    # },
    {
        # chr、char、String.fromCharCode等混淆模式
        "name": "Char",
        "pattern": REGEX_CHAR_OBFUSCATION,
        "callback": ("Char混淆", CharObfuscationDecoder),
    },
    {
        "name": "Hex",
        "pattern": EncodingEx.RegexHex,
        "checker": lambda match, session: EncodingEx.HexCheckFormat(match.group())
        > Recognition.Uncertain,
        "callback": ("Hex", EncodingEx.HexDecode),
    },
    {
        "name": "Base64",
        "pattern": EncodingEx.RegexBase64,
        "checker": lambda match, session: EncodingEx.Base64CheckFormat(match.group())
        > Recognition.Uncertain,
        "callback": ("Base64", EncodingEx.Base64DecodeToBinary),
    },
    {
        "name": "XML",
        "pattern": re.compile(
            '(?i)[^&"<>]*(&(%s|#(\d+|x[0-9a-f]+));[^&"<>]*)+'
            % ("|".join(EncodingEx.XMLSymbols)),
            re.ASCII,
        ),
        "callback": ("XML", EncodingEx.XMLDecode),
    },
    {
        "name": "URL",
        # 这是最低限度的URL编码要求了
        "pattern": re.compile(
            # 由于目前实际的URL编码基本上都是用在表单参数，所以适当放宽，以观后效
            # 20231110修改：实际上POST正文中，参数是可以有空格的，但这经常导致请求状态行中的URL解码把“ HTTP/1.1”带上。因此还是去除了空格。
            "(?i)[^\\r\\n\\&\\+=\\?\\s]*((\\%([0-9a-f]{2}|u[0-9a-f]{4})|\\+)[^\\r\\n\\&\\+=\\?\\s]*)+",
            # "(?i)[^\\r\\n\\$\\&\\+,/:;=\\?@]*((\\%([0-9a-f]{2}|u[0-9a-f]{4})|\\+)[^\\r\\n\\$\\&\\+,/:;=\\?@]*)+",
            # 如果只针对可解码部分进行匹配，那么多重编码的情况（比如Base64尾巴上有%3D%3D）将无法正确递归解码。虽然上面的写法有时会匹配到非常长的片段，但这是必要的，只能想其它方法解决了...
            # "(?i)(\\%([0-9a-f]{2}|u[0-9a-f]{4}))([^\\r\\n\\$\\&\\+,/:;=\\?@]*(\\%([0-9a-f]{2}|u[0-9a-f]{4})|\\+))+",
            re.ASCII,
        ),
        "checker": CheckURLEncode,
        "callback": ("URL", EncodingEx.URLDecodeToBinary),
    },
    {
        "name": "Escape",
        # 实际上，很多语言都支持多行字符串，未必不能有换行，但实际攻击中这种情况还是太少见了
        "pattern": re.compile(
            "[^'\"\\\\\\r\\n]*(\\\\([\\\\/abfnrtv'\"0]|%s|x[0-9a-fA-F]{2}|u+[0-9a-fA-F]{4})[^'\"\\\\\\r\\n]*)+"
            % Utils.RegexOctASCII.pattern,
            re.ASCII,
        ),
        "callback": ("Escape", EncodingEx.SlashStripBinary),
    },
]


def MLMScore(logits, labels, attention_mask, entropy_weight=1.0):
    logits_entropy = -logits * torch.log(logits)
    logits_entropy = torch.sum(
        torch.where(
            torch.isnan(logits_entropy),
            torch.zeros_like(logits_entropy),
            logits_entropy,
        ),
        dim=-1,
    )
    reconstruct_probability = torch.gather(
        logits, dim=-1, index=labels.unsqueeze(-1)
    ).squeeze(-1)
    result = reconstruct_probability - entropy_weight * logits_entropy
    return torch.sum(result * attention_mask, dim=-1) / torch.sum(
        attention_mask, dim=-1
    )


class EncodingScanner:
    def __init__(
        self,
        tokenizer=None,
        model=None,
        accelerator=None,
        spliters=None,
        decode_min_length=4,
        binary_mask_min_length=12,
        string_from_binary_min_length=8,  # 注意，这个是字符串对应的字节长度，而不是字符长度
        messy_min_length=8,
        messy_min_tokens=20,
        binary_entropy_limit=5,
        mlm_max_tokens=512,
        mlm_entropy_weight=1.0,
        mlm_score_limit=0.3,
        stringify_omit_max_length=20,
        stringify_omit_min_length=8,
        debugging=False,
    ):
        self.Tokenizer = tokenizer
        self.Model = model
        self.Accelerator = accelerator
        self.Spliters = [{**i} for i in (spliters or TextSpliters)]
        self.DecodeMinLength = decode_min_length
        self.BinaryMaskMinLength = binary_mask_min_length
        self.StringFromBinaryMinLength = string_from_binary_min_length
        self.MessyMinLength = messy_min_length
        self.MessyMinTokens = messy_min_tokens
        self.BinaryEntropyLimit = binary_entropy_limit
        self.MLMMaxTokens = mlm_max_tokens
        self.MLMEntropyWeight = mlm_entropy_weight
        self.MLMScoreLimit = mlm_score_limit
        self.StringifyOmitMaxLength = stringify_omit_max_length
        self.StringifyOmitPrefixLength = stringify_omit_min_length // 2
        self.StringifyOmitSuffixLength = (
            stringify_omit_min_length - self.StringifyOmitPrefixLength
        )
        self.Debugging = debugging

    def GetFinalText(self, data, session):
        return data

    def GetFinalBinary(self, data, session):
        if len(data) < self.BinaryMaskMinLength:
            return data
        return "《此处省略%d个%s字节》" % (
            len(data),
            "不可打印" if self.CheckBinaryEntropyLimit(data) else "高熵",
        )

    def CheckTextEntropyLimit(self, data):
        # 定义重构得分：MLM模型在某个单词位置上原样输出的概率，减去预测分布的信息熵，按行求平均
        # 但该方法对于过短的串（可能由于缺少足够的上下文）会有较大的偏差，因此需要设置一个最小长度
        # 目前看来主要是信息熵在起作用。除此之外，len(data)/len(input_ids)也有一定的作用，但暂时先不考虑
        # 眼下实在是没有足够的样本，不然的话，后续应该针对这些特征另外建模
        if len(data) < self.MessyMinLength:
            return True
        if self.Tokenizer is None or self.Model is None:
            return True
        with torch.inference_mode():
            inputs = self.Tokenizer(data, truncation=True)
            if len(inputs.input_ids) < self.MessyMinTokens:
                return True
            input_ids = torch.tensor([inputs.input_ids])
            attention_mask = torch.tensor([inputs.attention_mask])
            if self.Accelerator:
                input_ids = input_ids.to(self.Accelerator.device)
                attention_mask = attention_mask.to(self.Accelerator.device)
            else:
                input_ids = input_ids.to(self.Model.device)
                attention_mask = attention_mask.to(self.Model.device)
            # input_ids可能超长，这种情况分片处理
            logits = []
            for offset in range(0, input_ids.shape[1], self.MLMMaxTokens):
                logits.append(
                    self.Model(
                        input_ids[:, offset : offset + self.MLMMaxTokens],
                        attention_mask=attention_mask[
                            :, offset : offset + self.MLMMaxTokens
                        ],
                    ).logits
                )
            logits = torch.cat(logits, dim=1)
            logits = torch.softmax(logits, dim=-1)
            mlm_score = MLMScore(
                logits, input_ids, attention_mask, self.MLMEntropyWeight
            )
            if self.Debugging:
                print(
                    f"[DEBUG] MLM score: {repr(data)} {input_ids.shape} -> {mlm_score[0].item()}"
                )
            return mlm_score[0].item() >= self.MLMScoreLimit

    def CheckBinaryEntropyLimit(self, data):
        return (
            len(data) < self.MessyMinLength
            or ByteEntropy(data) < self.BinaryEntropyLimit
        )

    def CheckEntropyLimit(self, data):
        if isinstance(data, Utils.BinaryType):
            return self.CheckBinaryEntropyLimit(data)
        elif isinstance(data, Utils.TextType):
            return self.CheckTextEntropyLimit(data)
        else:
            raise TypeError("data must be binary or text")

    """
    最新方案结合两个旧方案的优点：

    原始载荷：b"AAAAA《块1开始》BBBBB《块1结束》CCCCC《此处省略X个高熵/低熵不可打印字节》XXXXX"
    块1的URL解码：b"DDDDD《块2开始》EEEEE《块2结束》FFFFF"
    块2的Base64解码：b"GGGGG"

    这样修改的主要预期是，首先在涉及编解码时，偏移量的定义可能会比较麻烦（用解码前的也不对，用解码后的也不对）
    其次是偏移量的标识方法可能不太利于使用QKV的Transformer模型进行查询（拍脑门理解，相当于这些偏移量标记的运作完全依赖相对位置编码，其鲁棒性可能不太好）
    所以还是改用插入时的标记。这个方法此前已被证实应该是有效的。虽然这样一来编码块还是不能重叠，但至少保留了解码前的原文，应该能缓解过度解码的问题
    这个方案的输出结构和下面的应该是一样的，但需要添加一下路径搜索，用以消除编码块的重叠

    新方案大致如下：
    1、提取出所有可打印的字符串片段，每个分配一个序号
        不可打印的部分只进行一个总体的长度统计，不再插入simhash片段
            更新：这个长度统计看上去也没什么用，删了
        不可打印的部分后续也应加入如deflate、gzip、序列化块等的处理，但后续再补吧
    2、解码得到的字符串，指明其由前面的序号为N的字符串的[start:end]经过类型X的解码得到

    """

    def CallbackScan(self, name, decoder):
        def callback(match, session):
            begin, end = match.span()
            data = decoder(match.group())
            if self.Debugging:
                print(
                    f"[DEBUG] Decoding {name} {repr(match.group())} {match.span()} -> {repr(data)}"
                )
            if len(data) < self.DecodeMinLength:
                # 如果解码结果太短，则认为解码失败，不计入结果
                return
            children = self._Scan(data, session)
            if not children:
                # 如果解码结果没有子节点，则认为解码失败，不计入结果
                if self.Debugging:
                    print(
                        f"[DEBUG] Ignoring {name} {repr(data)} failed because of no children"
                    )
                return
            if not isinstance(children, dict):
                # 被标记为不可丢弃的，只有ASCII部分的二进制块，它应成为叶子节点
                yield (begin, end, name), {"data": data, "children": {}}
                return
            # 也因此，这里使用yield而非return，以便在解码失败时不会产生任何结果
            if len(children) > 1:
                yield (begin, end, name), {"data": data, "children": children}
            child_key, child_value = children.popitem()
            # 如果解码结果只有一个子节点，且覆盖了整个解码结果，则直接替换掉当前节点
            yield ((begin, end, name), child_value) if child_key[0] == 0 and child_key[
                1
            ] == len(data) else (
                (begin, end, name),
                {
                    "data": data,
                    "children": {child_key: child_value},
                },
            )

        return callback

    # 与SplitWords不同，ScanWords不会对data进行任何处理，而是直接返回一个树状结构。因此，解码块是允许重叠的，不需要spliter_index
    def _Scan(self, data, session=None):
        result = session["Results"].get(data)
        if not result is None:
            if self.Debugging:
                print(f"[DEBUG] Scanning with cache: {repr(data)} -> {result}")
            return result
        if self.Debugging:
            print(f"[DEBUG] Scanning without cache: {repr(data)}")
        result = {}
        if type(data) == Utils.BinaryType:
            dropable = True
            for begin, end, text, encoding in EncodingEx.StringsFromBinary(
                data,
                min(self.StringFromBinaryMinLength, len(data)),
                return_encoding=True,
            ):
                if self.Debugging:
                    print(f"[DEBUG] Text found: {repr(text)} {encoding} {begin} {end}")
                session["Depth"] += 1
                children = self._Scan(text, session)
                assert isinstance(children, dict), str(type(children))
                session["Depth"] -= 1
                if children:
                    if encoding == "ASCII":
                        # ASCII解码结果实际上会包含在repr(data)中，单独生成一个节点没有意义
                        # 而且好在，ASCII全都是单字节编码，ASCII解码结果中的偏移量可以直接对应倒字节偏移量
                        # 因此，对ASCII解码结果的子节点，直接作为当前字节块的子节点即可
                        for (
                            child_begin,
                            child_end,
                            child_encoding,
                        ), child_value in children.items():
                            result[
                                (begin + child_begin, begin + child_end, child_encoding)
                            ] = child_value
                        break
                else:
                    if not self.CheckTextEntropyLimit(text):
                        # 叶子节点不应是乱码
                        continue
                    if encoding == "ASCII" and not (begin == 0 and end == len(data)):
                        # ASCII解码结果实际上会包含在repr(data)中，无子节点且未覆盖整个data的情况下不需要额外输出
                        # 但如果二进制块只有ASCII部分，如果丢弃掉的话，会导致这个二进制块没有任何解码结果，从而被认为是乱码而丢弃
                        # 所以，当二进制块中检出ASCII部分时，应将其标记为不可丢弃
                        dropable = False
                        continue
                result[(begin, end, encoding)] = {
                    "data": text,
                    "children": children,
                }
            if not result and not dropable:
                return True
        else:
            assert isinstance(data, Utils.TextType), str(type(data))
            for spliter in session["Spliters"]:
                pattern = spliter["pattern"]
                checker = spliter.get("checker")
                callback = spliter.get("callback")
                for i in pattern.finditer(data):
                    if self.Debugging:
                        print(
                            f"[DEBUG] Spliter hit: {spliter['name']} {repr(i.group())} {i.span()}"
                        )
                    if checker and not checker(i, session):
                        if self.Debugging:
                            print(
                                f"[DEBUG] Checker failed: {spliter['name']} {repr(i.group())} {i.span()}"
                            )
                        continue
                    if callback:
                        for key, value in callback(i, session):
                            result[key] = value
        if result:
            if self.Debugging:
                print(f"[DEBUG] Cache set: {repr(data)} -> {result}")
            session["Results"][data] = {**result}
        return result

    def CreateSession(self):
        spliters = [{**i} for i in self.Spliters]
        for i in spliters:
            if not callable(i["callback"]):
                i["callback"] = self.CallbackScan(*i["callback"])
        return {
            "Spliters": spliters,
            "Depth": 0,
            "Results": {},
        }

    def Scan(self, data, session=None):
        if session is None:
            session = self.CreateSession()
        result = self._Scan(data, session)
        if not isinstance(result, dict):
            # 整个data只适合ASCII解码，那么由于repr会包含ASCII解码结果，因此直接返回data即可
            result = {}
        # 组装根节点
        if len(result) != 1:
            result = {
                "data": data,
                "children": result,
            }
        else:
            # 如果只有一个子节点，且覆盖整个data，则直接返回子节点
            key, value = result.popitem()
            if key[0] == 0 and key[1] == len(data):
                result = value
            else:
                result = {
                    "data": data,
                    "children": {key: value},
                }
        return result

    def ScanBatch(self, data_map, session=None):
        if session is None:
            session = self.CreateSession()
        return {key: self.Scan(value, session) for key, value in data_map.items()}

    def ReprList(self, data_list, binary=False):
        prefix = 'b"' if binary else '"'
        suffix = '"'

        return (
            prefix
            + "".join(
                (
                    EncodingEx.SlashEscapeText(i)
                    if isinstance(i, Utils.TextType)
                    else EncodingEx.SlashEscapeBinary(i)
                )
                for i in data_list
            )
            + suffix
        )

    def _OmitSplit(self, data, raw):
        if len(data) <= self.StringifyOmitMaxLength or type(data) != type(raw):
            return [(data, False)]
        # 目前data中有很多片段与raw中的片段重复，我们需要对所有长度超过self.StringifyOmitMaxLength的重复片段进行缩略，缩略后的片段用xxx〔...〕yyy表示，其中xxx和yyy的长度之和应为self.StringifyOmitMinLength
        # 为此，需要先运行LCS算法，找出data和raw的最长公共子串；有多个最长公共子串时，选择data中切分最少的那个
        # 不过，逐字符或逐字节跑LCS的话，性能开销有点太大。所以先简单切分一下
        REGEX_SPLIT = (
            re.compile("([a-zA-Z0-9]+)")
            if isinstance(data, Utils.TextType)
            else re.compile(b"([a-zA-Z0-9]+)")
        )
        data_pieces = REGEX_SPLIT.split(data)
        raw_pieces = REGEX_SPLIT.split(raw)
        # 与常规LCS不同，每个状态中需要记录：（长度，切分次数，路径回溯方向）
        # 其中路径回溯方向：1表示上，2表示左，3（即1|2）表示左上，0预留
        # 状态转移方程：dp[i][j] = max(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]+1 if data[i]==raw[j] else 0)
        dp = [
            [(0, 0, 0) for j in range(len(raw_pieces) + 1)]
            for i in range(len(data_pieces) + 1)
        ]
        for i in range(1, len(data_pieces) + 1):
            for j in range(1, len(raw_pieces) + 1):
                # 生成候选解
                next_states = [
                    (dp[i - 1][j][0], dp[i - 1][j][1], 1),
                    (dp[i][j - 1][0], dp[i][j - 1][1], 2),
                ]
                if data_pieces[i - 1] == raw_pieces[j - 1]:
                    next_states.append(
                        (
                            dp[i - 1][j - 1][0] + 1,
                            dp[i - 1][j - 1][1] + 1
                            if dp[i - 1][j - 1][2] != 3
                            else dp[i - 1][j - 1][1],
                            3,
                        )
                    )
                # 选出最优解
                dp[i][j] = max(next_states, key=lambda x: (x[0], -x[1]))
        # 回溯路径，将data整理成不可缩略串（非公共串）和可缩略串（公共串）的混合列表
        i = len(data_pieces)
        j = len(raw_pieces)
        result = []
        temp_result = []
        current_omissible = False
        empty_piece = "" if isinstance(data, Utils.TextType) else b""
        while i > 0 and j > 0:
            if dp[i][j][2] == 1:
                i -= 1
                if current_omissible and temp_result:
                    temp_result.reverse()
                    temp_result = empty_piece.join(temp_result)
                    if temp_result:
                        result.append((temp_result, True))
                    temp_result = []
                    current_omissible = False
                temp_result.append(data_pieces[i])
            elif dp[i][j][2] == 2:
                j -= 1
                # 我们不关心raw中的不可缩略串（非公共串），所以这里不需要处理
            else:
                assert dp[i][j][2] == 3
                i -= 1
                j -= 1
                if not current_omissible and temp_result:
                    temp_result.reverse()
                    temp_result = empty_piece.join(temp_result)
                    if temp_result:
                        result.append((temp_result, False))
                    temp_result = []
                    current_omissible = True
                temp_result.append(data_pieces[i])
        if temp_result:
            temp_result.reverse()
            temp_result = empty_piece.join(temp_result)
            if temp_result:
                result.append((temp_result, current_omissible))
        if i > 0:
            result.append(
                (
                    empty_piece.join(data_pieces[:i]),
                    False,
                )
            )
        result.reverse()
        return result

    def _MarkInsert(self, data_list, mark_list):
        # 从后向前插入所有标记，确保标记位置前后都有足够的前后缀不被缩略
        # 这意味着，如果标记位置位于可缩略串中，则应拆分（甚至在串较短时直接放弃缩略）；如果标记位置位于不可缩略串中，若前后缀原本就够长就仅仅插入即可，否则应扩展前后缀并减少相邻的可缩略串长度
        # 为了确定标记位置，我们需要先计算出所有片段的完整起始位置
        start_position = [0] * (len(data_list))
        for i in range(len(data_list) - 1):
            start_position[i + 1] = start_position[i] + len(data_list[i][0])
        # 从后向前插入标记
        mark_list.sort(key=lambda x: x[0], reverse=True)
        for i, (mark_position, _) in enumerate(mark_list):
            # 二分查找
            # 注意，保存这个结果意义不大，因为后续要对data_list进行修改，所以这个结果也会失效；而持续更新它们的话，开销还不如重新查找
            index = bisect.bisect(start_position, mark_position) - 1
            offset = mark_position - start_position[index]
            # 如果是可缩略串
            if data_list[index][1]:
                if offset > self.StringifyOmitPrefixLength:
                    # 切分前缀
                    data_list.insert(
                        index + 1,
                        (
                            data_list[index][0][
                                offset - self.StringifyOmitPrefixLength :
                            ],
                            True,
                        ),
                    )
                    data_list[index] = (
                        data_list[index][0][: offset - self.StringifyOmitPrefixLength],
                        True,
                    )
                    start_position.insert(
                        index + 1,
                        start_position[index] + offset - self.StringifyOmitPrefixLength,
                    )
                    index += 1
                    offset = self.StringifyOmitPrefixLength
                if offset < len(data_list[index][0]) - self.StringifyOmitSuffixLength:
                    # 切分后缀
                    data_list.insert(
                        index + 1,
                        (
                            data_list[index][0][
                                offset + self.StringifyOmitSuffixLength :
                            ],
                            True,
                        ),
                    )
                    data_list[index] = (
                        data_list[index][0][: offset + self.StringifyOmitSuffixLength],
                        True,
                    )
                    start_position.insert(
                        index + 1,
                        start_position[index] + offset + self.StringifyOmitSuffixLength,
                    )
                # 转化为不可缩略串
                data_list[index] = (data_list[index][0], False)
            # 此时，data_list[index]必定为不可缩略串，但前后缀长度可能不够
            assert not data_list[index][1]
            while offset < self.StringifyOmitPrefixLength and index > 0:
                if (
                    data_list[index - 1][1]
                    and len(data_list[index - 1][0])
                    > self.StringifyOmitPrefixLength - offset
                ):
                    # 切分前一个片段，补到当前片段前面
                    data_list[index] = (
                        data_list[index - 1][0][
                            -self.StringifyOmitPrefixLength + offset :
                        ]
                        + data_list[index][0],
                        False,
                    )
                    data_list[index - 1] = (
                        data_list[index - 1][0][
                            : -self.StringifyOmitPrefixLength + offset
                        ],
                        data_list[index - 1][1],
                    )
                    start_position[index] = start_position[index - 1] + len(
                        data_list[index - 1][0]
                    )
                    offset = self.StringifyOmitPrefixLength
                    break
                else:
                    # 将前一个片段合并到当前片段前面
                    data_list[index] = (
                        data_list[index - 1][0] + data_list[index][0],
                        False,
                    )
                    offset += len(data_list.pop(index - 1)[0])
                    start_position.pop(index)
                    index -= 1
            while (
                len(data_list[index][0]) < offset + self.StringifyOmitSuffixLength
                and index < len(data_list) - 1
            ):
                if (
                    data_list[index + 1][1]
                    and len(data_list[index + 1][0])
                    > self.StringifyOmitSuffixLength - len(data_list[index][0]) + offset
                ):
                    # 切分后一个片段，补到当前片段后面
                    data_list[index] = (
                        data_list[index][0]
                        + data_list[index + 1][0][
                            : self.StringifyOmitSuffixLength
                            - len(data_list[index][0])
                            + offset
                        ],
                        False,
                    )
                    data_list[index + 1] = (
                        data_list[index + 1][0][
                            self.StringifyOmitSuffixLength
                            - len(data_list[index][0])
                            + offset :
                        ],
                        data_list[index + 1][1],
                    )
                    start_position[index + 1] = start_position[index] + len(
                        data_list[index][0]
                    )
                    break
                else:
                    # 将后一个片段合并到当前片段后面
                    data_list[index] = (
                        data_list[index][0] + data_list[index + 1][0],
                        False,
                    )
                    data_list.pop(index + 1)
                    start_position.pop(index + 1)
        # 已经确保所有插入位置都是不可缩略串，且前后缀长度足够，接下来实际进行插入
        # 由于插入标记的类型可能与data不同，因此不能在原地修改，而是创建一个新的列表
        # 由于是从后向前插入，因此不会影响前面的插入位置，所以不再需要修改start_position
        for i, (mark_position, mark_data) in enumerate(mark_list):
            index = bisect.bisect(start_position, mark_position) - 1
            offset = mark_position - start_position[index]
            assert not data_list[index][1]
            if len(data_list[index][0]) > offset:
                data_list.insert(index + 1, (data_list[index][0][offset:], False))
                data_list[index] = (data_list[index][0][:offset], False)
            data_list.insert(
                index + 1, (mark_data, False)
            )  #################这里后续改一下生成式，避免频繁insert
        return data_list

    def _OmitParse(self, data, omit_piece="〔...〕"):
        data, omissible = data
        if omissible and len(data) > self.StringifyOmitMaxLength:
            yield data[: self.StringifyOmitPrefixLength]
            yield omit_piece
            yield data[-self.StringifyOmitSuffixLength :]
        else:
            yield data

    def MarkAndOmit(
        self, data, mark_list=None, raw=None, replace_pieces={"〔": "（", "〕": "）"}
    ):
        result = self._OmitSplit(data, raw) if raw else [(data, False)]
        if not result:
            raise ValueError("Empty data" + repr(data) + "\r\n" + repr(raw))

        def _ReplacePieces(result_item):
            data = result_item[0]
            if not isinstance(data, Utils.TextType):
                return result_item
            omissible = result_item[1]
            for k, v in replace_pieces.items():
                data = data.replace(k, v)
            return (data, omissible)

        result = list(map(_ReplacePieces, result))
        if mark_list:
            result = self._MarkInsert(result, mark_list)
        result = list(itertools.chain.from_iterable(map(self._OmitParse, result)))
        return self.ReprList(result, binary=isinstance(data, Utils.BinaryType))

    def StringifyScanResultWithMark(
        self,
        result,
        raw=None,
        encoding=None,
        block_id=None,
        processed_datas=None,
        title=None,
    ):
        # 将ScanWords的结果转换为文本形式
        # result是ScanWords的返回值
        # 统计所有需要标记的位置
        """
        现在还是不对：
        [DEBUG] Missing cache: 'SELECT+%2A++FROM+%60business_trip%60+WHERE+%60login%60+LIKE+%27zhangbo4%27%0AORDER+BY+%60business_trip%60.%60close_time%60+DESC'
        [DEBUG] Missing cache: 'SELECT+*++FROM+%60business_trip%60+WHERE+%60login%60+LIKE+%27zhangbo4%27%0D%0AORDER+BY+%60business_trip%60.%60close_time%60+DESC'
        这种原始数据不同，但解码结果相同的情况，应该标记出来，类似这样的格式：
        块X经过XXX解码：ZZZ
        块Y经过YYY解码：〔与 块X经过XXX解码 的结果相同〕

        """
        data = result["data"]
        children = result["children"]
        mark_list = []
        sub_results = []
        if block_id is None or processed_datas is None:
            block_id = [0]
            processed_datas = {}
        current_id = block_id[0]
        cache = processed_datas.get(data)
        if cache is None:
            if self.Debugging:
                print(f"[DEBUG] Missing cache: {repr(data)}")
            processed_datas[data] = (current_id, encoding)
            children = list(children.items())
            children.sort(key=lambda x: x[0][0])
            for (begin, end, child_encoding), value in children:
                block_id[0] += 1
                child_id = block_id[0]
                sub_result = list(
                    self.StringifyScanResultWithMark(
                        value,
                        data[begin:end],
                        child_encoding,
                        block_id,
                        processed_datas,
                    )
                )
                if not sub_result:
                    block_id[0] -= 1
                    continue
                sub_results.append(sub_result)
                mark_list.append((begin, f"〔块{child_id}开始〕"))
                mark_list.append((end, f"〔块{child_id}结束〕"))
            data_repr = self.MarkAndOmit(data, mark_list, raw)
        else:
            if self.Debugging:
                print(f"[DEBUG] Cache hit: {repr(data)}")
            data_repr = "〔与 块%d经%s解码 的结果相同〕" % cache
        yield f"{title if title else f'块{current_id}'}{f'经{encoding}解码' if encoding else ''}：{data_repr}"
        for sub_result in sub_results:
            yield from sub_result

    def StringifyScanResultWithMarkBatch(self, result_map):
        block_id = [0]
        processed_datas = {}
        for title, result in result_map.items():
            yield from self.StringifyScanResultWithMark(
                result, block_id=block_id, processed_datas=processed_datas, title=title
            )

if __name__ == "__main__":
    spliter = EncodingScanner()
    result = spliter.Scan(
        b"InRtcC50bXAiO2lmKGlzX2RpcigkSCkpe2lmKEBmd3JpdGUoQGZvcGVuKCIkSC8kTiIsICd3JyksInRtcCIpKXtAdW5saW5rKCIkSC8kTiIpO2VjaG8gc3RyX3JlcGxhY2UoIlxcIiwiLyIsJEgpLiJ8IjskR0xPQkFMU1snSSddPSRHTE9CQUxTWydJJ10rMTt9fX1lY2hvKCJ8PC0iKTtkaWUoKTs%3D&z9=BaSE64%5FdEcOdE&z2=1&z3=100"
    )
    print("\r\n".join(spliter.StringifyScanResultWithMark(result)))