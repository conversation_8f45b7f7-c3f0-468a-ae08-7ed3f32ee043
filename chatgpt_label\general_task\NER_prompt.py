from tabulate import tabulate
import json
import random


type_map = {
    "cluener_public": {
        "address": "地址",
        "book": "书名",
        "company": "公司",
        "game": "游戏",
        "government": "政府",
        "movie": "电影",
        "name": "姓名",
        "organization": "组织机构",
        "position": "职位",
        "scene": "景点"
    },
    "conll2003":{
        "ORG": "",
        "LOC": "",
        "PER": "",
        "MISC": ""
    },
    "wnut16":{
        "company": "",
        "facility": "",
        "loc": "",
        "movie": "",
        "musicartist": "",
        "other": "",
        "person": "",
        "product": "",
        "sportsteam": "",
        "tvshow": ""
    },
    "wnut17":{
        "corporation": "",
        "creative-work": "",
        "group": "",
        "location": "",
        "person": "",
        "product": ""
    },
    "LatticeLSTMdata_resume":{
        "CONT": "国籍",
        "EDU": "教育背景",
        "LOC": "地名",
        "NAME": "人名",
        "ORG": "组织名",
        "PRO": "专业",
        "RACE": "民族",
        "TITLE": "职称"
    },
    "LatticeLSTMdata":{
        "PER": "人名",
        "ORG": "组织",
        "GPE": "地缘政治",
        "LOC": "地名",
    },
    "ChineseNERdata":{
        "PER": "人名",
        "ORG": "组织机构",
        "LOC": "地名",
    },
    "boson":{
        "time": "时间",
        "location": "地点",
        "person_name": "人名",
        "org_name": "组织名",
        "company_name": "公司名",
        "product_name": "产品名"
    }
}


def format_entity_out(entity_list, type_list=None, format="str", add_none_type=False, clean_output=False):
    en_list = {}
    for entity in entity_list:
        if entity['type'] not in entity_list:
            en_list[entity['type']] = []
        en_list[entity['type']].append(entity['token'])

    if type_list is not None:
        en_list = {k:v for k,v in en_list.items() if k in type_list}

    if add_none_type:
        assert type_list is not None
        for entity_type in type_list:
            if entity_type not in en_list:
                en_list[entity_type] = []

    answer_list = [
        "好的，我可以帮您完成实体抽取任务。以下是抽取结果：\n",
        "抽取结果如下：\n",
        "以下是句子中的实体：\n",
        "以下是句子中的实体以及对应的实体类型：\n",
        "好的，我可以帮您找出句子中的实体以及对应的实体类型：\n",
    ]
    if not clean_output:
        out = random.choice(answer_list)
    else:
        out = ""

    if format == "str":
        for entity_type, entity_value in en_list.items():
            if len(entity_value) <= 0:
                entity_value = "None"
            else:
                entity_value = ', '.join(entity_value)
            out += " - %s: %s\n" % (entity_type, entity_value)
    elif format == "json":
        out += "```\n%s\n```\n" % json.dumps(entity_list, ensure_ascii=False, indent=4)
    else:
        table_line = [["实体类型", "实体词"]]
        for entity_type, entity_value in en_list.items():
            if len(entity_value) <= 0:
                entity_value = "None"
            else:
                entity_value = ', '.join(entity_value)
            table_line.append([entity_type, entity_value])
        out += tabulate(table_line, headers="firstrow", tablefmt="pipe")

    return out


def Prompt1(point, entity_type_map):
    question_list = [
        "下面这个句子中包含哪些实体：%s",
        "找出句子中的实体：%s",
        "找出给定句子中的实体。\n句子：%s",
        "现在你要完成实体识别任务，需要协助我找出句子中的实体词和实体类型。\n%s",
        "%s\n找出其中的实体词和实体类型",
        "实体识别：%s",
        "%s\n实体识别",
        "句子：%s\n句子中的实体有：",
        "实体识别任务，从下面这段话中抽取出实体类型和实体词\n%s",
    ]

    type_list = [x for x in entity_type_map.keys()]

    entity_list = []
    entity_value = []
    for entity in point['entity']:
        if entity['token'] in entity_value:
            continue
        map_value = entity_type_map.get(entity['type'], "")
        if len(map_value) <= 0:
            map_value = entity['type']
        entity_list.append(
            {
                "token": entity['token'],
                "type": map_value
            }
        )
        entity_value.append(entity['token'])

    if len(entity_list) <= 0:
        return None

    answer_list = [
        ("", None, "str", False, False),
        ("", None, "table", False, False),
        ("\n结果以表格的形式给出", None, "table", False, False),
        ("\n结果以json的形式给出", None, "json", False, False),

        ("\n定义的实体类型如下：[%s]" % (", ".join(type_list)), type_list, "str", False, False),
        ("\n定义的实体类型如下：[%s]" % (", ".join(type_list)), type_list, "table", False, False),
        ("\n定义的实体类型如下：[%s]\n结果以表格的形式给出，不存在的实体类型结果为None" % (", ".join(type_list)), type_list, "table", True, False),
        ("\n定义的实体类型如下：[%s]\n结果以json的形式给出，不存在的实体类型给出空列表" % (", ".join(type_list)), type_list, "json", True, False),

        ("\n结果以json的形式给出，只输出json结果，不要说其他的", None, "json", False, True),
        ("\n定义的实体类型如下：[%s]\n结果以json的形式给出，不存在的实体类型给出空列表，只输出json结果，不要说其他的" % (", ".join(type_list)), type_list, "json", True, True),
    ]

    question = random.choice(question_list) % point['sentence']
    answer_param = random.choice(answer_list)
    question += answer_param[0]
    answer = format_entity_out(entity_list, answer_param[1], answer_param[2], answer_param[3], answer_param[4])

    prompts = {"question": question,
               "answer": answer}
    return prompts


data_list = [
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\EcomData\conll2003/train.json", "conll2003"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\EcomData\conll2003/dev.json",  "conll2003"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\EcomData\conll2003/test.json",  "conll2003"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut17命名实体识别数据集/train.json", "wnut17"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut17命名实体识别数据集/dev.json", "wnut17"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut17命名实体识别数据集/test.json", "wnut17"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut16命名实体识别数据集/train.json", "wnut16"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut16命名实体识别数据集/dev.json", "wnut16"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\wnut16命名实体识别数据集/test.json", "wnut16"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\cluener_public/train.json", "cluener_public"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\cluener_public/dev.json", "cluener_public"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\cluener_public/test.json", "cluener_public"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\LatticeLSTMdata\LatticeLSTMdata/demo_train.json", "LatticeLSTMdata"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\LatticeLSTMdata\LatticeLSTMdata/demo_dev.json", "LatticeLSTMdata"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\LatticeLSTMdata\LatticeLSTMdata/demo_test.json", "LatticeLSTMdata"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ResumeNER/train.json", "LatticeLSTMdata_resume"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ResumeNER/dev.json", "LatticeLSTMdata_resume"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ResumeNER/test.json", "LatticeLSTMdata_resume"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ChineseNERdata\ChineseNERdata/example_train.json", "ChineseNERdata"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ChineseNERdata\ChineseNERdata/example_dev.json", "ChineseNERdata"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\ChineseNERdata\ChineseNERdata/example_test.json", "ChineseNERdata"),

    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\BosonNERdata/boson_train.json", "boson"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\BosonNERdata/boson_dev.json", "boson"),
    (r"D:\work\bert_pretrain_model\ChatGPT_dataset\nlp_task\NERTask\BosonNERdata/boson_test.json", "boson"),

]

all_data = []

for data_path, data_name in data_list:
    with open(data_path, 'r', encoding='utf-8') as f:
        if data_name == "cluener_public":
            data = []
            for line in f:
                data.append(json.loads(line.strip()))
        else:
            data = json.load(f)

        print("%s data size: %s" % (data_name, len(data)))

        for point in data:
            if data_name == "cluener_public":
                labels = point.get('label', {})
                entity_l = []
                for en_type, en_list in labels.items():
                    for en in en_list:
                        entity_l.append({
                            "token": en,
                            "type": en_type
                        })
                point = {
                    "sentence": point['text'],
                    "entity": entity_l
                }
            prompt = Prompt1(point, type_map[data_name])
            if prompt is not None:
                all_data.append(prompt)

print(len(all_data))


with open('./ner_prompt_data.json', 'w', encoding='utf-8') as fp:
    for line in all_data:
        line = json.dumps(line, ensure_ascii=False)
        fp.write(line + '\n')



