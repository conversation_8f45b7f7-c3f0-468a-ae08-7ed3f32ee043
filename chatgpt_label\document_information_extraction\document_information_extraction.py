"""输入：上传一事一分析报告，自动提取相关关键信息。
输出：自动提炼相关重要信息，以表格形式输出，包括以下内容：
1、事件:天气、停电时间、复电时间、停电时长、线路名称、影响时户数、故障性质（故障跳闸/主动停运/上级电源点故障）。
2、故障概述：一句话概述故障原因（时间、供电所、变电站、线路名称、开关名称、保护动作跳闸名称/接地选停开关名称（含设备动作情况））
3、设备故障原因及相关情况
4、网架、设备及运行管理情况：网架结构情况、设备故障原因及相关情况、运行方式安排及故障处置情况、历史跳闸情况及原因、线路巡视记录。
5、故障线路有/无故障应急处置预案、故障处置流程按照/未按照先复电后抢修进行、运用/未运用配电自动化系统开展故障处置、故障处置的方式安排、转供方案合理/不合理。
6、暴露问题。
7、下一步措施
最终表格字段包括：文档名称、停电事件名称、所属地市、天气、停电时间、复电时间、停电时长、线路名称、影响时户数、
故障性质（故障跳闸/主动停运/上级电源点故障）、故障概述、网架结构薄弱点、设备故障原因及相关情况、故障线路有/无故障应急处置预案、故障处置流程按照/未按照先复电后抢修进行、运用/未运用配电自动化系统开展故障处置、故障处置的方式安排、转供方案合理/不合理、暴露问题、下一步措施。
"""


import os
import json
import pandas as pd
from docx import Document
import requests
import time

fields = {
    "停电事件名称": {"description": "停电事件名称", "default": ""},
    "所属地市": {"description": "所属地市", "default": ""},
    "天气": {"description": "天气", "default": ""},
    "停电时间": {"description": "停电时间", "default": ""},
    "复电时间": {"description": "复电时间", "default": ""},
    "停电时长": {"description": "停电时长", "default": ""},
    "线路名称": {"description": "线路名称", "default": ""},
    "影响时户数": {"description": "影响时户数", "default": ""},
    "故障性质": {"description": "故障性质，包括：故障跳闸/主动停运/上级电源点故障", "default": ""}  ,
    "故障概述": {"description": "一句话概述故障原因（包含时间、供电所、变电站、线路名称、开关名称、保护动作跳闸名称/接地选停开关名称及设备动作情况）", "default": ""},
    "网架结构情况": {"description": "网架结构情况", "default": ""},
    "网架结构薄弱点": {"description": "网架结构薄弱点", "default": ""},
    "设备故障原因及相关情况": {"description": "设备故障原因及相关情况", "default": ""},
    "运行方式安排": {"description": "包括网架、设备、线路的运行方式安排等", "default": ""},
    "故障处置情况": {"description": "故障处置结果、后续步骤、措施安排等情况", "default": ""},
    "历史跳闸情况及原因": {"description": "给出历史的跳闸、停电、事故、异常等全部记录情况，包括停电开始时间、结束时间、原因、类型、范围、时户数等全部信息", "default": ""},
    "线路巡视记录": {"description": "给出历史的线路的巡视、巡查的全部记录，包括巡视日期、范围、人员、工具、结果、单号、整改情况等全部信息", "default": ""},
    "故障线路有/无故障应急处置预案": {"description": "故障线路有/无故障应急处置预案，包括：有/无", "default": ""},
    "故障处置流程按照/未按照先复电后抢修进行": {"description": "故障处置流程按照/未按照先复电后抢修进行，包括：按照/未按照", "default": ""},
    "运用/未运用配电自动化系统开展故障处置": {"description": "运用/未运用配电自动化系统开展故障处置，包括：运用/未运用", "default": ""},
    "故障处置的方式安排": {"description": "包括处理故障的流程、思路、方式、方法、安排、措施等", "default": ""},
    "转供方案合理/不合理": {"description": "转供方案合理/不合理，包括：合理/不合理", "default": ""},
    "暴露问题": {"description": "暴露问题，分条全部列举。如1. 问题1，2. 问题2，3. 问题3。", "default": ""},
    "下一步措施": {"description": "下一步措施，分条全部列举。如1. 措施1，2. 措施2，3. 措施3。", "default": ""},
}

# 读取docx文件内容
def read_docx(file_path):
    try:
        from docx import Document
        from docx.oxml.text.paragraph import CT_P
        from docx.oxml.table import CT_Tbl
        from docx.table import _Cell, Table
        from docx.text.paragraph import Paragraph
        
        doc = Document(file_path)
        full_text = []
        
        # 遍历所有元素
        para_idx = 0
        tbl_idx = 0
        last_element_type = None  # 跟踪上一个元素类型
        
        for element in doc.element.body:
            # 处理段落（包括标题、列表等）
            if element.tag.endswith('p'):
                # 如果从表格转到段落，添加额外换行
                if last_element_type == 'table':
                    full_text.append("\n\n")
                
                paragraph = doc.paragraphs[para_idx]
                para_text = paragraph.text.strip()
                if para_text:
                    # 检查段落样式，可能是标题、列表等
                    style = paragraph.style.name
                    if style.startswith('Heading'):
                        full_text.append(f"【{style}】{para_text}")
                    elif style == 'List Paragraph':
                        full_text.append(f"• {para_text}")
                    else:
                        full_text.append(para_text)
                para_idx += 1
                last_element_type = 'paragraph'
                
            # 处理表格
            elif element.tag.endswith('tbl'):
                table = doc.tables[tbl_idx]
                full_text.append("【表格开始】")
                
                # 获取表格列数，用于格式化
                max_cols = max(len(row.cells) for row in table.rows) if table.rows else 0
                
                # 格式化表格内容
                for row_idx, row in enumerate(table.rows):
                    # 为保持单元格对齐，填充缺失的单元格
                    cells = [cell.text.strip().replace('\n', ' ').replace('\t', ' ') for cell in row.cells]
                    while len(cells) < max_cols:
                        cells.append("")
                    
                    # 使用特殊分隔符，更清晰地划分单元格
                    full_text.append(" | ".join(cells))
                    
                    # 在表头后添加分隔线
                    if row_idx == 0:
                        separator = "-+-".join(["-" * max(len(cell), 1) for cell in cells])
                        full_text.append(separator)
                
                full_text.append("【表格结束】")
                tbl_idx += 1
                last_element_type = 'table'
                
            # 处理其他可能的元素类型（如目录）
            elif element.tag.endswith('sdt'):  # 结构化文档标签，可能包含目录
                # 只在目录前添加额外换行
                full_text.append("\n\n【目录开始】")
                
                # 尝试提取目录内容
                has_content = False
                for child in element.iter():
                    if child.tag.endswith('p'):
                        try:
                            text = child.text_content().strip()
                            if text:
                                full_text.append(f"【目录项】{text}")
                                has_content = True
                        except:
                            continue
                
                if has_content:
                    full_text.append("【目录结束】")
                else:
                    # 如果没有提取到目录内容，删除之前添加的目录开始标记
                    full_text.pop()
                
                last_element_type = 'toc'
        
        # 最终文本采用单换行符连接
        final_text = "\n".join(full_text)
        
        # 合并多个连续的换行符，最多允许三个连续换行符
        import re
        final_text = re.sub(r'\n{4,}', '\n\n\n', final_text)
        
        return final_text
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

# 添加新函数：提取文档中所有事件
def extract_events_with_llm(text, model, api_base, api_key):
    # 构造提示
    prompt = f"""
    请从以下文档中提取所有停电事件名称，并以JSON格式的列表返回。
    如果文档中有多个停电事件，请分别提取每个事件的名称。
    如果文档中没有明确的事件名称，请根据停电线路、时间等信息概括事件名称。
    
    注意！你只需要提取文档的对应的主要事件名称，不要提取文档中其他无关的事件名称，例如历史事件、历史跳闸情况及原因、线路巡视记录等。
    
    文档内容：
    {text}
    
    请直接返回JSON格式的事件名称列表，例如：["事件1", "事件2", "事件3"]
    不要输出任何其他说明或解释，你的输出需要能直接被json.loads()解析。
    """

    max_retries = 5
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_base,
                headers={'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'},
                json={
                    'model': model,
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 1000,
                    'temperature': 0.01,
                    'top_p': 0.01,
                    'stream': False
                }
            )
            response.raise_for_status()
            result = response.json()['choices'][0]['message']['content'].strip()
            
            # 解析返回的JSON字符串为Python列表
            try:
                events_list = json.loads(result)
                if isinstance(events_list, list):
                    return events_list
                else:
                    print(f"LLM返回的不是列表格式: {result}")
                    # 如果不是列表但是字符串，尝试作为单个事件处理
                    if isinstance(result, str):
                        return [result]
                    return []
            except json.JSONDecodeError:
                # 如果不是有效的JSON，可能是单个事件名称
                print(f"无法解析LLM返回结果为JSON: {result}")
                # 尝试处理为单个事件
                if result and not result.startswith('[') and not result.endswith(']'):
                    return [result]
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"LLM API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                print("达到最大重试次数，放弃重试。")
                return []

# 使用LLM提取特定事件的信息
def extract_info_with_llm(text, field_name, field_description, event_name, model, api_base, api_key):
    # 构造提示
    prompt = f"""
    你需要从文档中提取我给你的特定事件相关的特定字段的信息。
    
    事件名称：{event_name}
    需要提取的字段的名称：{field_name}
    需要提取的字段的描述：{field_description}
    
    如果文档中没有关于此事件的该字段的相关信息，请返回：无。
    你只需要返回提取到的信息内容或者单独的无字，直接返回字符串结果即可，不要输出其他的任何说明或解释等内容。

    文档内容：
    {text}
    
    注意！！！
    1、不要输出任何的说明或解释等内容。
    2、只输出与事件 "{event_name}" 相关的需要提取的字段的内容或者单独的无字。
    3、不要输出【表格开始】、【表格结束】、【Heading xxx】等段落、分隔字样。
    4、注意输出该字段的抽取结果的全面性、完整性和准确性，不要遗漏信息。
    """

    max_retries = 5
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_base,
                headers={'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'},
                json={
                    'model': model,
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 1000,
                    'temperature': 0.1,
                    'top_p': 0.1,
                    'stream': False
                }
            )
            response.raise_for_status()
            result = response.json()['choices'][0]['message']['content'].strip()
            return result
        except requests.exceptions.RequestException as e:
            print(f"LLM API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
            else:
                print("达到最大重试次数，放弃重试。")
                return ""

# 处理单个文档
def process_document(file_path, model, api_base, api_key):
    text = read_docx(file_path)
    if not text:
        return []

    file_name = os.path.basename(file_path)
    
    # 首先识别文档中的所有事件
    print(f"  - 正在提取文档中的事件列表...")
    events = extract_events_with_llm(text, model, api_base, api_key)
    
    if not events:
        print(f"  ⚠️  未能从文档中提取到事件列表，尝试作为单个事件处理")
        # 如果没有提取到事件，尝试直接提取停电事件名称
        event_name = extract_info_with_llm(text, "停电事件名称", fields["停电事件名称"]["description"], "", model, api_base, api_key)
        if event_name and event_name != "无":
            events = [event_name]
        else:
            # 如果仍然无法提取，使用文件名作为事件名
            events = []

    print(f"  - 从文档中识别到 {len(events)} 个事件: {events}")
    
    all_extracted_data = []
    
    # 对每个事件分别提取信息
    for event_idx, event_name in enumerate(events):
        print(f"  - 正在处理事件 {event_idx+1}/{len(events)}: {event_name}")
        
        extracted_data = {'文档名称': file_name, '停电事件名称': event_name}
        
        # 提取该事件的所有字段信息
        for field_name, field_value in fields.items():
            if field_name == "停电事件名称":  # 事件名称已经确定
                continue
                
            print(f"    - 正在提取字段: {field_name}")
            extracted_field_value = extract_info_with_llm(text, field_name, field_value['description'], event_name, model, api_base, api_key)
            
            if extracted_field_value and extracted_field_value != "无":
                extracted_data[field_name] = extracted_field_value
            else:
                extracted_data[field_name] = field_value['default']
        
        all_extracted_data.append(extracted_data)
    
    if not all_extracted_data:
        print(f"  ⚠️  处理 {file_path} 失败，LLM未能提取到有效信息。")
    
    return all_extracted_data

# 保存为JSON
def save_to_json(data, output_path):
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"已保存JSON到: {output_path}")
    except Exception as e:
        print(f"保存JSON失败: {e}")

# 保存为CSV
def save_to_csv(data, output_path):
    # 定义最终表格字段
    csv_columns = ['文档名称'] + list(fields.keys())
    try:
        df = pd.DataFrame(data, columns=csv_columns)
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"已保存CSV到: {output_path}")
    except Exception as e:
        print(f"保存CSV失败: {e}")

# 主函数
def main(directory_path, model, api_base, api_key, output_path):
    all_output = []
    all_output_path = os.path.join(output_path, 'all_output.json')
    all_output_csv_path = os.path.join(output_path, 'all_output.csv')
    non_docx_files = []
    failed_files = []

    # 尝试加载已有的 all_output
    if os.path.exists(all_output_path):
        try:
            with open(all_output_path, 'r', encoding='utf-8') as f:
                all_output = json.load(f)
            print(f"  ✅  加载已存在的 all_output 数据，将进行续写。")
        except json.JSONDecodeError:
            print(f"  ⚠️  all_output.json 文件损坏，将重新创建。")
            all_output = []

    for root, _, files in os.walk(directory_path):
        for file in files:
            if not file.endswith('.docx'):
                continue

            file_path = os.path.join(root, file)
            file_name = os.path.basename(file_path)
            
            save_json_path = os.path.join(output_path, file_name + '_extracted.json')
            save_csv_path = os.path.join(output_path, file_name + '_extracted.csv')

            # 检查是否已存在输出文件
            if os.path.exists(save_json_path) and os.path.exists(save_csv_path):
                print(f"  ⏭️  跳过已处理文件: {file} (JSON/CSV已存在)")
                continue

            print(f"正在处理: {file_path}")
            extracted_data_list = process_document(file_path, model, api_base, api_key) # process_document 返回列表
            if extracted_data_list:
                # 保存每个文档的 JSON 和 CSV
                save_to_json(extracted_data_list, save_json_path)
                save_to_csv(extracted_data_list, save_csv_path)
                all_output.extend(extracted_data_list) # 将列表合并到 all_output
            else:
                failed_files.append(file)


    # 保存所有输出
    if all_output: # 只有当 all_output 不为空时才保存
        save_to_json(all_output, all_output_path)
        save_to_csv(all_output, all_output_csv_path)
        print(f"  ✅  已更新 all_output 数据。")
    else:
        print(f"  ⚠️  没有新的数据添加到 all_output。")

    # 打印非 docx 文件列表
    if non_docx_files:
        print("\n  ⚠️  以下文件不是 .docx 格式，已被跳过:")
        for file in non_docx_files:
            print(f"    - {file}")
        print("    请手动将这些文件转换为 .docx 格式后重新运行程序。")

    # 打印处理失败的文件列表
    if failed_files:
        print("\n  ⚠️  以下文件处理失败，LLM未能提取到有效信息:")
        for file in failed_files:
            print(f"    - {file}")
        print("    请检查这些文件内容或稍后重新运行程序。")

    print("\n处理完成")

if __name__ == '__main__':
    # python -m pip install json pandas docx requests -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
    # python document_information_extraction.py
    
    # 只能处理docx文件，其他文件会被跳过。请转换为docx文件后运行。
    # output 文件夹下每个文件都会生成对应的抽取结果的JSON和CSV文件
    # output 文件夹下最后会生成两个文件：all_output.json 和 all_output.csv，分别保存所有事件的抽取结果的JSON和CSV格式。
    # output 中已有文件会被跳过，不会重复处理。

    directory_path = "./docx/"
    model = "secllm-v3"
    api_base = "http://************:8080/v1/chat/completions"
    api_key = "sk-owl-secllm"
    output_path = "./output"
    main(directory_path, model, api_base, api_key, output_path)
    
    