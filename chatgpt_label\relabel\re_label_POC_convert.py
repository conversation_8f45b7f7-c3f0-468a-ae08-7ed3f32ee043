# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import os
import yaml





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Athene-V2-Chat", #"Mistral-Large-Instruct-2407",  #"secllm-v2",  #"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 80  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



print("read data...")
with open("/home/<USER>/nuclei-POC/relabel_poc_gen.json", 'r', encoding='utf-8') as fp:
    poc_data = json.load(fp)

# 按照yaml解析poc_content字段，过滤出所有的一级标签
# ['dns', 'network', 'headless', 'self-contained', 'ssl', 'http', 'javascript', 'file', 'tags', 
#  'htmlhint', 'id', 'requests', 'flow', 'info', 'code', 'tcp', 'variables']

use_keys = ["tags", "id", "requests", "info", "variables", "http", "tcp"]

raw_data = []
for poc in tqdm(poc_data):
    try:
        poc_content = poc["poc_content"]
        poc_yaml = yaml.safe_load(poc_content)
        poc_keys = list(poc_yaml.keys())
        for key in poc_keys:
            if key not in use_keys:
                break
        else:
            raw_data.append(poc)

    except Exception as e:
        print(e)
        continue

print("\n\ndata size: %s" %  len(raw_data))




sys_prompt_gen_vuln_desc = """
你需要根据漏洞分析和nuclei中对应漏洞检测的POC配置文件，参考EZ检测平台的POC编写文档，编写EZ检测平台上的POC规则文件。

=============    以下是 EZ检测平台 的 POC编写文档    =============
## 1.EZ PoC字段名说明

|序号|字段名|类型|Yaml对应名称|是否必选|说明|
|---|---|---|---|---|---|
|1|name|string|name|是|POC名称，限定由三种字符构成：小写字母、数字、-。<br>例如：poc-yaml-vbulletin-rce-cve-2020-17496|
|2|Type|int|type|否|定义检测POC的扫描类型，参数级或目录级，取值范围：<br>PERFILETYPE = 5<br>PERFOLDERTYPE = 1<br>PERSCHEMETYPE = 2<br>PERHOST = 3<br>PERDNS = 4|
|3|Protocol|string|protocol|否|协议，取值范围：HTTP、TCP、UDP，默认为HTTP|
|4|Level|Int|level|是|POC对应漏洞的风险等级，<br>0：低危（指纹提示、轻微的信息泄露、登录页面等，通常不开发该风险等级的指纹）<br>1：中危（可间接获取权限或信息的漏洞，如SQL注入、管理员密码或密码hash泄露、后台默认口令、ssrf等）<br>2 ：高危（有一定条件的getshell，例如：需要暴力破解key、需要等待管理员触发等）<br>3：严重（可直接getshell或RCE的漏洞，如：Struts2-045、fastjson、shiro反序列化RCE等）|
|5|Finger|string|finger|否|POC所对应指纹，若该字段被定义，则当且仅当匹配到对应指纹后才会发送POC检测。<br>取值有两种形式：<br>- "vbulletin" in finger.name （完全匹配）<br>- finger.name.lcontains("vbulletin") （搜索匹配）|
|6|PocCode|string|pocCode|否|唯一值，默认不需要填写，EZ会自动生成，生成方式是先把yaml md5，然后取前4位。|
|7|Set|map[string]string|set|否|定义在POC中使用的变量，通常用于生成随机数和随机字符串。例如：<br>set:<br>r1: randomInt(8000, 10000)<br>r2: randomInt(8000, 10000)|
|8|Rules|[]red.Rules|rules|否|POC检测规则字段，由一系列检测规则组合而成，检测规则取值详见表二。|
|9|Groups|map[string][]red.Rules|groups|否|由若干个Rules组合而成，检测采用“或”关系运算，即只要其中某一个Rules满足检测逻辑就报告漏洞。通常Rules与Groups需要至少填写一项。|
|10|Detail|Detail|detail|否|定义PoC的描述信息，详见表三|

## Rules字段说明：

|序号|字段名|类型|Yaml对应名称|是否必选|说明|
|---|---|---|---|---|---|
|1|Method|string|method|是|请求方式，Web漏洞POC取值通常包括：GET、POST、PUT、TRACE、MOVE等，TCP类型POC取值固定为TCP。|
|2|Path|string|path|否|请求路径，在Web漏洞POC中必须填写，例如：“/index”|
|3|Headers|map[string]string|headers|否|请求头，由一组请求头构成，指定发送POC时除默认请求头以外附加的请求头参数，例如：<br>headers:<br>Cookie: ctr_t=0; sid=123456789<br>Content-Type: application/json|
|4|Body|int|body|是|请求体，指定发送POC时的请求体部分，形式允许任意填写。例如：<br> 1. urlencode 类型：a=1&b=2<br>2. JSON类型：{"a":1,"b":2}<br>3. XML类型: \<xml> \<a>1\</a> \<b>2\</b> \</xml>|
|5|Search|string|search|否|定义从响应包中需要提取的变量，通过正则表达式的P方法将提取到的内容存储在变量中， 如下案例，将响应包中隐藏的input标签中连续的32位字符提取并储存在token变量。 例如：<br>search: <input\stype="hidden"\sname="(?P\<token>\S{32})"|
|6|FollowRedirects|bool|follow_redirects|否|是否跟随跳转，当取值为1时，表示跟随跳转，此时会检测跳转后的页面响应内容。当取值为0时，表示不跟随跳转，此时仅检测当前响应内容，尽管响应头为301、302也仍然仅会检测当前响应。|
|7|Expression|string|expression|否|命中POC的条件，通常由一串逻辑表达式组成<br>例如：<br>expression: \|<br>response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)<br>表示当匹配到响应头状态200且响应内容命中所示正则时，完成此次匹配。<br>当缺省Expression字段时，会默认直接匹配该检测项，若POC中仅有一条检测项，且expression为空，则会直接报告漏洞存在。|

## Detail字段说明：

|序号|字段名|类型|Yaml对应名称|是否必选|说明|
|---|---|---|---|---|---|
|1|Author|string|author|否|插件开发作者|
|2|Links|[]string|links|否|相关链接，提供一组能够描述漏洞原理、详情或复现方式的参考链接|
|3|Description|string|description|否|漏洞简要描述，可以直接填写与漏洞有关的描述信息。|
|4|TvulId|int|tvul_id|是|与M-SEC对应的漏洞唯一编号，Web类POC由1-6位数字表示：<br>如：91846 可关联社区漏洞：https://msec.nsfocus.com/vuln/detail/2024-91846|

## Response字段说明：

|序号|字段名|类型|Yaml对应名称|是否必选|说明|
|---|---|---|---|---|---|
|1|Headers|map[string]string|author|否|headers，匹配响应头参数，如： response.headers["content-type"].contains() 。<br>注意响应头全部小写。|
|2|Body|byte|body|否|响应体，通常匹配形式如下： response.body.bcontains(b"for 16-bit app support")|
|3|Content_type|string|content_type|否|响应content-type字段，可以用headers["content-type"]代替。|
|4|Status|int|status|否|响应状态码，例如：response.status==200|
|5|ReqRaw|string|reqRaw|否|请求原始内容，字符串形式|
|6|RespRaw|string|respRaw|否|响应原始内容，字符串形式|
|7|Body_md5|string|body_md5|否|响应体md5值|
|8|Icon_hash|string|icon_hash|否|图标hash，计算方法与fofa一致|

## 2.POC开发

POC，Proof of Concept。是指用于证明漏洞存在所进行的检测。POC规则一般是通过发送一个或多个具有先后顺序的HTTP请求，根据网站页面返回情况的不同来判断网站存在漏洞与否。特殊地，对于无回显的漏洞，一般通过盲打的检测方法，需要依赖DNS log、Curl log等。

POC文件名称的命名通常的形式为：组件名称-漏洞类型-cve(cnvd)-xxxx-xxxx.yml， 例如：vBulletin-rce-cve-2020-17496.yml。
对于有cve或cnvd编号的漏洞，建议在命名时写清编号。此处是cve-2020-17496。
组件名称 如：Discuz、Tomcat、vBulletin等。此处是vBulletin。
漏洞类型通常是：rce、sqli、xss、ssrf、fileread等。此处是rce。

EZ的POC开发基于YMAL格式，分为几个部分： name、level、finger、set、rules、detail。

整体结构如下：
```yaml
name: poc-yaml-vBulletin-rce-cve-2020-17496
level: 3
finger: |
  "vbulletin" in finger.name
set: 
  r1: randomInt(8000, 10000)
  r2: randomInt(8000, 10000)
rules:
  - method: POST
    path: /ajax/render/widget_tabbedcontainer_tab_panel
    body: subWidgets[0][template]=widget_php&subWidgets[0][config][code]= echo {{r1}}*{{r2}}; exit;
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1*r2)))
detail:
  author: ez
  links:
    - https://www.jianshu.com/p/9b8d9a505ace
```

2.1 name与文件名基本一致，在前面添加poc-yaml-即可。需要注意的是，name直接影响了 ez webscan --pocs [name] 语法的检索，故命名时需要谨慎。

2.2 level表示该POC的严重程度。

2.3 finger 表示该POC适用于的指纹，也就是当命中哪个或哪些指纹时发送该POC，若希望不通过任何指纹匹配强制发送该POC，则可将该字段移除（这样做会引起EZ发包量增加，不建议）。YMAL默认缩进格式为两个空格。

finger定义的指纹分为两类，1是精确匹配，2是模糊匹配。 
 - 精确匹配例如：
```yaml
finger: |
  "vbulletin" in finger.name
```
是会严格匹配指纹名称是否等于vbulletin，相等才会发送POC。 

 - 而模糊匹配：
```yaml
finger: |
  finger.name.lcontains("seeyon") 
```
会将指纹中含有seeyon的关键词，含有及表示命中。

2.4 set字段是我们在发送请求前预设的一些变量，通常用于生成若干个随机数变量，如：
```yaml
set: 
  r1: randomInt(8000, 10000)
  r2: randomInt(8000, 10000)
```
 - 这里定义了两个变量r1和r2，取值随机与8000到10000之间。随机变量在同一个POC请求中的值是固定的。使用随机数可以减小POC检测的固化因素影响，同时还可以在一定程度上对抗扫描检测系统。

POC 编写时，set 部分会根据 Key 排序，建议其它变量前缀和第一个变量名保持一致，如：
```yaml
set:
  reverse: newReverse()
  reverseURL: reverse.url
  reverseDNS: reverse.dns
  url: newUrl()
  urlPath: url.path
```
对于请求较为复杂（如文件上传），或请求中含有不可见字符时，建议采用hex编码的方式编写POC，例如：
```yaml
set:
  s1: hexDecode("2d2d62xxx6e7428")
```
可以将变量与上传包混合在一起，上传的内容使用hexDecode函数封装，这样避免POC中的0x0a、0x0d在Windows与Linux（MacOS）平台出现差异。

2.5 rules，是整个POC的核心。 其中rules 可以是一个单一的请求，也可以是一系列复合请求（groups）。

我们先来介绍单一请求的情况，在一个请求中包含： method、path、headers（非必须）、body（非必须）、expression 几个常用字段。 

2.5.1 method 表示请求方法，通常情况下 method 为 GET或POST。

2.5.2 path定义了该请求发送的URL路径。

2.5.3 headers 表示请求头，在某些情况下需要登录或设置特殊的User-Agent、Content-Type等，这时我们需要通过headers参数来设置。 如：
```yaml
headers:
  Cookie: ctr_t=0; sid=123456789
  Content-Type: application/json
```

2.5.4 body 表示请求体，在POST方法下，我们往往还需要指定body。

对于 `application/x-www-form-urlencoded` 的情况，我们可以直接写：
```yaml
body: arg1=1&arg2=name&arg3……;
```

而对于xml或其他格式的情况，我们需要按下面的方法来定制化body：
```yaml
body: |-
  <?xml version="1.0" encoding="utf-8"?>
    <soapenv:Envelope
xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:wsa="http://www.w3.org/2005/08/addressing"
xmlns:asy="http://www.bea.com/async/AsyncResponseService">
    <soapenv:Header>
      <wsa:Action>xx</wsa:Action>
      <wsa:RelatesTo>xx</wsa:RelatesTo>
      <work:WorkContext
                …………
```

2.5.5 expression 表示漏洞情况的匹配规则，可通过返回状态、返回内容等维度来综合判断。

多个检测逻辑直接支持使用 && 和 || 来表示且和或的关系。 response.status 可匹配返回状态码，如： `response.status == 200`

response.body.bcontains() 支持以bytes格式匹配返回内容，判断返回内容中是否含有关键词aaa可使用： `response.body.bcontains(b'aaa')` 匹配二进制字节时可使用：
```yaml
content_type.contains('application/octet-stream') && body.bcontains(b'\x00\x01\x02')
```

当使用随机数变量匹配结果时，可使用： `response.body.bcontains(bytes(string(r1*r2)))` 。使用随机数需要注意的是：尽量不使用加法操作，因为加号也是URL编码中空格的编码表示。 使用乘法运算时尽量让两个随机数变量取值控制在10000以内，因为乘积过大可能会导致整型溢出，带来结果匹配不到，召回率下降。 当需要匹配返回包Content-Type时，可使用： `response.content_type.contains("application/json")`

希望匹配其他响应头部字段时，可参考：
```yaml
response.headers["set-cookie"].contains("user=admin") 
```

此外，还可借助反连平台编写POC，例如： `reverse.wait(5)` 表示监测反连平台5秒内是否有收到数据。如果5秒内，反连平台收到符合要求的请求，则 `reverse.wait(5)` 返回true，漏洞存在；如果5秒内反连平台没有收到请求，则 `reverse.wait(5)` 返回false，漏洞不存在。

对于复合请求的POC编写，可在rules中添加多个分组，例如：
```yaml
rules:
  - method: GET
    path: /
    headers:
      Content-Type: application/x-www-form-urlencoded
    follow_redirects: true
    expression: |
      response.status == 200
    search: <input\stype="hidden"\sname="(?P<token>\S{32})"
  - method: POST
    path: /
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      username=user&{{token}}=1&password=xxx
    follow_redirects: true
    expression: |
      response.body.bcontains(bytes(r1 + "%" + r2))
```
该POC，先发送了GET请求请求路径为根目录，然后若GET请求返回的响应状态码为200，则进入后续流程，再发送POST请求，匹配两个随机数取余后的结果。 在这个POC中比较特殊的一点是，第二个请求需要依赖第一个请求响应内容中的Token，可在第一个请求中使用search字段匹配出该Token， `?P<token>`表示将匹配结果赋值于token变量。这样，在第二个请求中我们就可以直接使用{{token}}来引用第一个请求匹配出的Token结果了。

2.6 最后一个detail字段主要记录了作者简介以及漏洞详情的URL等，该字段类似于注释，并不会影响漏洞检测效果，在填写漏洞链接是，需要遵循links参数规则，分多个条目依次填写即可。

## 4.EZ YAML POC支持的函数列表

### 4.1 contains 字符串包含
```
// 字符串A 是否包含 字符串B, 返回值 bool
A.contains(B)
例如
"Abc".contains("bc") --> true
```

### 4.2 bcontains 字节包含
```
// 字节A 是否包含 字节B，返回值 bool
A.bcontains(B)
例如
b"Abc".bcontains(b"bc")) --> true
```

### 4.3 matches 字符串正则匹配
```
// 正则表达式A(字符串类型) 是否可以匹配到 字符串B，返回值 bool
A.matches(B)
例如
"\d+\w{3}".matches("123Abc") -> true
```

### 4.4 versionCompare 版本号比较
```
// 字符串A版本号 比较 字符串B版本号，返回数字
// 1 代表 A版本 比 B版本 大
// 2 代表 A版本 比 B版本 小
// 3 代表 A版本 与 B版本 相等
A.versionCompare(B)
例如
"1.2.3".versionCompare("1.2.4") -> 2
```

### 4.5 bmatches 字节正则匹配
```
// 字符串类型的正则表达式A 匹配 字节类型的B
"A".bmatches(b"B")
例如
"\d+\w{3}".matches(b"123Abc") -> true
```

### 4.6 md5加密
```
// 将字符串A进行md5加密，返回字符串
md5(A) -> string
例如
md5("admin") -> 21232f297a57a5a743894a0e4a801fc3
```

### 4.7 upperCase 字符串全大写
```
// 将字符串A的字母转变为全大写
upperCase("A") -> string
例如
upperCase("abc") -> ABC
```

### 4.8 randomInt 随机数字生成
```
// 随机生成一个生成从A到B范围的数字
randomInt(A, B) -> int
例如
randomInt(10, 20) -> 15
```

### 4.9 randomLowercase 随机字符串生成
```
// 随机生成一个A长度的字符串
randomLowercase(A) -> string
例如
randomLowercase(6) -> ezcool
```

### 4.10 base64 Base64编码 字符串/字节
```
// 将字符串A / 字节A 进行base64编码
base64("A") -> string
base64(b"A") -> string
例如
base64("admin") -> YWRtaW4=
base64(bytes("admin")) -> YWRtaW4=
```

### 4.11 base64Decode Base64解码 字符串/字节
```
// 将字符串A / 字节A 进行base64解码
base64Decode("A") -> string
base64Decode(b"A") -> string
例如
base64Decode("YWRtaW4=") -> admin
base64Decode(bytes("YWRtaW4=")) -> admin

### 4.12 hexDecode 十六进制字符串解码
```
// 将字符串A进行十六进制的解码
hexDecode("A") -> string
例如
hexDecode("61646d696e") -> admin
```

### 4.13 urlencode 字符串/字节的url编码
```
// 将字符串A / 字节A 进行URL编码
urlencode("A") -> string
urlencode(b"A") -> string
例如
urlencode(">>>") -> %3e%3e%3e
urlencode(b">>>") -> %3e%3e%3e

### 4.14 urldecode 字符串 / 字节的解码
```
// 将字符串A / 字节A 进行URL编码
urldecode("A") -> string
urldecode(b"A") -> string
例如
urldecode("%3e%3e%3e") -> >>>
urldecode(b"%3e%3e%3e") -> >>>
```

### 4.15 substr 字符串截取
```
// 将字符串A 从B位置开始取C长度的字符串
substr("A", B, C) -> string
例如
substr("abcd", 1, 2) -> bc
```

### 4.16 ts 获取当前时间戳
```
// 获取当前时间戳 等价于十进制 `time.Now().Unix()`
ts()
例如
ts() -> 1718284800
```

### 4.17 extraVersionNum 提取版本号
```
// 提取字符串A中的第B个版本号 (可能会匹配到多个，由B来确认匹配到的位置)
extraVersionNum(A,1) -> string
例如
extraVersionNum("(1.2.3.2.3),(4.5.6)", 2) -> 4.5.6
```

### 4.18 icontains 忽略大小写是否包含
```
// 字符串A 与 字符串B 进行忽略大小写包含判断
"A".icontains("B") -> bool
例如
"aBc".icontains("Ab") -> true
```

### 4.19 strlen 字符串长度
```
// 字符串A的长度
strlen("A") -> int
例如
strlen("abc") -> 3
```

### 4.20 bytelen 字节长度
```
// 字节A的长度
bytelen(b"A") -> int
例如
bytelen(b"abc") -> 3
```

### 4.21 replace 字符串替换
```
// 将 字符串A 中的 B字符串 替换为 C字符串
"A".replace("B", "C") -> string
例如
"abcool".replace("ab", "ez") -> ezcool
```

=============    以下是 漏洞分析    =============
{vuln_desc_answer}

=============    以下是 nuclei中对应漏洞检测的POC配置文件    =============
{poc_content}

=============    任务描述    =============
根据 [漏洞分析] 编写一个编写EZ检测平台上的POC规则文件。首先给出详细的编写思路，然后给出具体的POC内容，最后给出POC的说明分析。
！注意，你可以参考 [nuclei中对应漏洞检测的POC配置文件]，你需要删除 [nuclei中对应漏洞检测的POC配置文件] 中有，但是 [漏洞分析] 中没有的信息。
！注意，编写POC的所有信息都只能来自于 [漏洞分析]，不要使用 [nuclei中对应漏洞检测的POC配置文件] 中的信息。
！注意，在你的回答中，不要提及任何 [nuclei中对应漏洞检测的POC配置文件] 的存在。
！注意，你的名字是SecLLM，不要提及任何千问(Qwen)相关的信息。
"""

for poc in raw_data:
    poc["poc_gen_answer"] = poc["answer"]
    vuln_desc_answer = poc["vuln_desc_answer"]
    poc_content = poc["poc_content"]
    sys_prompt = sys_prompt_gen_vuln_desc.replace("{vuln_desc_answer}", vuln_desc_answer).replace("{poc_content}", poc_content)
    poc["prompt"] = sys_prompt

print("\n\ndata size: %s" %  len(raw_data))
print("\ndata example: \n\n%s" % raw_data[0])


raw_data = raw_data[:100]

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(raw_data))

print("Done")


with open("/home/<USER>/nuclei-POC/relabel_poc_vuln_desc_convert.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)


