{"UTS": {"入侵防护日志": {"msgtype": {"name": "msgtype", "description": "发送给ISOP的消息类型", "value_range": "13：入侵检测（吸星）\n14：入侵检测（zealot）\n17：DDos\n21：webshell\n31489：nti威胁情报\n63：SDK防病毒\n100：自定义情报\n101：自定义规则\n110：内网主机扫描\n130：翻墙行为告警\n131：违法网站告警\n132：违规应用告警", "type": "int", "is_enum": true, "enum_map": {"13": "入侵检测（吸星）", "14": "入侵检测（zealot）", "17": "DDos", "21": "webshell", "31489": "nti威胁情报", "63": "SDK防病毒", "100": "自定义情报", "101": "自定义规则", "110": "内网主机扫描", "130": "翻墙行为告警", "131": "违法网站告警", "132": "违规应用告警", "109": "钓鱼邮件告警"}, "prompt_template": "这是安全日志的消息类型", "trans_key": "msgtype"}, "hash": {"name": "hash", "description": "发送消息的设备hash", "type": "string"}, "product": {"name": "product", "description": "产品标识", "value_range": "uts", "type": "string", "is_enum": true, "enum_map": {"uts": "uts"}}, "dev_ip": {"name": "dev_ip", "description": "发送消息的设备ip", "type": "string"}, "sid": {"name": "sid", "description": "会话id", "value_range": "计算：snowflake_id()", "type": "string"}, "alert_type": {"name": "alert_type", "description": "告警类型", "value_range": "值为”startrack”,”zealot”，“webshell”,”nti”,”uts”,”tac”,” custom_rule”", "type": "string"}, "sip": {"name": "sip", "description": "源IP", "type": "string", "prompt_template": "这是告警事件的源IP地址", "trans_key": "sip"}, "sport": {"name": "sport", "description": "源端口", "type": "int", "prompt_template": "这是告警事件的源端口", "trans_key": "sport"}, "dip": {"name": "dip", "description": "目的IP", "type": "string", "prompt_template": "这是告警事件的目的IP地址", "trans_key": "dip"}, "dport": {"name": "dport", "description": "目的端口", "type": "int", "prompt_template": "这是告警事件的目的端口，这里是%s，归一化后的字段为dport", "trans_key": "dport"}, "protocol": {"name": "protocol", "description": "协议", "type": "int", "is_enum": true, "enum_map": {"6": "HTTP", "17": "UDP"}, "prompt_template": "这是告警事件的协议类型", "trans_key": "protocol"}, "app": {"name": "app", "description": "应用id", "type": "int"}, "direct": {"name": "direct", "description": "当前会话的方向", "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）", "type": "int", "is_enum": true, "enum_map": {"1": "内向外", "2": "外向内", "3": "内对内", "4": "其他"}, "prompt_template": "这是告警事件的流量方向", "trans_key": "direction"}, "user_name": {"name": "user_name", "description": "", "type": "string"}, "action": {"name": "action", "description": "根据规则应该采取的动作", "value_range": "在使用时该值经过和0x00414000进行与操作后，值为0(允许),0x00010000(旁路阻断),0x00004000(阻断),0x00400000(隔离)", "type": "int"}, "acted": {"name": "acted", "description": "实际所完成的动作", "type": "int", "prompt_template": "这是根据规则设备实际采取的动作", "trans_key": "acted_action"}, "smac": {"name": "smac", "description": "源mac", "type": "string"}, "dmac": {"name": "dmac", "description": "目的mac", "type": "string"}, "group": {"name": "group", "description": "对应组", "type": "int"}, "module": {"name": "module", "description": "对应模块", "type": "int"}, "ruleid": {"name": "ruleid", "description": "规则ID", "type": "int"}, "alertlevel": {"name": "alertlevel", "description": "告警级别", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}, "prompt_template": "这是日志事件的告警级别", "trans_key": "alertlevel"}, "classification": {"name": "classification", "description": "威胁分类", "value_range": "取值见《威胁分类对照表》", "type": "int"}, "reliability": {"name": "reliability", "description": "置信度", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}}, "cve_id": {"name": "cve_id", "description": "cve值", "value_range": "仅入侵检测漏洞含有", "type": "string"}, "vid": {"name": "vid", "description": "vlan id", "value_range": "", "type": "int"}, "last_times": {"name": "last_times", "description": "持续发生的次数", "value_range": "如事件归并后，则为告警时归并的次数", "type": "int"}, "timestamp": {"name": "timestamp", "description": "事件发生的时间", "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）", "type": "int", "prompt_template": "这是日志事件发生的时间戳", "trans_key": "timestamp"}, "msel": {"name": "msel", "description": "事件发生的时间的毫秒数", "value_range": "", "type": "int"}, "strategy": {"name": "strategy", "description": "命中的策略编号", "value_range": "", "type": "int"}, "szonename": {"name": "szonename", "description": "源安全区名称", "value_range": "", "type": "string"}, "dzonename": {"name": "dzonename", "description": "目的安全区名称", "value_range": "", "type": "string"}, "rawlen": {"name": "rawlen", "description": "报文长度", "value_range": "", "type": "int", "prompt_template": "这是报文的长度", "trans_key": "payload_len"}, "rawinfo": {"name": "rawinfo", "description": "原始报文信息", "value_range": "base64编码\n包含SDK防病毒日志中的md5信息", "type": "string", "prompt_template": "这是原始报文信息", "trans_key": "payload"}, "msg": {"name": "msg", "description": "存放对应该日志的描述", "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;", "type": "string", "prompt_template": "这是日志事件的消息内容，同时可以由此判断日志对应的事件类型", "trans_key": ["log_message", "log_type"]}, "ar": {"name": "ar", "description": "攻击是否成功", "value_range": "值为1(成功),2(失败),3(未知)", "type": "int", "is_enum": true, "enum_map": {"1": "成功", "2": "失败", "3": "未知"}, "prompt_template": "这是攻击结果", "trans_key": "ar"}, "ds": {"name": "ds", "description": "协议解码现场", "value_range": "只取前4096个字节，并对这4096个字节进行base64编码", "type": "string"}, "dhcpmac": {"name": "dhcpmac", "description": "dhcp dhcp-req-chaddr", "value_range": "", "type": "string"}, "card": {"name": "card", "description": "网卡名", "value_range": "类似于G1/1", "type": "string"}, "src_asset": {"name": "src_asset", "description": "资产id", "value_range": "", "type": "int"}, "dst_asset": {"name": "dst_asset", "description": "资产id", "value_range": "", "type": "int"}, "iscdnip": {"name": "iscdnip", "description": "", "value_range": "dst_asset不为0，则其值为1，dst_asset为0，则其值为0", "type": "int"}, "r_code": {"name": "r_code", "description": "响应码", "value_range": "200", "type": "int"}, "q_body": {"name": "q_body", "description": "http请求消息", "value_range": "base64编码（4096字节）", "type": "string", "prompt_template": "这是日志事件的http请求消息", "trans_key": "q_body"}, "r_body": {"name": "r_body", "description": "http回应消息", "value_range": "base64编码（4096字节）", "type": "string", "prompt_template": "这是日志事件的http回应消息", "trans_key": "r_body"}, "proxy_info": {"name": "proxy_info", "description": "xff转换前ip", "value_range": "ip地址字符串，填写http xff转换中的所有ip地址。", "type": "string"}, "judge_ret": {"name": "judge_ret", "description": "Uts研判结果", "value_range": "0：未研判；\n1：攻击尝试；\n2：高度可疑攻击；\n3：攻击成功；\n4：失陷。", "type": "int", "is_enum": true, "enum_map": {"0": "未研判", "1": "攻击尝试", "2": "高度可疑攻击", "3": "攻击成功", "4": "失陷"}}, "n_cont_length": {"name": "n_cont_length", "description": "http响应方向body长度", "value_range": "", "type": "int"}}, "web安全日志": {"msgtype": {"name": "msgtype", "description": "发送给ISOP的日志消息类型", "value_range": "3480", "type": "int"}, "hash": {"name": "hash", "description": "发送消息的设备hash", "type": "string"}, "product": {"name": "product", "description": "产品标识", "value_range": "uts", "type": "string", "is_enum": true, "enum_map": {"uts": "uts"}}, "dev_ip": {"name": "dev_ip", "description": "发送消息的设备ip", "type": "string"}, "action": {"name": "action", "description": "根据规则应该采取的策略动作", "value_range": "0：允许 ，1：放过。 2：block， 3：接受（等同于0）， 4：重定向", "type": "int", "is_enum": true, "enum_map": {"0": "允许", "1": "放过", "2": "block", "3": "接受（等同于0）", "4": "重定向"}}, "src_ip": {"name": "src_ip", "description": "源IP", "type": "string"}, "src_port": {"name": "src_port", "description": "源端口", "type": "int"}, "dst_ip": {"name": "dst_ip", "description": "目的IP", "type": "string"}, "dst_port": {"name": "dst_port", "description": "目的端口", "type": "int"}, "domain": {"name": "domain", "description": "域名", "type": "string"}, "event_type": {"name": "event_type", "description": "告警类型", "value_range": "", "type": "int"}, "count_num": {"name": "count_num", "description": "匹配次数", "value_range": "", "type": "int"}, "alertinfo": {"name": "alertinfo", "description": "告警信息", "value_range": "", "type": "string"}, "protocol_type": {"name": "protocol_type", "description": "协议类型", "value_range": "HTTP/HTTPS", "type": "string"}, "country": {"name": "country", "description": "", "value_range": "", "type": "string"}, "stat_time": {"name": "stat_time", "description": "时间戳", "value_range": "", "type": "int"}, "method": {"name": "method", "description": "匹配次数", "value_range": "", "type": "int"}, "http": {"name": "http", "description": "HTTP请求或者响应信息", "value_range": "", "type": "string"}, "protect_id": {"name": "protect_id", "description": "防护对象id", "value_range": "", "type": "int"}, "characters": {"name": "characters", "description": "匹配特征", "value_range": "", "type": "string"}, "alertlevel": {"name": "alertlevel", "description": "告警级别", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}}, "uri": {"name": "uri", "description": "", "value_range": "", "type": "string"}, "block_info": {"name": "block_info", "description": "禁封信息", "value_range": "", "type": "string"}, "rule_id": {"name": "rule_id", "description": "规则id", "value_range": "", "type": "int"}, "classification": {"name": "classification", "description": "威胁分类", "value_range": "取值见《威胁分类对照表》", "type": "int"}, "reliability": {"name": "reliability", "description": "置信度", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}}, "rule_name": {"name": "rule_name", "description": "规则名称", "value_range": "", "type": "string"}, "block": {"name": "block", "description": "是否禁用ip", "value_range": "0：不启用\n1: 启用IP封禁", "type": "int", "is_enum": true, "enum_map": {"0": "不启用", "1": "启用IP封禁"}}, "policy_id": {"name": "policy_id", "description": "匹配策略", "value_range": "", "type": "int"}, "policy_name": {"name": "policy_name", "description": "策略名称", "value_range": "", "type": "string"}, "policy_desc": {"name": "policy_desc", "description": "策略描述", "value_range": "", "type": "string"}, "wci": {"name": "wci", "description": "浏览器标识", "value_range": "", "type": "string"}, "wsi": {"name": "wsi", "description": "会话标识", "value_range": "", "type": "string"}, "proxy_info": {"name": "proxy_info", "description": "代理信息", "value_range": "ip地址字符串，填写http xff转换中的所有ip地址。", "type": "string"}, "correlation_id": {"name": "correlation_id", "description": "访问日志与安全日志关联ID", "value_range": "", "type": "int"}, "site_name": {"name": "site_name", "description": "站点名称", "value_range": "", "type": "string"}, "vsite_name": {"name": "vsite_name", "description": "虚拟站点名称", "value_range": "", "type": "string"}, "sid": {"name": "sid", "description": "会话id", "value_range": "", "type": "string"}, "r_code": {"name": "r_code", "description": "响应码", "value_range": "200", "type": "int"}, "q_body": {"name": "q_body", "description": "http请求消息", "value_range": "base64编码", "type": "string"}, "r_body": {"name": "r_body", "description": "http回应消息", "value_range": "base64编码", "type": "string"}, "judge_ret": {"name": "judge_ret", "description": "攻击研判结果", "value_range": "0：未研判；\n1：攻击尝试；\n2：高度可疑攻击；\n3：攻击成功；\n4：失陷。", "type": "int", "is_enum": true, "enum_map": {"0": "未研判", "1": "攻击尝试", "2": "高度可疑攻击", "3": "攻击成功", "4": "失陷"}}, "card": {"name": "card", "description": "网卡信息", "value_range": "", "type": "string"}, "n_cont_length": {"name": "n_cont_length", "description": "http响应方向body长度", "value_range": "", "type": "int"}}, "钓鱼邮件告警日志": {"msgtype": {"name": "msgtype", "description": "发送给ISOP的消息类型", "value_range": "109", "type": "int"}, "hash": {"name": "hash", "description": "发送消息的设备hash", "type": "string"}, "product": {"name": "product", "description": "产品标识", "value_range": "uts", "type": "string", "is_enum": true, "enum_map": {"uts": "uts"}}, "dev_ip": {"name": "dev_ip", "description": "发送消息的设备ip", "type": "string"}, "sid": {"name": "sid", "description": "会话id", "value_range": "计算：snowflake_id()", "type": "string"}, "alert_type": {"name": "alert_type", "description": "告警类型", "value_range": "值为”uts”", "type": "string"}, "sip": {"name": "sip", "description": "源IP", "type": "string"}, "sport": {"name": "sport", "description": "源端口", "type": "int"}, "dip": {"name": "dip", "description": "目的IP", "type": "string"}, "dport": {"name": "dport", "description": "目的端口", "type": "int"}, "protocol": {"name": "protocol", "description": "协议", "type": "int"}, "app": {"name": "app", "description": "应用id", "type": "int"}, "direct": {"name": "direct", "description": "当前会话的方向", "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）", "type": "int", "is_enum": true, "enum_map": {"1": "内向外", "2": "外向内", "3": "内对内", "4": "其他"}}, "user_name": {"name": "user_name", "description": "", "type": "string"}, "action": {"name": "action", "description": "根据规则应该采取的动作", "value_range": "在使用时该值经过和0x00414000进行与操作后，值为0(允许),0x00010000(旁路阻断),0x00004000(阻断),0x00400000(隔离)", "type": "int"}, "acted": {"name": "acted", "description": "实际所完成的动作", "type": "int"}, "smac": {"name": "smac", "description": "源mac", "type": "string"}, "dmac": {"name": "dmac", "description": "目的mac", "type": "string"}, "group": {"name": "group", "description": "对应组", "type": "int"}, "module": {"name": "module", "description": "对应模块", "type": "int"}, "ruleid": {"name": "ruleid", "description": "规则ID", "type": "int"}, "alertlevel": {"name": "alertlevel", "description": "告警级别", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}}, "classification": {"name": "classification", "description": "威胁分类", "value_range": "取值见《威胁分类对照表》", "type": "int"}, "reliability": {"name": "reliability", "description": "置信度", "value_range": "1.高\n2.中\n3.低", "type": "int", "is_enum": true, "enum_map": {"1": "高", "2": "中", "3": "低"}}, "vid": {"name": "vid", "description": "vlan id", "value_range": "", "type": "int"}, "last_times": {"name": "last_times", "description": "持续发生的次数", "value_range": "如事件归并后，则为告警时归并的次数", "type": "int"}, "timestamp": {"name": "timestamp", "description": "事件发生的时间", "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）", "type": "int"}, "msel": {"name": "msel", "description": "事件发生的时间的毫秒数", "value_range": "", "type": "int"}, "strategy": {"name": "strategy", "description": "命中的策略编号", "value_range": "", "type": "int"}, "szonename": {"name": "szonename", "description": "源安全区名称", "value_range": "", "type": "string"}, "dzonename": {"name": "dzonename", "description": "目的安全区名称", "value_range": "", "type": "string"}, "rawlen": {"name": "rawlen", "description": "报文长度", "value_range": "", "type": "int"}, "rawinfo": {"name": "rawinfo", "description": "原始报文信息", "value_range": "base64编码", "type": "string"}, "msg": {"name": "msg", "description": "存放对应该日志的描述", "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;", "type": "string"}, "ar": {"name": "ar", "description": "攻击是否成功", "value_range": "值为1(成功),2(失败),3(未知)", "type": "int"}, "ds": {"name": "ds", "description": "协议解码现场", "value_range": "只取前4096个字节，并对这4096个字节进行base64编码", "type": "string"}, "dhcpmac": {"name": "dhcpmac", "description": "dhcp dhcp-req-chaddr", "value_range": "", "type": "string"}, "card": {"name": "card", "description": "网卡名", "value_range": "类似于G1/1", "type": "string"}, "src_asset": {"name": "src_asset", "description": "资产id", "value_range": "", "type": "int"}, "dst_asset": {"name": "dst_asset", "description": "资产id", "value_range": "", "type": "int"}, "iscdnip": {"name": "iscdnip", "description": "", "value_range": "dst_asset不为0，则其值为1，dst_asset为0，则其值为0", "type": "int"}, "r_code": {"name": "r_code", "description": "响应码", "value_range": "200", "type": "int"}, "q_body": {"name": "q_body", "description": "http请求消息", "value_range": "base64编码（2048字节）", "type": "string"}, "r_body": {"name": "r_body", "description": "http回应消息", "value_range": "base64编码（2048字节）", "type": "string"}, "proxy_info": {"name": "proxy_info", "description": "xff转换前ip", "value_range": "ip地址字符串，填写http xff转换中的所有ip地址。", "type": "string"}, "judge_ret": {"name": "judge_ret", "description": "Uts研判结果", "value_range": "0：未研判；\n1：攻击尝试；\n2：高度可疑攻击；\n3：攻击成功；\n4：失陷。", "type": "int", "is_enum": true, "enum_map": {"0": "未研判", "1": "攻击尝试", "2": "高度可疑攻击", "3": "攻击成功", "4": "失陷"}}, "n_cont_length": {"name": "n_cont_length", "description": "http响应方向body长度", "value_range": "", "type": "int"}}}}