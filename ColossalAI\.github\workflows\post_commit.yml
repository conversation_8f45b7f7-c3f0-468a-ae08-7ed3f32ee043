name: post-commit

on:
  pull_request:
    types:
        - closed

jobs:
  # this job will run after a PR is merged to run pre-commit on any changed file
  # so that the user does not need to learn pre-commit and pre-commit can still
  # be auto-executed by the workflow
  pre-commit:
    runs-on: ubuntu-latest
    if: github.event.pull_request.merged == true && github.repository == 'hpcaitech/ColossalAI'
    steps:
    - uses: actions/checkout@v2
      with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

    # the PR branch and the hpcaitech/colossal-ai main branch
    # must share a common commit, we need to locate that commit,
    # which is the commit checked-out or forked when the PR branch is created
    # such that we can look for files changed since that commit
    - name: Locate base commit
      id: locate-base-sha
      run: |
          curBranch=$(git rev-parse --abbrev-ref HEAD)
          commonCommit=$(git merge-base origin/main $curBranch)
          echo $commonCommit
          echo "baseSHA=$commonCommit" >> $GITHUB_OUTPUT

    - name: Find the changed files
      id: find-changed-files
      uses: tj-actions/changed-files@v35
      with:
        base_sha: ${{ steps.locate-base-sha.outputs.baseSHA }}

    - name: List all changed files
      run: |
        for file in ${{ steps.find-changed-files.outputs.all_changed_files }}; do
          echo "$file was changed"
        done

    # check out the main branch
    - uses: actions/checkout@v2
      with:
        ref: 'main'

    - uses: actions/setup-python@v3

    - name: Cache pre-commit hooks
      uses: actions/cache@v3
      with:
        path: ~/.cache/pre-commit
        key: ${{ runner.os }}-pre-commit-hooks

    - name: Set up pre-commit
      run: |
        pip install pre-commit
        pre-commit install

    # run pre-commit on changed files
    - name: Run Pre-commit
      run: |
        for file in ${{ steps.find-changed-files.outputs.all_changed_files }}; do
          pre-commit run --files $file || true
        done

    # create commit for pre-commit
    # when all files are well formatted, there is no need to create a commit
    # therefore, this step will produce an error, which should be allowed
    - name: Create commits
      id: commit
      continue-on-error: true
      run: |
        git config --global user.name 'github-actions'
        git config --global user.email '<EMAIL>'
        git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}
        git add -A
        git commit -am "[format] applied code formatting on changed files in pull request ${{ github.event.pull_request.number }}"

    # create pull request
    - name: Create Pull Request
      if: steps.commit.outcome == 'success'
      id: cpr
      uses: peter-evans/create-pull-request@v4
      with:
        branch: pre-commit-${{ github.event.pull_request.number }}
        title: "[format] applied code formatting on changed files in PR ${{ github.event.pull_request.number }}"

    - name: Enable Auto-merge for the New PR
      if: steps.commit.outcome == 'success'
      uses: peter-evans/enable-pull-request-automerge@v2
      with:
        pull-request-number: ${{ steps.cpr.outputs.pull-request-number }}
        merge-method: squash
