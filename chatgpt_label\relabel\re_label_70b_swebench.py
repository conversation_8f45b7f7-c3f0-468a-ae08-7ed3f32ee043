# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset




prompt = """You are a gitlab code review bot, and your task is to review the diff code of the merge request and give a code review opinion. 
- You need to review each modified file individually, provide concise and refined review comments. 
- Your review should include changes, suggestions, problems, bugs, efficiencies, grammatical errors, etc. 
- If there are no issues, bugs, etc., you can skip this review. 
- Utilize it in conclusion to provide a concise overview. 
- Please use chinese to reply. 
- Please do not list the all modification points one by one! Please keep the review comments as concise and precise as possible!"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://*********:59052/v1/chat/completions"


        messages = [
            {"role": "system",
             "content": prompt},
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Qwen2.5-72B-Instruct",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 150  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


data = []

data2 = load_dataset("parquet", data_files="/home/<USER>/llm_dataset/SWE-bench/data/train-00000-of-00001.parquet")
data2 = data2["train"]
for i, point in enumerate(data2):
    point["system"] = prompt
    point["prompt"] = "Problem Statement: \n" + point["problem_statement"] + "\n\nPull Request: \n" + point["patch"]
    point["data_source"] = "SWE-bench"
    point["data_index"] = i
    data.append(point)


print("data size: %s" %  len(data))
print("data example: ", data[0])

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/SWE-bench/re_label_swe_bench_qwen25.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

