# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07



import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
import torch
import yaml
import math
import numpy as np
from transformers import AutoConfig, AutoModelForCausalLM, AutoTokenizer
from datasets import load_from_disk, load_dataset , Dataset
import datasets
import re
# from dataset_util import construct_dataset
# from trainer import Trainer
# from dataset_util import EvalDataset
import random
import os
import json
random.seed(42)

prompt_1 = {
"PREFIX": """Respond to the human as helpfully and accurately as possible. You have access to the following tools:""",

"FORMAT_INSTRUCTIONS": """Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).

Valid "action" values: "Final Answer" or {tool_names}

Provide only ONE action per $JSON_BLOB, as shown:

```
{{{{
  "action": $TOOL_NAME,
  "action_input": $INPUT
}}}}
```

Follow this format:

Question: input question to answer
Thought: consider previous and subsequent steps
Action:
```
$JSON_BLOB
```
Observation: action result
... (repeat Thought/Action/Observation N times)
Thought: I know what to respond
Action:
```
{{{{
  "action": "Final Answer",
  "action_input": "Final response to human"
}}}}
```""",

"SUFFIX": """Begin! Reminder to ALWAYS respond with a valid json blob of a single action. Use tools if necessary. Respond directly if appropriate. Format is Action:```$JSON_BLOB```then Observation:.
Thought:"""
}


prompt_2 = {
"PREFIX": """以尽可能详细、有帮助和准确的方式回复用户或完成用户指定的任务。你可以使用工具来帮助回答用户的问题或完成用户指定的任务，你也可以直接回复用户。
你可以使用以下工具：""",

"FORMAT_INSTRUCTIONS": """----

首先会给你用户问题（Question），然后你的任务开始。

在每一步中，您都需要给出你的思考（Thought），分析现在的状态，以及要回复用户的问题下一步该做什么。
然后调用工具（Action）来实际执行您的步骤。
每一步调用工具结束后，您将获得调用工具的结果（Observation），然后你将会分析你现在的状态，然后决定下一步该做什么...
经过多次思考和调用工具（Thought-Action-Observation）的步骤后，你最后知道了如何回复用户或完成了用户指定的任务，然后你就可以给出你的最终答案（Final Answer）。

----

你可以通过json blob的形式调用指定的工具，json块中必须包括两个key，分别是"action"（调用工具的名称）和"action_input"（调用工具的输入）

使用以下格式：
Question: [你需要回答的问题或指令]
Thought: [你需要分析现在的状态，以及要回复用户的问题下一步该做什么]
Action:
```
{{{{
  "action": $TOOL_NAME, [调用工具的名称，必须是：[Final Answer, {tool_names}] 中的一个]
  "action_input": $INPUT, [调用工具的输入]
}}}}
```

记住:
1.每次只能输出一个Action，调用一个工具，请始终提供有效的单一的（Action）来回应。
2.如果需要使用工具，则输出调用工具的（Action）。如果已经能回答用户的问题，则直接调用“Final Answer”，输出回应用户的最终回复。
3.所有的“Thought”都很简短，最多5句话。
4.总是在任务结束时调用“Final Answer”工具。最终的答案应该包含足够的信息来展示给用户，如果你不能处理这个任务，或者你发现工具调用总是失败（工具现在无效），请使用工具（Final Answer），回复我放弃了，重新开始任务。

----""",


"SUFFIX": """开始！
你可以通过json blob的形式调用指定的工具，json块中必须包括两个key，分别是"action"（调用工具的名称）和"action_input"（调用工具的输入）

输出格式为: 
Thought: [你需要分析现在的状态，以及要回复用户的问题下一步该做什么]
Action:
```
{{{{
  "action": $TOOL_NAME, [调用工具的名称]
  "action_input": $INPUT, [调用工具的输入]
}}}}
```
"""

}


prompt_3 = {
"PREFIX": """以尽可能有益和准确的方式回应用户。您可以使用以下工具：""",

"FORMAT_INSTRUCTIONS": """使用 json blob 指定工具，提供一个action key（工具名称）和一个action_input key（工具输入）。.

有效的“action”值: “Final Answer”或{tool_names}

每个$JSON_BLOB只提供一个动作，如下所示：

```
{{{{
  "action": $TOOL_NAME,
  "action_input": $INPUT
}}}}
```

按照此格式：

Question: 输入要回答的问题
Thought: 考虑之前和之后的步骤,分析现在的状态，以及要回应用户下一步该做什么
Action：
```
$JSON_BLOB
```
Observation: 调用工具的结果
... (重复 Thought/Action/Observation N 次)
Thought: 我知道如何回应
Action: 
```
{{{{
  "action": "Final Answer",
  "action_input": "回应用户的最终回复"
}}}}
```""",

"SUFFIX": """开始！记住始终提供有效的单一动作的 json blob 来回应。如果需要使用工具，则输出调用工具的 json blob。如果已经能回答用户的问题，则直接调用Final Answer，输出回应用户的最终回复。
输出格式为: 
Thought: 
Action: 
```
$JSON_BLOB
```
"""

}


def clean_conversation(in_point):
    raw_point = copy.deepcopy(in_point)
    conversation = raw_point['conversations']
    new_point = {
        "id": raw_point["id"]
    }
    if (conversation[0]['from'] != "system") or (conversation[1]['from'] != "user"):
        new_point['conversations'] = []
        return new_point

    use_conversation = [
        conversation[0],
        conversation[1]
    ]

    # 去掉重新启动循环的
    for point in conversation[2:]:
        if point['from'] == "user":
            break
        use_conversation.append(point)
        if "\"return_type\": \"give_answer\",\n  \"final_answer\":" in point['value']:
            break

    # 去掉最后非assistant的部分
    while (len(use_conversation) > 0) and (use_conversation[-1]['from'] != "assistant"):
        use_conversation = use_conversation[:-1]
    if len(use_conversation) == 0:
        new_point['conversations'] = []
        return new_point

    # 增加工具列表
    tools_str = use_conversation[0]['value']
    tools_str = tools_str.split("you have access to the following APIs: ")[-1].strip().split(
        ", {'name': 'Finish', 'description':")[0].strip() + "]"

    tools_re = tools_str.replace("': \"", "': '").replace("\": '", "': '").\
        replace("\", '", "', '").replace("', \"", "', '"). \
        replace("}, \"", "}, '").replace("], \"", "], '"). \
        replace("\": {", "': {").replace("\": [", "': [").\
        replace("{\"", "{'").replace("\"}", "'}"). \
        replace("[\"", "['").replace("\"]", "']"). \
        replace("\"", "\\\"").\
        replace("': '", "\": \"").replace("', '", "\", \""). \
        replace("}, '", "}, \"").replace("], '", "], \""). \
        replace("': {", "\": {").replace("': [", "\": [").\
        replace("{'", "{\"").replace("'}", "\"}"). \
        replace("['", "[\"").replace("']", "\"]").\
        replace("': ", "\": ").replace("\\'", "'").replace(" True", " true").replace(" False", " false")

    # tools_re = tools_re.replace("': \"", "': '").replace("\", '", "', '").replace("\"", "\\\""). \
    #     replace("\\'", "[||||]").replace(" 's", "[--0--]").replace("['s", "[--1--]").replace("{'s", "[--2--]").\
    #     replace("'s", "[||||]s").replace("[--0--]", " 's").replace("[--1--]", "['s").replace("[--2--]", "{'s"). \
    #     replace("'", "\"").replace("[||||]", "'")
    try:
        tools_list = json.loads(tools_re)
    except:
        tools_list = []

    # 工具名称后面增加tool后缀
    if len(tools_list) > 0:
        # change_tool_name = random.choice([0, 1])
        change_tool_name = 1
        new_point['have_tool_list'] = 1
    else:
        change_tool_name = 0
        new_point['have_tool_list'] = 0

    if change_tool_name == 1:
        tool_name_list = [x['name'] for x in tools_list]
        for point in use_conversation:
            value = point['value']
            for tool_name in tool_name_list:
                value = value.replace(tool_name, tool_name + "_tool")
            point['value'] = value

        tools_str = use_conversation[0]['value']
        tools_str = tools_str.split("you have access to the following APIs: ")[-1].strip().split(
            ", {'name': 'Finish', 'description':")[0].strip() + "]"
        tools_re = tools_str.replace("': \"", "': '").replace("\": '", "': '"). \
            replace("\", '", "', '").replace("', \"", "', '"). \
            replace("}, \"", "}, '").replace("], \"", "], '"). \
            replace("\": {", "': {").replace("\": [", "': ["). \
            replace("{\"", "{'").replace("\"}", "'}"). \
            replace("[\"", "['").replace("\"]", "']"). \
            replace("\"", "\\\""). \
            replace("': '", "\": \"").replace("', '", "\", \""). \
            replace("}, '", "}, \"").replace("], '", "], \""). \
            replace("': {", "\": {").replace("': [", "\": ["). \
            replace("{'", "{\"").replace("'}", "\"}"). \
            replace("['", "[\"").replace("']", "\"]"). \
            replace("': ", "\": ").replace("\\'", "'").replace(" True", " true").replace(" False", " false")
        tools_list = json.loads(tools_re)

    new_point['tool_list'] = tools_list

    # 修改系统prompt
    # use_prompt = random.choice([prompt_1, prompt_2, prompt_3])
    use_prompt = prompt_1
    system_prompt = use_prompt["PREFIX"] + "\n\n"
    if change_tool_name == 1:
        for tool in tools_list:
            system_prompt = system_prompt + tool['name'] + ': ' + tool['description'] + ", args: " + str(tool['parameters']) + "\n"
    else:
        system_prompt = system_prompt + tools_str + "\n"
    system_prompt = system_prompt + "\n" + use_prompt["FORMAT_INSTRUCTIONS"].replace("{tool_names}", ", ".join([x['name'] for x in tools_list])) + "\n"
    system_prompt = system_prompt + "\n" + use_prompt["SUFFIX"] + "\n\nQuestion: " + use_conversation[1]['value']

    # 去掉重复的工具调用
    repeat_conversation = [
        {
            "from": "user",
            "value": system_prompt
        }
    ]
    add_function = False
    use_function = set()
    for point in use_conversation[2:]:
        if point['from'] == "function":
            point['value'] = "Observation: " + point['value']
            if add_function:
                repeat_conversation.append(point)
                add_function = False
            else:
                continue
        else:
            assistant_out = point['value']
            action_input = assistant_out.split("Action Input:")[-1].strip()
            try:
                action_input = json.loads(action_input)
            except:
                new_point['conversations'] = []
                return new_point
            action_func = assistant_out.split("Action Input:")[0].strip().split("Action:")[-1].strip()
            action_thought = assistant_out.split("Action:")[0].strip().split("Thought:")[-1].strip()

            if action_func == "Finish":
                action_func = "Final Answer"
                action_input = action_input.get("final_answer", "give_up_and_restart")

            value = {"action": action_func, "action_input": action_input}
            value = json.dumps(value, indent=4, ensure_ascii=False)
            value = "Thought: " + action_thought + "\n" + "Action: \n```\n" + value + "\n```"

            use_key = action_func + "\n" + json.dumps(action_input, indent=4, ensure_ascii=False)
            if use_key in  use_function:
                add_function = False
                continue
            else:
                use_function.add(use_key)
                add_function = True
                repeat_conversation.append(
                    {
                        "from": "assistant",
                        "value": value,
                        "action_func": action_func,
                        "action_input": action_input,
                        "action_thought": action_thought
                    }
                )
    # 去掉最后非assistant的部分
    while (len(repeat_conversation) > 0) and (repeat_conversation[-1]['from'] != "assistant"):
        repeat_conversation = repeat_conversation[:-1]
    if len(repeat_conversation) == 0:
        new_point['conversations'] = []
        return new_point
    new_point["conversations"] = repeat_conversation

    # 增加是否有final answer的标识
    if repeat_conversation[-1]['action_func'] == "Final Answer":
        new_point["have_final_answer"] = 1
    else:
        new_point["have_final_answer"] = 0
    return new_point


with open("/home/<USER>/llm_dataset/ToolBench/new_data/toolllama_G123_dfs_train.json", 'r', encoding='utf-8') as fp:
    tool_data = json.load(fp)

print("raw data size: ", len(tool_data))

all_data = []
final_data = []
for point in tqdm(tool_data):
    new_point = clean_conversation(point)
    if len(new_point['conversations']) > 0:
        all_data.append(new_point)
        if new_point['have_final_answer'] == 1:
            final_data.append(new_point)

print("clean data size: ", len(all_data))
print("have final data size: ", len(final_data))


with open("/home/<USER>/llm_dataset/ToolBench/new_data/clean_toolbench_train.json", 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

with open("/home/<USER>/llm_dataset/ToolBench/new_data/clean_toolbench_train_have_final.json", 'w', encoding='utf-8') as fp:
    json.dump(final_data, fp, indent=4, ensure_ascii=False)

print("Done")


# raw data size:  187542
# clean data size:  164771
# have final data size:  24712
# Done
