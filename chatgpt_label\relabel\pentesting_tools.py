# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





prompt = """你是一个渗透测试专家。你的目标是参考给定的渗透测试任务和工具对，将其重写为更复杂多样的渗透测试任务。

- 重写的渗透测试任务必须合理，必须被人类理解和且完成。
- 重写的提渗透测试任务必须能使用新给出的工具完成。
- 重写的提渗透测试任务需要在深度和广度上有所增加。

你可以从一些方面来增加深度和广度：
- 增加渗透测试任务的难度，例如增加攻击的复杂度，增加攻击的隐蔽性，增加攻击的持久性等。
- 增加渗透测试任务的多样性，例如增加攻击的攻击向量，增加攻击的攻击目标，增加攻击的攻击场景等。

你可以使用以下工具：
- msploit
- 冰蝎

使用的工具：msploit
渗透测试任务：I want to exploit a known RCE in phpMyAdmin 4.8.1


新的工具：冰蝎
新的渗透测试任务：
"""

prompt = """
你现在处于一个渗透测试环境中。你需要模拟nmap工具，根据用户给出的指令，生成nmap工具执行该指令的结果。

- 请尽量模拟现实中的环境，并生成对应的执行结果。
- 你生成的结果必须多样，重现现实中复杂多样化的情况。

指令：Perform a version scan on all HTTP and HTTPS servers in 192.168.1.0/24
"""



prompt = """
你现在处于一个渗透测试环境中。你需要创建一个靶标环境和一个渗透测试任务。

- 请尽量模拟现实中的环境，并生成创建尽量复杂、多样的靶场环境。重现现实中复杂多样化的情况。
- 生成的渗透测试任务需要能在你创建的靶场环境中完成。
- 生成的渗透测试任务需要多样且复杂，重现现实中复杂多样化的任务。
- 尽量详细的给出靶场环境。包括靶场环境的网络拓扑，靶场环境的网络设备，靶场环境的操作系统，靶场环境的软件，服务的IP和端口，存在的漏洞等。
- 尽量详细的给出渗透测试任务。包括渗透测试任务的攻击目标，渗透测试任务的攻击方法，攻击步骤，使用的攻击工具和命令，渗透测试任务的攻击结果等。 
- 请使用中文回答。
- 请使用markdown格式回答。

注意：
你生成的环境中需要具备以下漏洞：
{}

请生成一个靶场环境和渗透测试任务。

"""




async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://*********:50003/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")

raw_data = load_dataset("parquet", data_dir="/home/<USER>/llm_dataset/aya_dataset/data")
raw_data = raw_data['train']
print(raw_data[0])

print("\nraw data size: %s" % len(raw_data))


raw_data = raw_data.map(lambda x: {"prompt": x["inputs"]}, num_proc=35)
print("\n\nprompt over. prompt example:\n%s" % raw_data[0]["prompt"])

print("\n\nmake list data")
data = []
for point in tqdm(raw_data):
    data.append(point)
    
# data = data[:100] + data[-100:]

print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/aya_dataset/re_label_answer.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)








