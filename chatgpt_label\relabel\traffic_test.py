# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import numpy as np
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset


prompt = """

The available categories include: 
{category_list}

You should choose one category from the above list to label the question.

------------------------------------

The last line of your response should be of the following format: 'ANSWER: (category)'
Answer in the following format:

Thought: [Think step by step]
ANSWER: [category, should be one of the above categories]
"""



def eval_calculation(pred, label):
    if not pred:
        return 0
    ANSWER_PATTERN = r"(?i)ANSWER\s*:\s*([^\n]+)"
    match = re.search(ANSWER_PATTERN, pred)
    extracted_answer = match.group(1) if match else None
    if extracted_answer is None:
        return 0
    else:
        if (label in extracted_answer) or (extracted_answer in label):
            return 1
        else:
            return 0
    

async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="instruction", answer_name="answer", category="category_list"):
    async with semaphore:
        url = "http://10.24.45.213:50000/v1/chat/completions"
        
        category_list = ", ".join(point[category])


        messages = [
            {"role": "user",
             "content": point[question_name] + "\n\n" + prompt.format(category_list=category_list)},
        ]

        body = {
            "model": "secllm-v2",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        point["eval"] = eval_calculation(answer, point["output"])
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results

loop = asyncio.get_event_loop()

# data_name = ""
# app53-2023  csic-2010  cstnet-2023  cw100-2018  dapt-2020  dohbrw-2020  instructions  iscx-botnet-2014  iscx-tor-2016  iscx-vpn-2016  ustc-tfc-2016


    

for data_name in ["app53-2023", 
                  "csic-2010", 
                  "cstnet-2023", 
                  "cw100-2018", 
                  "dapt-2020", 
                  "dohbrw-2020", 
                #   "instructions", 
                  "iscx-botnet-2014", 
                  "iscx-tor-2016", 
                  "iscx-vpn-2016", 
                  "ustc-tfc-2016" ]:
    
    with open(f"/home/<USER>/TrafficLLM/TrafficLLM_Datasets/{data_name}/{data_name}_label.json", 'r', encoding='utf-8') as fp:
        category_list = json.load(fp)
        category_list = list(category_list.keys())
        
    data = []
    
    with open(f"/home/<USER>/TrafficLLM/TrafficLLM_Datasets/{data_name}/{data_name}_detection_packet_test.json", 'r', encoding='utf-8') as fp:
        for line in fp:
            point = json.loads(line)
            point["category_list"] = category_list
            data.append(point)

    print("data name: %s" % data_name)
    print("data size: %s" %  len(data))
    print("data example: ", data[0])

    results = loop.run_until_complete(main(data))

    print("%s Done" % data_name)
    eval_list = [point["eval"] for point in results]
    print("%s eval ACC: %s" % (data_name, np.mean(eval_list)))


    with open(f"/home/<USER>/TrafficLLM/TrafficLLM_Datasets/{data_name}/{data_name}_detection_packet_secllm2_output.json", 'w', encoding='utf-8') as fp:
        json.dump(results, fp, indent=4, ensure_ascii=False)

