# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhang<PERSON>
# @Time: 2021/11/22 16:07


from __future__ import print_function
import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd


def openai_query(messages, model="gpt-3.5-turbo-16k", temperature: float = 0, top_p: float = 1,
                       presence_penalty: float = 0.0, frequency_penalty: float = 0.0):
    url = "http://gpt1.attackgraph.com:16180/openai"
    body = {
        "model": model,
        "temperature": temperature,
        "top_p": top_p,
        "messages": messages,
        "presence_penalty": presence_penalty,
        "frequency_penalty": frequency_penalty
    }

    try:
        # print("openai chatgpt query!")
        response = requests.post(url, json=body)
        # print("openai chatgpt response status: %s - text: %s" % (response.status_code, response.text))
        if response.status_code == 200:
            res = response.json()
            return res
        else:
            print("openai chatgpt api-- Error: %s" % response.json())
            return None
    except Exception as ex:
        print("openai chatgpt request -- Error:, ex: %s" % ex)
        return None


def chatgpt_math_solve_step(question, equation, answer):
    if equation.startswith("x="):
        equation = equation[2:] + '='

    messages = [
        {"role": "user",
         "content": "你需要参考提示的解题公式和答案，解决改出的数学题。首先分析解题公式的思路。然后，你需要一步步详细的写出解题步骤，每一步的解题或者化简都不允许跳过。确保最终答案和提示的答案相同。"
                    "解题思路与解题步骤不允许抄袭提示的公式与答案。"},
         {"role": "assistant",
         "content": "好的，我会解决给出的数学题。先分析解题公式的思路，并一步步详细的写出我的解题步骤。"},
         {"role": "user",
         "content": "题目：小王要将150千克含药量20%的农药稀释成含药量5%的药水．需要加水多少千克？\n公式：150*20%/5%-150=\n答案：450"},
         {"role": "assistant",
         "content": "解题思路：\n此处先通过150*20%计算得到实际的农药量，然后除以稀释后的含药量5%，得到稀释后的药水重量，最后减去最初的药水重量，得到需要加的水的重量。\n"
                    "解题步骤：\n1.首先，我们知道小王最初有的农药含药量是20%，即150千克农药中有150*20%=30千克是农药，有150-30=120千克是水。\n"
                    "2.然后，我们知道小王稀释农药后，含药量降为5%。同时根据第一步，我们知道实际的农药量为：30千克。即稀释后的药水重量为30/5%=600千克。\n"
                    "3.最后，使用稀释后的药水的重量减去原始的药水重量，我们可以得到需要增加的水的重量：600-150=450千克。\n"
                    "4.所以，小王需要增加的水的重量是：150*20%/5%-150=450千克。\n"
                    "答案：450千克"},
         {"role": "user",
         "content": "题目：%s\n公式：%s\n答案：%s" % (question, equation, answer)}
    ]

    try:
        response = openai_query(messages, frequency_penalty=0.1)
        return response["choices"][0]["message"]['content']
    except Exception as ex:
        print("chatgpt math solve step error: %s" % str(ex))
        return ""


path = r"D:\work\GPT\chatgpt_label/Math23k_raw.json"
with open(path, 'r', encoding='utf-8') as f:
    math_question = json.load(f)

print("data size: %s" %  len(math_question))
print("data example: ", math_question[0])

all_data = []
for row in tqdm(math_question):
    answer = chatgpt_math_solve_step(row['sni_text'], row['equation'], row['ans'])
    row['step_answer'] = answer
    all_data.append(row)


with open('./Math23k_step_labeled.json', 'w', encoding='utf-8') as fp:
    json.dump(all_data, fp, indent=4, ensure_ascii=False)

use_data = [x for x in all_data if len(x['step_answer']) > 0]
print("use data size: %s" %  len(use_data))
with open('./Math23k_step_labeled_use.json', 'w', encoding='utf-8') as fp:
    for line in use_data:
        line = json.dumps(line, ensure_ascii=False)
        fp.write(line + '\n')

print("Done")
