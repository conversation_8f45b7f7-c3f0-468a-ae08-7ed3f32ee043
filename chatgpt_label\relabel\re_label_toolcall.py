# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import os
import yaml





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Athene-V2-Chat", #"Mistral-Large-Instruct-2407",  #"secllm-v2",  #"Meta-Llama-3.1-405B-Instruct",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 80  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")

import pandas as pd
data = load_dataset("parquet", data_dir="/home/<USER>/llm_dataset/function-calling-v0.2-with-r1-cot/data/")

data = data['train']

print(f"读取到 {len(data)} 个数据条目")

prompt = """
可用工具列表：
{{tool_list}}


==========================
用户命令：
{{query}}

==========================
thought：
{{think}}

==========================
工具调用命令：
{{tool_call}}

==========================

以上是一个英文分工具调用示例，请根据以下格式，生成一个中文的工具调用示例，并以json格式返回：

{
    "query": "中文的用户命令",
    "think": "中文的思考",
    "tool_call": "工具调用命令"
}

注意：
1. 请只给出json格式的示例，不要给出任何解释和说明。
2. 你给出的json示例，需要包含query, think, tool_call三个字段，并且能直接被json.loads解析。

"""

data_list = []
for point in data:
    data_list.append({
        "prompt": prompt.replace("{{tool_list}}", json.dumps(point["tools"])).replace("{{query}}", point["query"]).replace("{{think}}", point["think"]).replace("{{tool_call}}", json.dumps(point["answers"])),
        "tools": point["tools"],
        "en_query": point["query"],
        "en_think": point["think"],
        "en_tool_call": point["answers"],
    })


print("\n\ndata size: %s" %  len(data_list))
print("\ndata example: \n\n%s" % data_list[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data_list))

print("Done")


with open("/home/<USER>/llm_dataset/function-calling-v0.2-with-r1-cot/relabel_toolcall_chinese.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)



