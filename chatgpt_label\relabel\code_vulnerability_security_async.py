# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm


# 简单模板
TEMPLATE0 = '''
### Task Description:
Provided an instruction (which may include input), a code fragment response (with vulnerability), a code fragment response (without vulnerability), and a brief description of the code vulnerability.
1. Please provide a detailed explanation of the code response (with vulnerability and without vulnerability), including its functionality and implementation method.
2. Analyze in detail the types of vulnerabilities that exist in the code (with vulnerability), as well as the causes and formation methods of vulnerabilities, based on the code content. You should refer to the description of the code vulnerability.
3. Provide specific attack request parameters or attack code to exploit vulnerabilities in the code.
4. Provide the fix code. You should refer to the code fragment response (without vulnerability) and optimize it

### The instruction:
{question}

### Code Response (with vulnerability):
{rejected}

### Code Response (without vulnerability):
{chosen}

### Brief description of the code vulnerability:
{vulnerability}

'''

TEMPLATE1 = '''
### 任务描述:
提供一个编写代码的指令（可能包括输入输出要求）、对应的代码片段响应（有漏洞）、代码片段响应（无漏洞）以及代码漏洞的简要描述。
1. 请详细解释代码片段响应（有漏洞和无漏洞），包括其功能和实现方法。
2. 根据代码内容，详细分析代码片段响应（有漏洞）中存在的漏洞类型，以及漏洞的原因和形成方法。您应该参考代码漏洞的描述。
3. 提供特定的攻击请求参数或攻击代码，以利用代码片段响应（有漏洞）中的漏洞。
4. 提供修复代码。您应该参考代码片段响应（无漏洞）并对其进行优化。

### 编写代码的指令:
{question}

### 代码片段响应（有漏洞）:
{rejected}

### 代码片段响应（无漏洞）:
{chosen}

### 代码漏洞的简要描述:
{vulnerability}

'''

TEMPLATE2 = '''
### 任务描述:
提供一个编写代码的指令（可能包括输入输出要求）、对应的代码片段响应、修复后代码以及代码漏洞的简要描述。
1. 请详细解释代码片段响应，包括其功能和实现方法。
2. 根据代码内容，详细分析代码片段响应中存在的漏洞类型，以及漏洞的原因和形成方法。您应该参考代码漏洞的描述。
3. 提供特定的攻击请求参数或攻击代码，以利用代码片段响应中的漏洞。
4. 提供修复代码。您应该参考修复后代码并对其进行优化。

- 注意，你的回答中要提及任何修复后代码以及代码漏洞的简要描述的存在。

### 编写代码的指令:
{question}

### 代码片段响应:
{rejected}

### 修复后代码:
{chosen}

### 代码漏洞的简要描述:
{vulnerability}

'''

TEMPLATE2_use = '''
### 任务描述:
提供一个编写代码的指令（可能包括输入输出要求）、对应的代码片段响应。
1. 请详细解释代码片段响应，包括其功能和实现方法。
2. 根据代码内容，详细分析代码片段响应中存在的漏洞类型，以及漏洞的原因和形成方法。
3. 提供特定的攻击请求参数或攻击代码，以利用代码片段响应中的漏洞。
4. 提供修复代码。

### 编写代码的指令:
{question}

### 代码片段响应:
{rejected}

'''


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer



async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://10.37.4.4:8080/v1/chat/completions"
        
        body = {
            "model":"Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            # "frequency_penalty": 0.2,
            "max_tokens": 4000,
            "messages": [
                {"role": "user", "content": point["input"]},
            ],
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point['output'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


data_list = []

with open("/home/<USER>/llm_dataset/Code_Vulnerability_Security_DPO/secure_programming_dpo.json", 'r', encoding='utf-8') as f:
    for line in f:
        point = json.loads(line)
        
        point1 = copy.deepcopy(point)
        point1["input"] = (TEMPLATE0.replace("{question}", point["question"])
                          .replace("{rejected}", point["rejected"])
                          .replace("{chosen}", point["chosen"])
                          .replace("{vulnerability}", point["vulnerability"]))
        point1["model_input"] = point1["input"]
        data_list.append(point1)
        
        point2 = copy.deepcopy(point)
        point2["input"] = (TEMPLATE1.replace("{question}", point["question"])
                          .replace("{rejected}", point["rejected"])
                          .replace("{chosen}", point["chosen"])
                          .replace("{vulnerability}", point["vulnerability"]))
        point2["model_input"] = point2["input"]
        data_list.append(point2)
        
        point3 = copy.deepcopy(point)
        point3["input"] = (TEMPLATE2.replace("{question}", point["question"])
                          .replace("{rejected}", point["rejected"])
                          .replace("{chosen}", point["chosen"])
                          .replace("{vulnerability}", point["vulnerability"]))
        point3["model_input"] = (TEMPLATE2_use.replace("{question}", point["question"])
                          .replace("{rejected}", point["rejected"]))
        data_list.append(point3)

print("data size: %s" %  len(data_list))
print("data example: ", data_list[0])


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data_list))

with open("/home/<USER>/llm_dataset/Code_Vulnerability_Security_DPO/code_vulnerability_security_v4_meta.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

print("Done")



