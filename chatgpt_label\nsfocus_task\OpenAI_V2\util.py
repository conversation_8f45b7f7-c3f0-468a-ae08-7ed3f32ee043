# encoding: utf-8

import requests

#API_URL="http://gpt1.attackgraph.com:16180/openai"
API_URL="https://api.openai.com/v1/chat/completions"

API_KEY="***************************************************"

#ENGINE="gpt-3.5-turbo-16k"
ENGINE="gpt-4"

GENERATE_ARGS = {
    "temperature": 0.0,
    "top_p": 1,
    "frequency_penalty": 0.0,
    "max_tokens": 1024,
}

def ChatCompletions(messages, url=API_URL, api_key=API_KEY, engine=ENGINE, **kwargs):
    headers = {"Authorization": f"Bearer {api_key}"} if api_key else {}
    data = {
        "model": engine,
        "messages": messages,
        "stream": False,
    }
    if "GENERATE_ARGS" in globals():
        data.update(GENERATE_ARGS)
    data.update(kwargs)
    response = requests.post(url, headers=headers, json=data)
    try:
        return response.json()
    except:
        return response.text