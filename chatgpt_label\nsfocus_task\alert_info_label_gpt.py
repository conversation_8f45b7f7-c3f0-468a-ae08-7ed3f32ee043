import json
import random
from tqdm import tqdm
import re


service_list = ["ips-ids", "uts", "waf", "4a", "linux", "tac"]


key_map = {
    "msgtype": {
        "name": "msgtype",
        "description": "发送给ISOP的消息类型",
        "value_range": "13：入侵检测（吸星）\n14：入侵检测（zealot）\n17：DDos\n21：webshell\n31489：nti威胁情报\n63：SDK防病毒\n100：自定义情报\n101：自定义规则\n110：内网主机扫描\n130：翻墙行为告警\n131：违法网站告警\n132：违规应用告警",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "13": "入侵检测（吸星）",
            "14": "入侵检测（zealot）",
            "17": "DDos",
            "21": "webshell",
            "31489": "nti威胁情报",
            "63": "SDK防病毒",
            "100": "自定义情报",
            "101": "自定义规则",
            "110": "内网主机扫描",
            "130": "翻墙行为告警",
            "131": "违法网站告警",
            "132": "违规应用告警",
            "109": "钓鱼邮件告警"
        },
        "prompt_template": "这是安全日志的消息类型。",
        "trans_key": "msgtype"
    },
    "product": {
        "name": "product",
        "description": "产品标识",
        "type": "string",
        "prompt_template": "这是告警事件的产品标识。",
        "trans_key": ["product_id", "product_type"]
    },
    "dev_ip": {
        "name": "dev_ip",
        "description": "告警设备ip",
        "type": "string",
        "prompt_template": "这是告警事件的告警设备IP地址。",
        "trans_key": "dev_ip"
    },
    "devip": {
        "name": "devip",
        "description": "告警设备ip",
        "type": "string",
        "prompt_template": "这是告警事件的告警设备IP地址。",
        "trans_key": "dev_ip"
    },
    "sip": {
        "name": "sip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "sport": {
        "name": "sport",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dip": {
        "name": "dip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dport": {
        "name": "dport",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "src_ip": {
        "name": "src_ip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "src_port": {
        "name": "src_port",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dst_ip": {
        "name": "dst_ip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dst_port": {
        "name": "dst_port",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "protocol": {
        "name": "protocol",
        "description": "协议",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "6": "HTTP",
            "17": "UDP"
        },
        "prompt_template": "这是告警事件的协议类型。",
        "trans_key": "protocol"
    },
    "direct": {
        "name": "direct",
        "description": "当前会话的方向",
        "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "内向外",
            "2": "外向内",
            "3": "内对内",
            "4": "其他"
        },
        "prompt_template": "这是告警事件的流量方向。",
        "trans_key": "direction"
    },
    "acted": {
        "name": "acted",
        "description": "实际所完成的动作",
        "type": "int",
        "prompt_template": "这是根据规则设备实际采取的动作。",
        "trans_key": "acted_action"
    },
    "action": {
        "name": "action",
        "description": "根据规则应该采取的动作",
        "type": "string",
        "prompt_template": "这是根据规则设备应该采取的动作。",
        "trans_key": "policy_action"
    },
    "alertlevel": {
        "name": "alertlevel",
        "description": "告警级别",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的告警级别。",
        "trans_key": "alertlevel"
    },
    "timestamp": {
        "name": "timestamp",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "stat_time": {
        "name": "stat_time",
        "description": "时间戳",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "date": {
        "name": "date",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "rawlen": {
        "name": "rawlen",
        "description": "报文长度",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是报文的长度。",
        "trans_key": ["payload_len", "rawlen", "pkt_len"]
    },
    "rawinfo": {
        "name": "rawinfo",
        "description": "原始报文信息",
        "value_range": "base64编码\n包含SDK防病毒日志中的md5信息",
        "type": "string",
        "prompt_template": "这是原始报文信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    "msg": {
        "name": "msg",
        "description": "存放对应该日志的描述",
        "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "ar": {
        "name": "ar",
        "description": "攻击是否成功",
        "value_range": "值为1(成功),2(失败),3(未知)",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "成功",
            "2": "失败",
            "3": "未知"
        },
        "prompt_template": "这是攻击结果，标识攻击是否成功。",
        "trans_key": "ar"
    },
    "q_body": {
        "name": "q_body",
        "description": "http请求消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http请求消息，这是一个Base64编码的字符串，我们可以对其解码，得到q_body_plain字段。",
        "trans_key": "q_body"
    },
    "r_body": {
        "name": "r_body",
        "description": "http回应消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http回应消息，这是一个Base64编码的字符串，我们可以对其解码，得到r_body_plain。",
        "trans_key": "r_body"
    },
    "domain": {
        "name": "domain",
        "description": "域名",
        "type": "string",
        "prompt_template": "这是域名信息。",
        "trans_key": "domain"
    },
    "event_type": {
        "name": "event_type",
        "description": "告警类型",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志的告警类型。",
        "trans_key": "event_type"
    },
    "alertinfo": {
        "name": "alertinfo",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "alert_type": {
        "name": "alert_type",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志事件的告警类型。",
        "trans_key": "alert_type"
    },
    "protocol_type": {
        "name": "protocol_type",
        "description": "协议类型",
        "value_range": "HTTP/HTTPS",
        "type": "string",
        "prompt_template": "这是协议类型。",
        "trans_key": "protocol_type"
    },
    "uri": {
        "name": "uri",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "url": {
        "name": "url",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "block": {
        "name": "block",
        "description": "是否禁用ip",
        "value_range": "0：不启用\n1: 启用IP封禁",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "0": "不启用",
            "1": "启用IP封禁"
        },
        "prompt_template": "这是代表是否禁用ip。",
        "trans_key": "block_info"
    },
    "http": {
        "name": "http",
        "description": "HTTP请求或者响应信息",
        "value_range": "base64编码",
        "type": "string",
        "prompt_template": "这是HTTP请求或者响应信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    # 此处开始为新增

    "hash": {
        "name": "hash_device",
        "description": "设备HASH",
        "type": "string",
        "prompt_template": "这是设备的HASH",
        "trans_key": "hash_device"
    },
"count_num": {
        "name": "confidence",
        "description": "置信度",
        "type": "string",
        "prompt_template": "这是置信度",
        "trans_key": "confidence"
    },
    "country": {
        "name": "country",
        "description": "国家",
        "type": "string",
        "prompt_template": "这是国家",
        "trans_key": "country"
    },
    "raw_client_ip": {
        "name": "sip",
        "description": "源ip或攻击者的地址",
        "type": "string",
        "prompt_template": "这是攻击者的ip",
        "trans_key": "attacker"
    },
    "site_name": {
        "name": "site_name",
        "description": "站点名称",
        "type": "string",
        "prompt_template": "这是站点的名称",
        "trans_key": "site_name"
    },
    "sid": {
        "name": "session_id",
        "description": "会话ID",
        "type": "string",
        "prompt_template": "这是会话ID",
        "trans_key": "session_id"
    },
    "smac": {
        "name": "smac",
        "description": "源MAC地址",
        "type": "string",
        "prompt_template": "这是源MAC地址",
        "trans_key": "smac"
    },
    "dmac": {
        "name": "dmac",
        "description": "目的MAC地址",
        "type": "string",
        "prompt_template": "这是目的MAC地址",
        "trans_key": "dmac"
    },
    "group": {
        "name": "gr_danger",
        "description": "危险程度",
        "type": "string",
        "prompt_template": "这是危险程度",
        "trans_key": "gr_danger"
    },
"ruleid": {
        "name": "rule_id",
        "description": "策略编号",
        "type": "string",
        "prompt_template": "这是策略编号",
        "trans_key": "rule_id"
    },
    "last_times": {
        "name": "occur_count",
        "description": "发生次数",
        "type": "string",
        "prompt_template": "这是发生次数",
        "trans_key": "occur_count"
    },
    "judge_ret": {
        "name": "occur_count",
        "description": "发生次数",
        "type": "string",
        "prompt_template": "这是发生次数",
        "trans_key": "occur_count"
    },
    "ds": {
        "name": "ds",
        "description": "协议返回信息",
        "type": "string",
        "prompt_template": "这是协议返回信息",
        "trans_key": "ds"
    },
    "dhcpmac": {
        "name": "dhcpmac",
        "description": "发起DHCP请求的MAC地址",
        "type": "string",
        "prompt_template": "这是发起DHCP请求的MAC地址",
        "trans_key": "dhcpmac"
    },
    "card": {
        "name": "network_card",
        "description": "设备网卡",
        "type": "string",
        "prompt_template": "这是设备网卡",
        "trans_key": "network_card"
    },
    "vid": {
        "name": "vid",
        "description": "接口vlan id",
        "type": "string",
        "prompt_template": "这是接口vlan id",
        "trans_key": "vid"
    },
    "strategy": {
        "name": "strategy",
        "description": "策略",
        "type": "string",
        "prompt_template": "这是策略",
        "trans_key": "strategy"
    },
    "szonename": {
        "name": "szonename",
        "description": "源安全区名称",
        "type": "string",
        "prompt_template": "这是源安全区名称",
        "trans_key": "szonename"
    },
    "r_code": {
        "name": "code",
        "description": "响应码",
        "type": "string",
        "prompt_template": "这是响应码",
        "trans_key": "code"
    },
    "n_cont_length": {
        "name": "content_length_server",
        "description": "服务器端响应内容长度",
        "type": "string",
        "prompt_template": "这是服务器端响应内容长度",
        "trans_key": "content_length_server"
    },
    "characters": {
        "name": "characters",
        "description": "匹配特征",
        "type": "string",
        "prompt_template": "这是匹配特征",
        "trans_key": "characters"
    },
    "rule_id": {
        "name": "rule_id",
        "description": "策略编号",
        "type": "string",
        "prompt_template": "这是策略编号",
        "trans_key": "rule_id"
    },
    "rule_name": {
        "name": "log_message",
        "description": "日志消息内容",
        "type": "string",
        "prompt_template": "这是日志消息内容",
        "trans_key": "log_message"
    },
    "policy_name": {
        "name": "policy_name",
        "description": "站点设备策略名称",
        "type": "string",
        "prompt_template": "这是设备策略名称",
        "trans_key": "policy_name"
    },
    "vsite_name": {
        "name": "vsite_name",
        "description": "虚拟站点名称",
        "type": "string",
        "prompt_template": "这是虚拟站点名称",
        "trans_key": "vsite_name"
    },
    "proxy_info": {
        "name": "proxy_info",
        "description": "代理信息",
        "type": "string",
        "prompt_template": "这是代理信息",
        "trans_key": "proxy_info"
    },
    "dzonename": {
        "name": "szonename",
        "description": "源安全区名称",
        "type": "string",
        "prompt_template": "这是源安全区名称",
        "trans_key": "szonename"
    },
    "iscdnip": {
        "name": "iscdnip",
        "description": "目的IP为CDN IP",
        "type": "integer",
        "prompt_template": "判断目的IP是否为CDN IP",
        "trans_key": "iscdnip"
    },
    "policy_desc": {
        "name": "policy_desc",
        "description": "设备策略描述",
        "type": "string",
        "prompt_template": "设备策略描述",
        "trans_key": "policy_desc"
    },
    "msel": {
        "name": "msel",
        "description": "事件发生时的毫秒数",
        "type": "string",
        "prompt_template": "这是事件发生时的毫秒数",
        "trans_key": "msel"
    },

}

key_map_4a = {
    "SYSNAME": {
        "name": "SYSNAME",
        "description": "",
        "type": "string",
        "prompt_template": "这是服务名称。",
        "trans_key": "business_name"
    },
    "LOGINTIME": {
        "name": "LOGINTIME",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "SOURCEIP": {
        "name": "SOURCEIP",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是绕行事件的源IP地址。",
        "trans_key": "sip"
    },
    "RESIP": {
        "name": "RESIP",
        "description": "IP",
        "type": "string",
        "prompt_template": "这是IP地址。",
        "trans_key": "ip"
    },
    "LOGINNAME": {
        "name": "LOGINNAME",
        "description": "用户名",
        "type": "string",
        "prompt_template": "这是绕行事件的用户名。",
        "trans_key": "user_name"
    }

}

key_map_linux = {
    "name": {
        "name": "name",
        "description": "软件名",
        "type": "string",
        "prompt_template": "这是软件名。",
        "trans_key": "software"
    },
    "version": {
        "name": "version",
        "description": "软件版本",
        "type": "string",
        "prompt_template": "这是软件版本。",
        "trans_key": "software_version"
    },
    "action": {
        "name": "action",
        "description": "这是日志的内容",
        "type": "string",
        "prompt_template": "这是日志的内容。",
        "trans_key": "log_message"
    },
    "host_id": {
        "name": "host_id",
        "description": "主机的id",
        "type": "string",
        "prompt_template": "这是主机的id。",
        "trans_key": "host_id"
    }
}

key_map_tac = {
    "file_type": {
        "name": "file_type",
        "description": "文件的类型",
        "type": "string",
        "prompt_template": "这是文件的类型。",
        "trans_key": "sample_file_type"
    },
    "app_desc": {
        "name": "app_desc",
        "description": "app详情",
        "type": "string",
        "prompt_template": "这是app详情。",
        "trans_key": "app_desc"
    },
    "app": {
        "name": "app",
        "description": "应用唯一标志",
        "type": "string",
        "prompt_template": "这是应用的唯一标识。",
        "trans_key": "app"
    },
    "sampleid": {
        "name": "sampleid",
        "description": "样本唯一标识",
        "type": "string",
        "prompt_template": "这是样本的唯一标识。",
        "trans_key": "sample_id"
    },
    "file_name": {
        "name": "file_name",
        "description": "样本文件名称",
        "type": "string",
        "prompt_template": "这是样本文件名称，这是一个Base64编码的字符串，我们可以对其解码，得到具体的样本文件名称。",
        "trans_key": ["file_name", "sample_file_name"]
    },
    "file_hash": {
        "name": "file_hash",
        "description": "样本文件hash",
        "type": "string",
        "prompt_template": "这是样本文件hash。",
        "trans_key": ["file_hash", "sample_file_hash"]
    },
    "detail": {
        "name": "detail",
        "description": "扩展字段",
        "type": "string",
        "prompt_template": "这是告警日志的详情，其中包括一些扩展字段。",
        "trans_key": "info4"
    },
    "app_desc_uri": {
        "name": "app_desc_uri",
        "description": "网络定位信息",
        "type": "string",
        "prompt_template": "这是网络定位信息，这是一个Base64编码的字符串，我们可以对其解码，得到具体的uri。",
        "trans_key": "app_desc_uri"
    },
    "type": {
        "name": "type",
        "description": "威胁类型",
        "value_range": ''' "1": "Trojan",
            "2": "SuspiciousFile",
            "3": "AV" ''',
        "type": "string",
        "is_enum": True,
        "enum_map": {
            "1": "Trojan",
            "2": "SuspiciousFile",
            "3": "AV"
        },
        "prompt_template": "这是日志事件的威胁类型。",
        "trans_key": "alert_type"
    },
    "file_md5": {
        "name": "file_md5",
        "description": "文件MD5",
        "type": "string",
        "prompt_template": "这是样本文件MD5。",
        "trans_key": ["file_md5", "sample_file_md5h"]
    },
    "app_desc_orign": {
        "name": "app_desc_orign",
        "description": "app来源",
        "type": "string",
        "prompt_template": "这是app来源信息，这是一个Base64编码的字符串，我们可以对其解码，得到具体的uri。",
        "trans_key": "app_desc_orign"
    },
    "name": {
        "name": "name",
        "description": "告警的名称",
        "type": "string",
        "prompt_template": "这是告警的名称。",
        "trans_key": "alert_name"
    },
    "alert_time": {
        "name": "alert_time",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "threat": {
        "name": "threat",
        "description": "威胁等级",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的威胁等级。",
        "trans_key": "threat"
    },
    # 以下为新增
    "task_groupid": {
        "name": "task_groupid",
        "description": "任务组id",
        "type": "string",
        "prompt_template": "这是任务组id",
        "trans_key": "task_groupid"
    },
    "taskid": {
        "name": "task_id",
        "description": "任务id,样本唯一标识",
        "type": "string",
        "prompt_template": "这是任务id或样本唯一标识",
        "trans_key": "task_id"
    },
    "engine_type": {
        "name": "engine_type",
        "description": "引擎类型",
        "type": "string",
        "prompt_template": "这是引擎类型",
        "trans_key": "engine_type"
    },
    "tags": { # 这一条对应了两个format的条目，tags和devip
        "name": "tags",
        "description": "设备地址",
        "type": "string",
        "prompt_template": "这是设备地址",
        "trans_key": ["tags", "dev_ip"]
    },
    "source_type": {
        "name": "source_type",
        "description": "样本来源",
        "type": "int",
        "prompt_template": "这是样本来源",
        "trans_key": "source_type"
    },
    "time": {
        "name": "report_time",
        "description": "上报时间",
        "type": "long",
        "prompt_template": "这是上报时间",
        "trans_key": "report_time"
    },
    "engine_config": {
        "name": "engine_config",
        "description": "引擎配置",
        "type": "string",
        "prompt_template": "这是引擎配置",
        "trans_key": "engine_config"
    },
    # 新增2，TAC_TAC告警日志_yara_Chinese-Hacktool-Set---file-CookieTools.exe.txt
    "parent_taskid": {
        "name": "parent_taskid",
        "description": "父任务的uuid",
        "type": "string",
        "prompt_template": "这是父任务的uuid",
        "trans_key": "parent_taskid"
    },
    "intern_path": {
        "name": "intern_path",
        "description": "内部路径",
        "type": "string",
        "prompt_template": "这是内部路径",
        "trans_key": "intern_path"
    },
    "desc": {
        "name": "log_message",
        "description": "日志消息内容",
        "type": "string",
        "prompt_template": "这是日志消息内容",
        "trans_key": "log_message"
    }
}

# 新增安恒_EDR.TXT的key-map
key_map_anheng = {
    "rectime": {
        "name": "timestamp",
        "description": "时间戳",
        "type": "string",
        "prompt_template": "这是时间戳",
        "trans_key": "timestamp"
    },
    "app": {
        "name": "app_name",
        "description": "应用名称",
        "type": "string",
        "prompt_template": "这是应用名称",
        "trans_key": "app_name"
    },
    "app_version": {
        "name": "app_version",
        "description": "应用程序版本",
        "type": "string",
        "prompt_template": "这是应用程序版本",
        "trans_key": "app_version"
    },
    "username": {
        "name": "user_name",
        "description": "用户",
        "type": "string",
        "prompt_template": "这是用户",
        "trans_key": "user_name"
    },
    # 不知道是啥
    "uuid": {
        "name": "info0",
        "description": "扩展字段0",
        "type": "string",
        "prompt_template": "这是id扩展字段0",
        "trans_key": "info0"
    },
    "machine_ip": {
        "name": "dev_ip",
        "description": "设备地址",
        "type": "string",
        "prompt_template": "这是设备地址",
        "trans_key": "dev_ip"
    },
    "type": {
        "name": "msgtype",
        "description": "消息类型",
        "type": "string",
        "prompt_template": "这是消息类型",
        "trans_key": "msgtype"
    },
    "event_str": {
        "name": "log_message",
        "description": "日志消息内容",
        "type": "string",
        "prompt_template": "这是日志消息内容",
        "trans_key": "log_message"
    },
    "desc": {
        "name": "event_desc",
        "description": "事件摘要",
        "type": "string",
        "prompt_template": "这是事件摘要",
        "trans_key": "event_desc"
    },
    "risk": {
        "name": "risk_name",
        "description": "风险名称",
        "type": "string",
        "prompt_template": "这是风险名称",
        "trans_key": "risk_name"
    },
    "ip": {
        "name": "sip",
        "description": "源地址",
        "type": "string",
        "prompt_template": "这是源地址",
        "trans_key": "sip"
    },
    "ip_location": {
        "name": "geolocation",
        "description": "地理位置",
        "type": "string",
        "prompt_template": "这是地理位置",
        "trans_key": "geolocation"
    },
    "process": {
        "name": "process_path",
        "description": "进程路径",
        "type": "string",
        "prompt_template": "这是进程路径",
        "trans_key": "process_path"
    },
    "process_md5": {
        "name": "process_md5",
        "description": "进程映像md5",
        "type": "string",
        "prompt_template": "这是进程映像md5",
        "trans_key": "process_md5"
    },
    "process_id": {
        "name": "process_pid",
        "description": "进程ID",
        "type": "string",
        "prompt_template": "这是进程ID",
        "trans_key": "process_pid"
    },
    "process_user": {
        "name": "process_user_name",
        "description": "进程用户名",
        "type": "string",
        "prompt_template": "这是进程用户名",
        "trans_key": "process_user_name"
    },
    "pprocess": {
        "name": "process_parent_path",
        "description": "父进程映像路径",
        "type": "string",
        "prompt_template": "这是父进程映像路径",
        "trans_key": "process_parent_path"
    },
    "pprocess_md5": {
        "name": "info1",
        "description": "扩展字段1",
        "type": "string",
        "prompt_template": "这是扩展字段1",
        "trans_key": "info1"
    },
    "pprocess_id": {
        "name": "process_parent_pid",
        "description": "进程父进程的PID",
        "type": "string",
        "prompt_template": "这是进程父进程的PID",
        "trans_key": "process_parent_pid"
    },
    "pprocess_user": {
        "name": "process_parent_user_name",
        "description": "父进程用户名",
        "type": "string",
        "prompt_template": "这是父进程用户名",
        "trans_key": "process_parent_user_name"
    },
    "process_tree": {
        "name": "process_tree",
        "description": "进程树",
        "type": "string",
        "prompt_template": "这是进程树",
        "trans_key": "process_tree"
    },
    "operation": {
        "name": "process_action",
        "description": "进程动作",
        "type": "string",
        "prompt_template": "这是进程动作",
        "trans_key": "process_action"
    },
    "file_path": {
        "name": "infected_file_path",
        "description": "感染病毒文件路径",
        "type": "string",
        "prompt_template": "这是感染病毒文件路径",
        "trans_key": "infected_file_path"
    },
    "result": {
        "name": "event_res",
        "description": "事件结果",
        "type": "string",
        "prompt_template": "这是事件结果",
        "trans_key": "event_res"
    }
}

key_map_ruishu_WAF = {
    "hostname": {
        "name": "host_name",
        "description": "主机名",
        "type": "string",
        "prompt_template": "这是主机名",
        "trans_key": "host_name"
    },
    "node_ip": {
        "name": "dev_ip",
        "description": "设备地址",
        "type": "string",
        "prompt_template": "这是设备地址",
        "trans_key": "dev_ip"
    },
    "path": {
        "name": "task_path",
        "description": "任务执行路径",
        "type": "string",
        "prompt_template": "这是任务执行路径",
        "trans_key": "task_path"
    },
    "status": {
        "name": "status",
        "description": "状态",
        "type": "int",
        "prompt_template": "这是状态",
        "trans_key": "status"
    },
    "user_agent": {
        "name": "user_agent",
        "description": "身份标识",
        "type": "string",
        "prompt_template": "这是身份标识",
        "trans_key": "user_agent"
    },
    "upstream_status": {
        "name": "status",
        "description": "状态",
        "type": "string",
        "prompt_template": "这是状态",
        "trans_key": "status"
    },
    "http_host": {
        "name": "host_raw",
        "description": "主机信息",
        "type": "string",
        "prompt_template": "这是主机信息",
        "trans_key": "host_raw"
    },
    "invalid_request_action": {
        "name": "task_action",
        "description": "任务操作",
        "type": "string",
        "prompt_template": "这是任务操作",
        "trans_key": "task_action"
    },
    "ua_device": {
        "name": "device_version",
        "description": "设备版本",
        "type": "string",
        "prompt_template": "这是设备版本",
        "trans_key": "device_version"
    },
    "ua_browser_version": {
        "name": "browser",
        "description": "浏览器",
        "type": "string",
        "prompt_template": "这是浏览器",
        "trans_key": "browser"
    },
    "ua_os": {
        "name": "os_version",
        "description": "操作系统版本",
        "type": "string",
        "prompt_template": "这是操作系统版本",
        "trans_key": "os_version"
    },
    "server_alias": {
        "name": "site_name",
        "description": "站点名称",
        "type": "string",
        "prompt_template": "这是站点名称",
        "trans_key": "site_name"
    },
    "server_name": {
        "name": "server",
        "description": "服务器的地址",
        "type": "string",
        "prompt_template": "这是服务器的地址",
        "trans_key": "server"
    }
}


# 单行日志的key描述
target_key_desc = {
    "achannel_ip": "A接口服务端ip",
    "achannel_protocol": "A接口服务端协议",
    "dev_ip": "设备地址",
    "dip": "目的地址",
    "dport": "目的端口",
    "hash_device": "设备HASH",
    "log_message": "日志消息内容",
    "log_name": "日志名称",
    "log_type": "日志类型",
    "log_type_path": "日志分类绝对路径",
    "month": "月份值",
    "occur_count": "发生次数",
    "payload": "载荷",
    "product_type": "产品类型",
    "rule_id": "策略编号",
    "sip": "源地址",
    "sport": "源端口",
    "timestamp": "时间戳",
    "alertlevel": "原始日志事件级别",
    "app": "应用唯一标志",
    "app_action": "应用程序执行动作",
    "content_type": "内容类型",
    "create_time": "创建时间",
    "dev_id": "设备id",
    "dhost": "目的主机名",
    "direction": "流量方向",
    "event_id": "告警日志ID",
    "file_hash": "文件HASH",
    "file_type": "文件类型",
    "filter_rule": "过滤规则",
    "iint": "入接口",
    "info0": "扩展字段0",
    "info1": "扩展字段1",
    "info2": "扩展字段2",
    "info3": "扩展字段3",
    "level_name": "分级名称",
    "mail_receivers": "邮件接收者",
    "mail_sender": "邮件发送者",
    "mail_subject": "邮件主题",
    "merge_count": "事件持续发生的次数",
    "oint": "出接口",
    "os_name": "操作系统名称",
    "serial_num": "序列号",
    "session_id": "会话ID",
    "severity": "严重程度",
    "shost": "源主机名",
    "user_agent": "身份标识",
    "wsi": "会话标识",
    "x_forwarded_for": "HTTP XFF头",
    "attack_type": "攻击类型",
    "begin_time": "起始时间",
    "end_time": "结束时间",
    "filter_result": "过滤应用结果",
    "port": "端口号",
    "raw_log_num": "原始日志编号",
    "service_status": "服务状态",
    "syslog_tag": "系统日志标签",
    "tgted_attack": "定向攻击",
    "user_name": "用户",
    "alert_type": "告警类型",
    "asset_id": "资产ID",
    "behaviour_type": "行为类别",
    "change_size": "变化量",
    "dataset": "goose控制块对应数据集引用名",
    "data_type": "信息类型",
    "db_name": "数据库名",
    "db_type": "数据库类型",
    "dmac": "目的MAC地址",
    "duration_time": "持续时间",
    "error_info": "错误原因",
    "event_desc": "事件摘要",
    "event_name": "事件标识",
    "event_res": "事件结果",
    "last_msg": "最近结果描述",
    "payload_len": "报文长度",
    "policy_name": "设备策略名称",
    "process_action": "进程动作",
    "process_user_name": "进程用户名",
    "risk_level": "风险等级",
    "sens_info": "敏感信息",
    "smac": "源MAC地址",
    "smt_user": "智能用户识别",
    "sql": "SQL",
    "task_id": "任务id",
    "tgt_ip": "目标IP",
    "url": "统一资源定位器",
    "websafe_count": "网站检测日志数量",
    "we_security_userid": "windows事件安全用户id",
    "data_feature": "数据特征",
    "db_table": "数据库表名称",
    "template_name": "模板名称",
    "event_state": "事件状态",
    "file_name": "文件名称",
    "group": "事件所属组信息",
    "host_name": "主机名",
    "login_dev_type": "登录设备类型",
    "site_name": "站点名称",
    "stat_time": "统计时间",
    "syslog_facility": "系统日志记录的级别",
    "websafe_priority": "网站监测日志优先级",
    "websafe_type": "网站检测日志类型",
    "app_proto": "应用协议",
    "ar": "攻击结果",
    "attacker": "攻击者",
    "collect_time": "采集时间",
    "device_version": "设备版本",
    "info4": "扩展字段4",
    "os_type": "操作系统类型",
    "product_vender": "产品厂商",
    "protocol": "传输协议",
    "tags": "标签",
    "tenant_id": "租户ID",
    "threat_type": "威胁类型",
    "victim": "受害者",
    "data_content": "数据内容",
    "login_res": "登录结果",
    "raw_log_category_id": "原始日志类型编号",
    "version": "版本",
    "acted_action": "设备实际动作",
    "auth_type": "鉴权类型",
    "login_type": "登录类型",
    "uid": "用户ID",
    "app_id": "应用标识ID",
    "app_version": "应用程序版本",
    "event_identifier": "事件标识",
    "file_size": "文件大小",
    "host_id": "主机id",
    "sample_file_hash": "样本文件hash",
    "sample_file_path": "文件路径",
    "service_name": "服务名称",
    "syslog_severity": "系统日志严重程度",
    "tgt_detail": "目标详情",
    "tgt_type": "目标类型",
    "threat": "threat INT 威胁等级",
    "threat_family": "恶意家族",
    "risk_name": "风险名称",
    "alert_info": "alert_info",
    "alert_name": "告警的名称",
    "app_name": "应用名称",
    "msg_id": "消息ID",
    "msgtype": "消息类型",
    "nf_type": "网元类型",
    "precedence": "PDR的相对优先级",
    "process_guid": "进程guid",
    "websafe_effect_url": "网站检测日志受影响URL",
    "host_type": "主机类型",
    "sample_file_size": "样本文件大小",
    "user_group": "用户组",
    "website_type": "URL分类",
    "agent_id": "AGENT的ID",
    "cmd": "命令",
    "euid": "进程执行用户ID",
    "external_ip": "外网ip",
    "gid": "组id",
    "group_name": "分组名称",
    "host_ip": "主机ip",
    "host_raw": "主机信息",
    "internal_ip": "内网ip",
    "log_date": "日志处理时间",
    "msg_flag": "消息标识符",
    "process_name": "进程名",
    "process_parent_name": "父进程名",
    "process_parent_path": "父进程映像路径",
    "process_parent_pid": "进程父进程的PID",
    "process_parent_user_name": "父进程用户名",
    "process_path": "进程路径",
    "process_pid": "进程ID",
    "process_uid": "进程所属用户ID",
    "product_vender_id": "厂商id",
    "remark": "备注",
    "domain": "域名",
    "protocol_type": "协议类型",
    "device_vendor": "网络设备厂商",
    "hardware_id": "硬件id",
    "host_mac": "主机mac",
    "cve": "cve信息",
    "device_type": "设备类型",
    "vlan_id": "虚拟局域网ID",
    "attack_reports": "攻击报告",
    "gr_os": "操作系统",
    "raw_log_pcategory": "日志类型父类",
    "sampling": "会话日志固定字段",
    "uri": "统一资源标识符",
    "cnnvd_id": "CNNVD编号",
    "confidence": "置信度",
    "report_time": "上报时间",
    "risk_id": "风险ID",
    "sample_id": "样本唯一标识",
    "suggestion_detail": "响应建议详情",
    "tactic_name": "战术名称",
    "victim_type": "受害者类型",
    "vul_type": "漏洞类型",
    "attacker_port": "攻击者使用端口",
    "authorize_info": "授权信息",
    "bugtraq_id": "BUGTRAQ ID",
    "code": "响应码",
    "cpe": "cpe信息",
    "cve_id": "CVE编号",
    "date": "原始日志日期",
    "ds": "协议返回信息",
    "dst_asset": "原始日志目的资产ID",
    "gr_danger": "危险程度",
    "gr_pop": "流行程度",
    "gr_service": "gr字段_服务类型",
    "gr_tech": "技术手段",
    "gr_type": "攻击手段",
    "kill_chain_stage_name": "攻击链名称",
    "log_result": "日志附带的结果",
    "method": "方法",
    "module": "模块值",
    "msel": "事件发生时的毫秒数",
    "ms_id": "MSRC编号",
    "network_card": "设备网卡",
    "nsfocus_id": "绿盟漏洞库ID",
    "payload_plain": "载荷_明文",
    "pkt_len": "包大小",
    "policy_action": "策略动作",
    "policy_desc": "设备策略描述",
    "policy_id": "设备策略ID",
    "post_data": "POST请求数据",
    "probe_id": "探针id",
    "product_id": "产品ID",
    "q_body": "请求内容",
    "r_body": "响应内容",
    "rep_value": "信誉值",
    "ret_code": "状态码",
    "service_type": "OS服务类型",
    "snapshot": "快照",
    "src_asset": "原始日志源资产ID",
    "tactic_id": "战术ID",
    "technique_id": "技术ID",
    "technique_name": "技术名称",
    "trojan_type": "木马类型",
    "victim_port": "受害者被攻击端口",
    "vid": "接口vlan id",
    "bytes_all": "字节总数",
    "characters": "匹配特征",
    "country": "国家",
    "interface": "接口"
}


def clean_info(raw_info, format_info, file_name):
    # 复制通用的key map，然后根据文件名选择特定的key map进行修改
    my_key_map = {}
    for k, v in key_map.items():
        my_key_map[k] = v

    if file_name.lower().startswith("4a"):
        for k, v in key_map_4a.items():
            my_key_map[k] = v

    if file_name.lower().startswith("linux"):
        for k, v in key_map_linux.items():
            my_key_map[k] = v

    if file_name.lower().startswith("tac"):
        for k, v in key_map_tac.items():
            my_key_map[k] = v

    if ("安恒" in file_name) and ("edr" in file_name.lower()):
        for k, v in key_map_anheng.items():
            my_key_map[k] = v

    if ("瑞数" in file_name) and ("waf" in file_name.lower()):
        for k, v in key_map_ruishu_WAF.items():
            my_key_map[k] = v


    new_raw_info = {}
    new_format_info = {}

    # 部分原始日志为[{}, {}]，统一格式
    if isinstance(raw_info, dict):
        raw_info = [raw_info]

    for point in raw_info:
        for key, value in point.items():
            # 非key map中的关注字段跳过
            if (key[0] == "=") or (key not in my_key_map) or (len(str(value).strip()) == 0):
                continue

            # 对应的归一化后的字段名
            format_key_list = my_key_map[key]["trans_key"]

            # 部分字段会归一化为多个字段，统一格式为list
            if isinstance(format_key_list, str):
                format_key_list = [format_key_list]

            # 遍历每个归一化字段
            for format_key in format_key_list:
                # 归一化日志中没有对应的归一化字段，则原日志中该字段也无效，跳过空值
                if format_key in format_info and len(str(format_info[format_key]).strip()) > 0:

                    # 原日志字段值长度截断
                    # if isinstance(value, str) and len(value) > 150:
                    #     value = value[:150]

                    # 归一化字段值
                    format_value = format_info[format_key]

                    # 归一化字段值长度截断
                    # if isinstance(format_value, str) and len(format_value) > 150:
                    #     format_value = format_value[:150]

                    # 更新原日志字段值和归一化字段值
                    new_raw_info[key] = value
                    new_format_info[format_key] = format_value
    return  new_raw_info, new_format_info


def make_log(raw_info, is_json=True, key_value_split="", params_split=""):
    if is_json:
        return json.dumps(raw_info, ensure_ascii=False)
    else:
        assert  len(key_value_split) > 0 and len(params_split) > 0
        if params_split == " ":
            param_list = [k + key_value_split + '"' + str(v) + '"' for k, v in raw_info.items()]
        else:
            param_list = [k + key_value_split + str(v) for k, v in raw_info.items()]
        return params_split.join(param_list)


def make_format_response(raw_info, format_info, my_key_map, key_value_split="", params_split="", pad_json=False):
    response = "这是一个安全事件日志，它记录了一个特定告警事件的详细信息。让我们逐个分析每个字段：\n"
    for key, value in raw_info.items():
        if isinstance(value, str) and len(value) > 30:
            value = value[:30] + "..."

        if len(key_value_split) == 0 and len(params_split) == 0:
            key_value_split = ": "

        # if pad_json is True:
        #     param = key
        # elif params_split == " ":
        #     param = key + key_value_split + '"' + str(value) + '"'
        # else:
        #     param = key + key_value_split + str(value)

        if params_split == " ":
            param = key + key_value_split + '"' + str(value) + '"'
        else:
            param = key + key_value_split + str(value)

        param_key_map = my_key_map[key]
        param_desc = param_key_map['prompt_template']

        if param_key_map.get("is_enum", False) is True:
            new_value = param_key_map['enum_map'].get(str(value), "")
            if len(new_value.strip()) > 0:
                param_desc += "这是一个枚举值，它可能代表：%s。" % new_value

        new_key = param_key_map['trans_key']
        if not isinstance(new_key, list):
            new_key  = [new_key]
        if len(new_key) > 1 or new_key[0] != key:
            new_key = ['`%s`' % k for k in new_key]
            parse_list = [
                "可以被解析为%s字段。",
                "可以被规范化为%s字段。",
                "可以被格式化为%s字段。",
                "可以被结构化为%s字段。",
                "可以被归一化为%s字段。"
            ]
            param_desc += random.choice(parse_list) % '和'.join(new_key)

        response += "- `{}`：{}\n".format(param, param_desc)

    if pad_json:
        parse_list2 = [
            "如下为解析后的json体：",
            "以下是规范化为json后的结果：",
            "格式化后的json体为：",
            "结构化的json是：",
            "归一化后的日志json是："
        ]
        new_format_info = {}
        for k, v in format_info.items():
            if isinstance(v, str) and len(v) > 30:
                v = v[:30] + "..."
            new_format_info[k] = v
        response += "\n" + random.choice(parse_list2) + "\n```\n" + json.dumps(new_format_info, ensure_ascii=False, indent=4) + "\n```\n"

    return response


def prompt_1(raw_info, format_info, file_name, pad_json=False):
    my_key_map = {}
    for k, v in key_map.items():
        my_key_map[k] = v

    if file_name.lower().startswith("4a"):
        for k, v in key_map_4a.items():
            my_key_map[k] = v

    if file_name.lower().startswith("linux"):
        for k, v in key_map_linux.items():
            my_key_map[k] = v

    if file_name.lower().startswith("tac"):
        for k, v in key_map_tac.items():
            my_key_map[k] = v

    if ("安恒" in file_name) and ("edr" in file_name.lower()):
        for k, v in key_map_anheng.items():
            my_key_map[k] = v

    if ("瑞数" in file_name) and ("waf" in file_name.lower()):
        for k, v in key_map_ruishu_WAF.items():
            my_key_map[k] = v

    q_list = [
        "请分析一下这个告警日志中，这个事件的详细内容。日志：%s",
        "请给出这个告警日志详细分析。日志：%s",
        "分析一下这个告警日志。%s",
        "详细分析一下这个日志。%s",
        "详细分析一下这个告警日志的内容。%s",
        "%s，给出这个这个日志的详细内容?",
        "%s，解析这个日志",
        "%s，详细分析这个告警日志。",
        "分析这个告警日志: %s",
        "解析这个告警日志的内容: %s"
    ]
    q_list_json = [
        "请分析一下这个告警日志中，这个事件的详细内容，并输出归一化后的json结果。日志：%s",
        "请给出这个告警日志的详细分析，并输出解析后的json结果。日志：%s",
        "分析一下这个告警日志，并输出规范化后的json结果。%s",
        "详细分析一下这个日志，并输出归一化后的json结果。%s",
        "详细分析一下这个告警日志的内容，并输出解析后的json结果。%s",
        "%s，给出这个这个日志的详细内容?并输出归一化后的json结果",
        "%s，解析这个日志，并输出归一化后的json结果",
        "%s，详细分析这个告警日志，并输出规范化后的json结果。",
        "分析这个告警日志，并输出解析后的json结果: %s",
        "解析这个告警日志的内容，并输出归一化后的json结果: %s"
    ]
    if pad_json:
        use_q_list = q_list_json
    else:
        use_q_list = q_list

    log_type_json = random.choice([True, False])
    key_value_split = ""
    params_split = ""
    if log_type_json != True:
        key_value_split = random.choice(["=", ":", "/"])
        params_split = random.choice(["&", ",", " ", "|"])

    question =  random.choice(use_q_list) % (make_log(raw_info, log_type_json, key_value_split, params_split))
    response = make_format_response(raw_info, format_info, my_key_map, key_value_split, params_split, pad_json=pad_json)

    prompts = {"question": question,
               "answer": response}
    return prompts


def prompt_2(raw_info, format_info):
    q_list_json = [
        "这里有一条告警日志，将其转化为归一化后的告警日志。只用输出json结果，不要说其他的。日志：%s",
        "请给出这个告警日志解析后的json结果。只用输出json结果，不要说其他的。日志：%s",
        "分析一下这个告警日志，输出规范化后的json结果。只用输出json结果，不要说其他的。%s",
        "详细分析一下这个日志，并输出归一化后的json结果。只用输出json结果，不要说其他的。%s",
        "解析这个告警日志的内容，输出解析后的json结果。只用输出json结果，不要说其他的。%s",
        "%s，把这个告警日志归一化为json格式",
        "%s，解析这个日志，并输出归一化后的json结果。只用输出json结果，不要说其他的。",
        "%s，输出这个告警日志规范化后的json结果。",
        "把这条告警日志归一化为json格式: %s",
        "解析这个告警日志的内容，并输出归一化后的json结果。只用输出json结果，不要说其他的。: %s"
    ]
    prompts = {"question": random.choice(q_list_json) % raw_info,
               "answer": "```\n" + json.dumps(format_info, ensure_ascii=False, indent=4) + "\n```\n"}
    return prompts


def prompt_3(raw_info, format_info):
    # q_list_json = [
    #     "请分析一下这个告警日志中的详细内容，并输出归一化后的json结果。日志：%s",
    #     "请给出这个告警日志对应的详细分析，并输出解析后的json结果。日志：%s",
    #     "分析一下这个告警日志，并输出规范化后的json结果。%s",
    #     "详细分析一下这个日志，并输出归一化后的json结果。%s",
    #     "详细分析一下这个告警日志的内容，并输出解析后的json结果。%s",
    #     "%s，给出这个这个日志的详细内容?并输出归一化后的json结果",
    #     "%s，解析这个日志，并输出归一化后的json结果",
    #     "%s，详细分析这个告警日志，并输出规范化后的json结果。",
    #     "分析这个告警日志，并输出解析后的json结果: %s",
    #     "解析这个告警日志的内容，并输出归一化后的json结果: %s"
    # ]

    q_list_json = [
        "将这个事件日志归一化为json格式，并解释归一化后每个字段的含义。日志：%s",
        "把这个告警日志解析成标准的json格式，然后给出json中每个字段的意思。日志：%s",
        "这是一个事件日志，输出规范化后的json结果，然后分析一下这个告警日志。%s",
        "输出事件日志归一化后的json结果，详细分析一下这个日志。%s",
        "输出解析后的json结果，然后详细分析一下这个告警日志的内容。%s",
        "%s，输出归一化后的json结果，然后给出这个这个日志的详细内容?",
        "%s，输出归一化后的json结果，并解释json中每个字段的含义",
        "%s，给出规范化后的json结果，并详细分析这个告警日志。",
        "输出解析后的json结果，并分析这个告警日志: %s",
        "输出归一化后的json结果，并解析这个告警日志的内容: %s"
    ]
    question = random.choice(q_list_json) % raw_info

    response = "这是一个安全告警日志，它记录了一个特定告警事件的详细信息。\n"

    parse_list2 = [
        "如下为解析后的json体：",
        "以下是规范化为json后的结果：",
        "格式化后的json体为：",
        "结构化的json是：",
        "归一化后的日志json是："
    ]
    new_format_info = {}
    for k, v in format_info.items():
        if (str(v) not in raw_info) or (k not in target_key_desc):
            continue

        # if isinstance(v, str) and len(v) > 100:
        #     v = v[:10] + "..."

        new_format_info[k] = v

    response += "\n" + random.choice(parse_list2) + "\n```\n" + json.dumps(new_format_info, ensure_ascii=False,
                                                                           indent=4) + "\n```\n"

    response += "\n其中每个字段的解释如下："
    for key, value in new_format_info.items():
        desc = target_key_desc[key]
        response += "\n- `%s`: %s" % (key, desc)

    prompts = {"question": question,
               "answer": response}
    return prompts


import os

data_list = []

# data_dir1 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA"
# data_list1 = os.listdir(data_dir1)
# data_list += [(data_dir1, x) for x in data_list1]


data_dir2 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA2\DATA"
data_list2 = os.listdir(data_dir2)
data_list += [(data_dir2, x) for x in data_list2]


data_dir3 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\广西电信_ALL_DATA\DATA"
data_list3 = os.listdir(data_dir3)
data_list += [(data_dir3, x) for x in data_list3]

data_dir4 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\重庆移动_ALL_DATA\DATA"
data_list4 = os.listdir(data_dir4)
data_list += [(data_dir4, x) for x in data_list4]


data_dir5 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\DATA-0810\DATA"
data_list5 = os.listdir(data_dir5)
data_list += [(data_dir5, x) for x in data_list5]


all_data = []

for data_dir, data_name in data_list:
    raw_info = None
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line == "":
                continue
            if raw_info is None:
                raw_info = line
            elif line[0] != "{":
                raw_info = line
            else:
                all_data.append({"raw_info": raw_info, "format_info": line, "data_name": data_name})

                format_data = json.loads(line.strip())
                if (format_data['=vender'] != "绿盟") and (format_data['product_type'] != "4A") and (format_data['product_type'] != "DAS"):
                    for i in range(4):
                        all_data.append({"raw_info": raw_info, "format_info": line, "data_name": data_name})

                if (format_data['product_type'] == "TAC") or (format_data['product_type'] == "EDR"):
                    for i in range(10):
                        all_data.append({"raw_info": raw_info, "format_info": line, "data_name": data_name})

                raw_info = None
                line = None


print("all data size: %s" % len(all_data))



new_data = []
for point in tqdm(all_data):
    raw_line = point['raw_info']
    format_line = point['format_info']
    data_name = point['data_name']

    # '='开头的为额外补充的字段，去除
    format_data = {}
    for k, v in json.loads(format_line).items():
        if (k[0] != "=") and (len(str(v).strip()) > 0) and (re.compile("[\u4e00-\u9fa5a-zA-Z0-9]").search(str(v))):
            format_data[k] = v

    if raw_line[0] == "[" or raw_line[0] == '{':
        raw_data = json.loads(raw_line.strip())
        raw_data, format_data = clean_info(raw_data, format_data, data_name)

        # prompt = prompt_1(point["raw_info"], point["format_info"], pad_json=False)
        # new_data.append(prompt)
        prompt = prompt_1(raw_data, format_data, data_name, pad_json=True)
        new_data.append(prompt)

    else:
        prompt = prompt_3(raw_line, format_data)
        new_data.append(prompt)


print("use data size: %s" %  len(new_data))
with open('./alert_info_parse_1129.json', 'w', encoding='utf-8') as fp:
    for line in new_data:
        line = json.dumps(line, ensure_ascii=False)
        fp.write(line + '\n')


