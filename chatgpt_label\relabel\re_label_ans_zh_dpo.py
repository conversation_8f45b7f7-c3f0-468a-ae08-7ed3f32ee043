# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset





async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt_text", answer_name="secllm_answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "secllm-v2",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 1000  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




print("read data...")
with open("/home/<USER>/llm_dataset/re_label/re_lable_answer.json", "r", encoding="utf-8") as f:
    data = json.load(f)

data = [x for x in data if len(x['re_answer']) > 0]


def make_conv(prompt):
    try:
        prompt = prompt.split("中文答案：")
        question = prompt[0].split("中文问题：")[1].strip()
        answer = prompt[1].strip()
        if (len(question) <= 0) or (len(answer) <= 0):
            return []
        conv = [{"from": "human", "value": question},
                {"from": "assistant", "value": answer}]
        return conv
    except:
        return []

all_data = []
for point in data:
    conv_list = make_conv(point["re_answer"])
    if len(conv_list) > 0:
        new_point = {
            "question": point["question"],
            "data_source": point["data_source"],
            "data_index": point["data_index"],
            "prompt_text": conv_list[0]["value"],
            "Qwen_answer": conv_list[1]["value"]
        }
        all_data.append(new_point)
    


def filter_name(point):
    filer_list = ["千问", "通义", "阿里云", "阿里巴巴", "蚂蚁金服", "达摩院"]
    for key in filer_list:
        if key in point["Qwen_answer"]:
            return False
    return True

all_data = [x for x in all_data if filter_name(x)]


print("\nata size: %s" % len(all_data))

print("\n\nprompt over. prompt example:\n%s" % all_data[0]["prompt_text"])



print("\n\ndata size: %s" %  len(all_data))
print("\ndata example: \n\n%s" % all_data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(all_data))

print("Done")


with open("/home/<USER>/llm_dataset/re_label/re_lable_v3_answer_dpo.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
