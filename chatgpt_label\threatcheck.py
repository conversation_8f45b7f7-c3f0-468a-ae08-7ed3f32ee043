# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function
import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd


es_hosts = ["***********:9200"]
es = Elasticsearch(es_hosts, http_auth=('elastic', 'owl@2019'), timeout=60)


def search_text_ex(entity_id, query_ind):
    # 从ES中查询 实体 基础信息

    query = {
      "query":{
        "ids":{
                  "values": [entity_id]
                }

      },
      "size": 3
    }
    data = es.search(index=query_ind, body=query, size=1)
    data = data['hits']['hits']
    if len(data) > 0:
        return True
    else:
        return False


def make_es_match_obj(match_type, match_key, match_value):
    match_obj = {
        match_type: {
            match_key: match_value
        }
    }
    return match_obj


def make_es_match_multi(match_type, match_list):
    match_obj = {
        'bool': {
            match_type: match_list
        }
    }
    return match_obj


path = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-04\alarms_labeled_anonymized.xlsx"
threat_data = pd.read_excel(path, sheet_name=None, dtype=str)
threat_data = threat_data["Sheet1"].fillna("")
print("threat data size: %s" % threat_data.shape[0])
print("data columns: ")
for col in threat_data.columns:
    print("%s: %s" % (col, threat_data[col].values[0]))

for index, row in tqdm(threat_data.iterrows()):
    row = row.to_dict()

    url = "http://10.24.107.199:5000/api/v1/ml/ernie_ie/security_operation"
    try:
        headers = {}
        params = {
            "log_message": row.get("log_message", ""),
            "query_body": row["q_body_anonymized"]
        }
        r = requests.post(url, json=params, headers=headers)
        if r.status_code == 200:
            result = r.json()["data"]["message"]
            chatgpt_result = {
                "chat_history": json.dumps(result)
            }
            if len(result) >=3:
                assert result[2]["role"] == "assistant"
                chatgpt_result["q_decode"] = result[2]["content"]
            else:
                chatgpt_result["q_decode"] = ""

            if len(result) >=5:
                assert  result[4]["role"] == "assistant"
                chatgpt_result["simple_analysis"] = result[4]["content"]
            else:
                chatgpt_result["simple_analysis"] = ""

            if len(result) >=7:
                assert result[6]["role"] == "assistant"
                chatgpt_result["danger_analysis"] = result[6]["content"]
            else:
                chatgpt_result["danger_analysis"] = ""

            if len(result) >=9:
                assert result[8]["role"] == "assistant"
                chatgpt_result["analysis_detail"] = result[8]["content"]
            else:
                chatgpt_result["analysis_detail"] = ""

            row["chatgpt_analysis"] = chatgpt_result

            if search_text_ex(row['log_id'], 'model_threatcheck_history'):
                es.update(index='model_threatcheck_history', id=row['log_id'], body={"doc": row})
            else:
                es.index(index='model_threatcheck_history', doc_type='_doc', id=row['log_id'], body=row)

        else:
            print("data %s rest error: %s" % (index,  r.text))

    except Exception as ex:
        print("data %s error: %s" % (index,  str(ex)))


all_data = es.search(index="model_threatcheck_history", body={"query": {"match_all": {}}}, size=1000)
output = []
for data in all_data["hits"]["hits"]:
    point =  data["_source"]
    point['chatgpt_analysis']['chat_history'] = json.dumps(json.loads(point['chatgpt_analysis']['chat_history']), ensure_ascii=False)
    es.update(index='model_threatcheck_history', id=point['log_id'], body={"doc": point})
    output.append(data["_source"])

with open('./alarms_chatgpt_labeled.json', 'w', encoding='utf-8') as fp:
    json.dump(output, fp, indent=4, ensure_ascii=False)


print("Done")
