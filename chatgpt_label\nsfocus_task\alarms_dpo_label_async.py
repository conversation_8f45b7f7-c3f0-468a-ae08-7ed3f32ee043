# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm


# 简单模板
TEMPLATE = '''
你是一名网络安全专家，需要对IDS安全告警所指示的网络活动进行分析。过程中需要注意：
1、分析结果的格式严谨，应当包含以下内容：分析步骤、结论、评定分类；
2、原始载荷经过预处理，对部分编码块进行了解码，并剔除了低价值的高熵数据块，这些预处理标记以“〔〕”符号包裹。此外还对部分敏感信息进行了模糊化，替换后的信息以“Mask_”开头。
3、尽量避免针对具体通用漏洞和具体利用工具的特征，而以其漏洞大类取代之（例如，只称“OGNL代码注入”，而不强调Struts2 S2-045漏洞）。
4、分析结论应尽可能严谨。如果由于告警载荷不完整、缺少上下文等原因导致无法得出明确结论，应当直接指出，而不应过度猜测。
5、着重评估攻击者的攻击意图（攻击者只是想要判断漏洞是否存在，还是想要实际利用漏洞；实际利用漏洞时，攻击者希望达到何种具体目的；等等）。

分析结果的末尾需进行分类评定，并遵循以下标准：

1、是否攻击：
a) 非攻击：正常业务中可能出现的行为；
b) 攻击：正常业务中不可能出现的、必定包含恶意意图的行为；
c) 无法判断：告警载荷残缺、加密、不完整压缩、非标准协议等导致内容难以分析，无法推测行为意图的情况；
注意：如果不能确定是否关联到攻击行为，会优先判定为非攻击，即使其在一定程度上可疑；

2、仅在判断为“攻击”的前提下，安全专家会进行攻击结果分类和攻击意图分类。
其中攻击结果分类包括：成功、失败、无法判断 三者之一：
a) 成功：有证据表明当前攻击成功（如响应报文出现恶意命令执行结果回显），或指示受害主机已经失陷（如请求报文包含）；
b) 失败：有证据表明当前攻击失败（如响应报文完整且不含攻击预期结果）；
c) 无法判断：无法确定攻击是否成功（如响应报文不完整、缺少上下文等）；

其中攻击意图分类包括：试探性、利用性 两者之一：
a) 利用性：攻击者试图利用漏洞，达到某种具体目的，如获取敏感信息、篡改系统配置、部署后门、建立代理隧道等；
b) 试探性：攻击者仅试图检测目标主机是否存在某种漏洞，或收集某些并不敏感的信息，而不对实际业务造成影响；
注意：如果已有信息难以推断攻击者的具体意图，安全专家会优先判定为试探性攻击；

分析格式如下：
[分步的分析步骤]
结论：[分析的结论]
是否攻击：[可用值包括：非攻击、攻击、无法判断]
攻击结果：[仅在“是否攻击”判断为“攻击”的前提下有此结果，可用值包括：成果、失败、无法判断]
攻击意图：[仅在“是否攻击”判断为“攻击”的前提下有此结果，可用值包括：利用性、试探性]

'''

example_list = [
    '''
1、HTTP请求首部“X-Test”包含硬编码的操作系统命令，因此排除正常业务，并推测为命令注入攻击；
2、所执行的命令“expr 41182 - 1142735”仅测试常量表达式，即使目标主机确实存在漏洞也不会造成影响，因此推测为漏洞探测行为；
3、所测试的常量表达式“41182 - 1142735”较长而复杂，人工测试时很少会手动输入这样的表达式，因此推测为自动化行为；
4、由于缺少响应载荷信息，无法判断攻击是否成功；
结论：攻击者试图检测目标主机是否存在某种命令注入漏洞。
是否攻击：攻击
攻击结果：判断
攻击意图：试探性
''',
    '''
1、HTTP请求正文参数包含复杂的PHP代码，且部分参数经过Base64编码，意在绕过安全过滤。因此排除正常业务，并推测为PHP远程代码执行攻击；
2、请求URL“/uploads/1.php”疑似为用户上传的PHP脚本，且不存在任何与PHP代码执行过程无关的其它请求参数，因此推测为WebShell活动；
3、所执行的代码“fwrite(fopen(base64_decode($_POST["r853efb3e77925"]),"w"),base64_decode($_POST["c990db2d5ac0ae"]))”用于将Base64解码后的内容“<FilesMatch \\.ctfhub$>\n    SetHandler application/x-httpd-php\n</FilesMatch>”写入到文件“/var/www/html/123/.htaccess”中；
4、该操作意在修改Apache服务器的请求处理器映射，允许PHP后门脚本以“*.ctfhub”后缀执行，从而逃避针对php文件的反病毒或其它安全检测机制；
5、操作目标文件路径“/var/www/html/123/.htaccess”并非默认存在，攻击者很可能已经获取了目的主机的部分文件系统信息。因此排除漏洞探测行为，并推测目的主机可能已经失陷；
结论：攻击者通过WebShell下发命令，以部署隐藏后门，其中目的主机可能已经失陷。
是否攻击：攻击
攻击结果：成功
攻击意图：利用性
''',
    '''
1、告警载荷可能并不完整，其内容疑似为某种序列化块的片段；
2、告警载荷中可见一些形似HTTP协议字段的字符串，但并不构成完整的HTTP报文；
3、告警载荷中还包含"javax.servlet.include.request_uri"等与Java Servlet相关的内容，但没有发现与已知攻击模式相符的特征；
结论：某种使用序列化数据的通信行为，但其行为意图难以辨识。
是否攻击：无法判断
''',
    '''
1、HTTP GET请求的URL为“/OA_HTML/AppsLocalLogin.jsp?langCode=ZHS”，可能与登录过程有关；
2、告警载荷中没有发现形似认证凭据的字段，因此排除登录行为，推测正在获取一个登录页面；
3、告警载荷中没有发现任何攻击特征或畸形字段，因此推测为正常业务；
结论：用户尝试加载登录页面。
是否攻击：非攻击
''',
    '''
1、HTTP POST请求的URL为“/rtrlet/rtr”，可能与某种远程管理工具有关；
2、请求正文参数包含“file=C:\\WINDOWS\\system32\\drivers\\etc\\hosts”，推测为试图获取目标主机的hosts文件；
3、hosts文件通常不包含较多敏感信息，因此推测为漏洞探测行为；
4、响应状态为“400 Bad Request”，推测攻击并未成功；
结论：攻击者试图通过某种远程管理工具获取目标主机的hosts文件，但未能成功。
是否攻击：攻击
攻击结果：失败
攻击意图：试探性
''',
    '''
1、告警载荷可能并不完整，但如此复杂的命令在正常业务中非常罕见。因此排除正常业务，并推测为远程命令执行攻击；
2、命令的功能包括寻找可访问的目录、从“http://**************/8UsA.sh”和“ftp://**************/t8UsA.sh”下载Shell脚本并执行它们、然后删除该目录中的文件以清除痕迹；
3、这些命令具备高度进攻性和自动化，因此推测为蠕虫病毒活动，其中文件下载主机“**************”推测为攻击者所控制的C&C投递服务器；
结论：攻击者试图向目的主机部署恶意脚本。
是否攻击：攻击
攻击结果：无法判断
攻击意图：利用性
''',
    '''
1、HTTP GET请求的URL为“//cdsxxporxxx/baseControl/js/editor/FCKeditor/editor/filemanager/browser/default/browser.html”，可能与FCKeditor的文件管理器有关；
2、请求报文中没有发现形似认证凭据的字段，因此排除登录行为，推测正在获取一个文件管理器页面；
3、请求的Referer字段为“http://www.baidu.com”，但富文本编辑器的文件管理功能很少从站外链接，因此推测Referer字段是伪造的；
4、响应状态为“200 OK”，但响应正文并非FCKeditor页面，而是Nginx默认欢迎页，因此推测攻击并未成功；
结论：攻击者尝试检测目标服务是否存在FCKeditor的文件管理器组件，但未能成功。
是否攻击：攻击
攻击结果：失败
攻击意图：试探性
'''
]


async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://10.24.45.213:8080/v1/chat/completions"
        messages = []
        for p in point['conversation'][:-1]:
            messages.append({"role": p['from'], "content": p['value']})
        body = {
            "model":"secllm-v2-format",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.8,
            "frequency_penalty": 0.2,
            "max_tokens": 8000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""

        point['secllm_answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 30  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



data = pd.read_excel(r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2024-01/2023年11月告警分析数据标注（20240102完整第一批更新告警分类标准_替换后）.xlsx")

data_list = []


for ind, point in enumerate(data.iterrows()):
    point = point[1].to_dict()

    # # 没有结果示例
    # new_point = copy.deepcopy(point)
    # conversation = [
    #     {"from": "system", "value": TEMPLATE},
    #     {"from": "user", "value": new_point["ModelInput"]},
    #     {"from": "assistant", "value": new_point["ModelOutput"]}
    # ]
    # new_point["conversation"] = conversation
    # new_point["conversation_id"] = new_point["ID"] + "-0"
    # data_list.append(new_point)

    # 全部结果示例
    new_point = copy.deepcopy(point)
    out_example = "\n\n".join([x.strip() for x in example_list])
    conversation = [
        {"from": "system", "value": TEMPLATE.strip() + "\n\n以下是一些分析结果案例：\n\n" + out_example},
        {"from": "user", "value": new_point["ModelInput"]},
        {"from": "assistant", "value": new_point["ModelOutput"]}
    ]
    new_point["conversation"] = conversation
    new_point["conversation_id"] = new_point["ID"] + "-all"
    data_list.append(new_point)

    # # 采样结果示例
    # for i in range(5):
    #     new_point = copy.deepcopy(point)
    #     example_num = random.choice(range(len(example_list)))
    #     examples = random.sample(example_list, k=example_num)
    #     out_example = "\n\n".join([x.strip() for x in examples])
    #     conversation = [
    #         {"from": "system", "value": TEMPLATE.strip() + "\n\n以下是一些分析结果案例：\n\n" + out_example},
    #         {"from": "user", "value": new_point["ModelInput"]},
    #         {"from": "assistant", "value": new_point["ModelOutput"]}
    #     ]
    #     new_point["conversation"] = conversation
    #     new_point["conversation_id"] = new_point["ID"] + "-" + str(i) + "-" + str(example_num)
    #     data_list.append(new_point)

print("data size: %s" %  len(data_list))
print("data example: ", data_list[0])


use_data = []
for point in  data_list:
    for i in range(5):
        new_point = copy.deepcopy(point)
        use_data.append(new_point)

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))

with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label_1-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label_1-o-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)

print("Done")



data = []

with open(r"D:\work\GPT\chatgpt_label/alarms_label_1-20240110.json", 'r', encoding='utf-8') as fp:
    data += json.load(fp)

with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label_1-20240122.json", 'r', encoding='utf-8') as fp:
    data += json.load(fp)

print("all data size: %s" %  len(data))

data = [x for x in data if len(x["secllm_answer"].strip()) > 0]
print("all data size after filter: %s" %  len(data))


# 先按ID合并，并解析结果，计算得分

def format_label(out_text):
    format_text = []
    out_text = out_text.split("\n")
    for i in  range(len(out_text)):
        format_text.append(out_text[i])

        if "是否攻击：" in  out_text[i]:
            if (len(out_text) > i + 1) and ("攻击结果：" in out_text[i+1]):
                format_text.append(out_text[i+1])
            else:
                break

            if  (len(out_text) > i + 2) and ("攻击意图：" in out_text[i+2]):
                format_text.append(out_text[i+2])
            else:
                break

            break
    format_text = [x.strip() for x in format_text if len(x.strip()) > 0]
    format_text = "\n".join(format_text)
    return format_text


def parse_label(out_text):
    label_text = {
        "是否攻击：": "",
        "攻击结果：": "",
        "攻击意图：": ""
    }
    out_text = out_text.split("\n")
    for line in out_text:
        for key in label_text:
            if key in line:
                label_text[key] = line.replace(key, "").strip()
    return label_text


def cal_score(label, pred):
    score = 0
    if label["是否攻击："] != pred.get("是否攻击：", ""):
        return 0
    for k, v in  label.items():
        v_pred = pred.get(k, "")
        if v == v_pred:
            score += 1
    return score


use_data = {}
for point in data:
    if point["ID"] not in use_data:
        use_data[point["ID"]] = {
            "conversation_list": [],
            "secllm_answer_list": [],
            "label": parse_label(point["ModelOutput"])
        }
        for k, v in point.items():
            if k not in ["conversation", "conversation_id", "secllm_answer"]:
                use_data[point["ID"]][k] = v

    format_answer = format_label(point["secllm_answer"])
    secllm_label = parse_label(format_answer)
    score = cal_score(use_data[point["ID"]]["label"], secllm_label)
    use_data[point["ID"]]["conversation_list"].append(
        {
            "conversation": point["conversation"],
            "conversation_id": point["conversation_id"]
        }
    )
    use_data[point["ID"]]["secllm_answer_list"].append(
        {
            "secllm_answer": point["secllm_answer"],
            "format_answer": format_answer,
            "secllm_label": secllm_label,
            "score": score
        }
    )


for point_id, point in use_data.items():
    conversation_list = []
    use_id = set()
    for conversation in point["conversation_list"]:
        if conversation["conversation_id"] not in use_id:
            conversation_list.append(conversation)
            use_id.add(conversation["conversation_id"])
    point["conversation_list"] = conversation_list
    point["secllm_answer_list"] = sorted(point["secllm_answer_list"], key=lambda x: x["score"], reverse=True)


with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)





# with open(r"D:\work\GPT\chatgpt_label/alarms_dpo_label.json", 'r', encoding='utf-8') as fp:
#     use_data = json.load(fp)
#
# dpo_pair = []
#
# for point_id, point in use_data.items():
#     conversation_map = {}
#     for conversation in point["conversation_list"]:
#         conversation_map[conversation["conversation_id"]] = conversation["conversation"][0]["value"]
#     prompt_example_all = conversation_map[point["ID"] + "-all"]
#     prompt_example_0 = conversation_map[point["ID"] + "-0"]
#     prompt_example_other = [v for k, v in conversation_map.items() if (k != prompt_example_all) and (k != prompt_example_0)]
#
#     chose_prompt_example = [prompt_example_all, prompt_example_0] + random.sample(prompt_example_other, k=2)
#
#     answer_map = {}
#     for answer in point["secllm_answer_list"]:
#         if answer["score"] not in answer_map:
#             answer_map[answer["score"]] = []
#         answer_map[answer["score"]].append(answer)
#     score_list = sorted([x for x in answer_map.keys()], reverse=True)
#
#     for  _, chose_prompt in enumerate(chose_prompt_example):
#         for i, score_chosen in enumerate(score_list):
#             for _ in range(5):
#                 chose_answer = random.choice(answer_map[score_chosen])
#                 new_point = {k: v for k, v in point.items() if k not in ["secllm_answer_list", "conversation_list", "Request", "Response"]}
#                 new_point["prompt_text"] = point["ModelInput"].strip()
#                 new_point["chosen_text"] = point["ModelOutput"].strip()
#                 new_point["reject_text"] = chose_answer["secllm_answer"].strip()
#                 new_point["system"] = chose_prompt.strip()
#                 dpo_pair.append(new_point)
#
#             for j, score_reject in enumerate(score_list[i+1:]):
#                 for _ in range(5):
#                     chose_answer = random.choice(answer_map[score_chosen])
#                     reject_answer = random.choice(answer_map[score_reject])
#                     new_point = {k: v for k, v in point.items() if k not in ["secllm_answer_list", "conversation_list", "Request", "Response"]}
#                     new_point["prompt_text"] = point["ModelInput"].strip()
#                     new_point["chosen_text"] = chose_answer["secllm_answer"].strip()
#                     new_point["reject_text"] = reject_answer["secllm_answer"].strip()
#                     new_point["system"] = chose_prompt.strip()
#                     dpo_pair.append(new_point)
#
#




