{"questions": [{"question": "What is the primary requirement for an Random Bit Generator's (RBG) output to be used for generating cryptographic keys?", "answers": {"A": "The RBG's output should have a length that matches the target data to be protected.", "B": "The RBG's output should be computationally indistinguishable from random bits and provide sufficient entropy to support the security strength required for the target data.", "C": "The RBG's output should be as long as possible to ensure maximal randomness.", "D": "The RBG's output should have precisely the same length as the symmetric key to be generated."}, "solution": "B"}, {"question": "In cryptography, what is the purpose of using a key-derivation function (KDF)?", "answers": {"A": "Generate public keys", "B": "Authenticate digital signatures", "C": "Encrypt data using a password", "D": "KDF are algorithms used to transform a secret into crucial parameters like keys and Initialization Vectors (IVs)."}, "solution": "D"}, {"question": "What is the primary purpose of segmentation in the context of PCI DSS?", "answers": {"A": "To reduce the number of PCI DSS requirements applicable to an entity", "B": "To limit the scope of the PCI DSS assessment and minimize the potential for security breaches", "C": "To completely remove the applicability of PCI DSS for a merchant's cardholder data environment", "D": "To eliminate the need for implementing PCI DSS controls"}, "solution": "B"}, {"question": "What is the purpose of using strong cryptography to protect cardholder data during transmission over open, public networks?", "answers": {"A": "To ensure the data is secured from unauthorized access or interception during transmission.", "B": "To increase the speed of data transmission over open, public networks.", "C": "To provide access to authorized individuals to monitor the transmission process.", "D": "To improve the efficiency of network routing protocols."}, "solution": "A"}, {"question": "What is the purpose of periodic reviews of user accounts and access privileges?", "answers": {"A": "To ensure all user accounts have access to the highest privileges.", "B": "To demonstrate adherence to regulatory requirements.", "C": "To create a record of all user accounts", "D": "To identify and remove any inappropriate access and privileges."}, "solution": "D"}, {"question": "Which of the following is a purpose of having synchronized system clocks?", "answers": {"A": "To compare log files from different systems.", "B": "To accelerate system performance.", "C": "To standardize file naming conventions.", "D": "To reduce power consumption."}, "solution": "A"}, {"question": "What is physical security?", "answers": {"A": "The protection of data from hacker attacks.", "B": "The act of securing a building or location to prevent unauthorized access, theft, or damage to physical assets.", "C": "The prevention of natural disasters caused by environmental factors.", "D": "The enforcement of technical security controls to prevent data breaches."}, "solution": "B"}, {"question": "What does the abbreviation 'SDK' stand for in the context of software security?", "answers": {"A": "Software Development Kit", "B": "System Deflection Key", "C": "Software Delegation Kernel", "D": "System Development Key"}, "solution": "A"}, {"question": "What is the main purpose of multi-factor authentication in cybersecurity?", "answers": {"A": "To limit user privileges within the network", "B": "To ensure compliance with government regulations", "C": "To adds an extra layer of security by requiring multiple forms of verification.", "D": "To monitor network traffic for security threats"}, "solution": "C"}, {"question": "What is the primary objective of a Web Application Firewall (WAF) in cybersecurity?", "answers": {"A": "To encrypt network traffic between web servers", "B": "To prevent unauthorized access to network services", "C": "To protect web applications from security threats", "D": "To manage user authentication and authorization for web services"}, "solution": "C"}, {"question": "What is the purpose of periodically changing passwords or passphrases?", "answers": {"A": "To provide more time for a malicious individual to crack the password/passphrase", "B": "To allow for more rapid detection and response to address potentially compromised credentials", "C": "To prevent unauthorized users from gaining access to the user account through use of a shared authentication factor", "D": "To offer less time for a malicious individual to crack a password/passphrase and less time to use a compromised password"}, "solution": "D"}, {"question": "Which of the following defines the entity’s security objectives and principles?", "answers": {"A": "Security policies", "B": "Operational procedures", "C": "Risk assessment framework", "D": "Incident response plan"}, "solution": "A"}, {"question": "What is the focus of a security audit or vulnerability assessment?", "answers": {"A": "Locating vulnerabilities", "B": "Locating threats", "C": "Enacting threats", "D": "Exploiting vulnerabilities"}, "solution": "A"}, {"question": "Which of the following is a mechanism for managing digital certificates through a system of trust?", "answers": {"A": "PKI", "B": "PKCS", "C": "ISA", "D": "SSL"}, "solution": "A"}, {"question": "Which protocol is used to create a secure environment in a wireless network?", "answers": {"A": "WAP", "B": "WPA2", "C": "WTLS", "D": "WML3"}, "solution": "B"}, {"question": "Which design concept limits access to systems from outside users while protecting users and systems inside the LAN?", "answers": {"A": "DMZ", "B": "VLAN", "C": "I&A", "D": "Router"}, "solution": "A"}, {"question": "What is the process of making an operating system secure from attack called?", "answers": {"A": "Hardening", "B": "Tuning", "C": "Sealing", "D": "Locking down"}, "solution": "A"}, {"question": "Which mechanism is used by PKI to allow immediate verification of a certificate's validity?", "answers": {"A": "CRL", "B": "MD5", "C": "SSHA", "D": "OCSP"}, "solution": "D"}, {"question": "Which of the following is a major security problem with FTP?", "answers": {"A": "Password files are stored in an unsecure area on disk.", "B": "Memory traces can corrupt file access.", "C": "User IDs and passwords are unencrypted.", "D": "FTP sites are unregistered."}, "solution": "C"}, {"question": "Which of the following creates a fixed-length output from a variable-length input?", "answers": {"A": "MD5", "B": "SHA1", "C": "SHA3", "D": "All of the above"}, "solution": "D"}, {"question": "What is the purpose of a service-level agreement (SLA) in the context of business continuity?", "answers": {"A": "To define the obligations of the service provider to the client", "B": "To provide high-availability architecture", "C": "To outline a disaster recovery plan", "D": "To ensure the fault tolerance of systems"}, "solution": "A"}, {"question": "What does the Address Resolution Protocol (ARP) do in a network?", "answers": {"A": "Resolves domain names to IP addresses.", "B": "Allocates IP addresses to network devices.", "C": "Translates IP addresses to media access control (MAC) addresses.", "D": "Verifies that a host is reachable."}, "solution": "C"}, {"question": "In the OSI model, which layer consists of the physical media and infrastructure of networks?", "answers": {"A": "Session", "B": "Presentation", "C": "Data Link", "D": "Physical"}, "solution": "D"}, {"question": "In binary representation, what is the result of the bitwise XOR operation between 110101 and 101010", "answers": {"A": "011111", "B": "100111", "C": "0", "D": "111111"}, "solution": "A"}, {"question": "What is the proper sequence of the TCP three-way-handshake?", "answers": {"A": "SYN-ACK, ACK, ACK", "B": "SYN, SYN-ACK, ACK", "C": "SYN-SY<PERSON>, SYN-ACK, SYN", "D": "ACK, SYN-ACK, SYN"}, "solution": "B"}, {"question": "Which topology has built-in redundancy because of its many client connections?", "answers": {"A": "Token ring", "B": "Bus", "C": "Hybrid", "D": "<PERSON><PERSON>"}, "solution": "D"}, {"question": "How can Google hacking be valuable in a cybersecurity investigation?", "answers": {"A": "To create fake Google search results", "B": "To extract data from Google's databases", "C": "To reveal hidden or sensitive information from Google search results", "D": "To manipulate website content in Google's index"}, "solution": "C"}, {"question": "What is the role of the three-way handshake in establishing a TCP connection?", "answers": {"A": "To request the establishment of a connection", "B": "To acknowledge the request for connection", "C": "To complete the establishment of a connection", "D": "All of the above"}, "solution": "D"}, {"question": "Which best describes a vulnerability scan?", "answers": {"A": "A way to find open ports", "B": "A way to diagram a network", "C": "A proxy attack", "D": "A way to automate the discovery of vulnerabilities"}, "solution": "D"}, {"question": "What is Tor used for?", "answers": {"A": "To make it more difficult to trace a user's Internet activity", "B": "To hide the process of scanning", "C": "To automate scanning", "D": "To hide the banner on a system"}, "solution": "A"}, {"question": "What is the maximum number of IP addresses in an IPv6 subnet?", "answers": {"A": "6 bit", "B": "32 bit", "C": "6*16 bit", "D": "8*16 bit"}, "solution": "D"}, {"question": "Which of the following is a characteristic of a worm?", "answers": {"A": "Requires user interaction to spread", "B": "Self-replicates and spreads independently", "C": "Aargets specific individuals or organizations", "D": "A worm is an item that runs silently."}, "solution": "B"}, {"question": "What common tool can be used for launching an ARP poisoning attack?", "answers": {"A": "Cain & Abel", "B": "Nmap", "C": "<PERSON>ooter", "D": "Tcpdump"}, "solution": "A"}, {"question": "What technology is used to verify information as it is entered into a web application, preventing database manipulation and buffer overflows?", "answers": {"A": "Input Validation", "B": "Cross-Site Scripting (XSS)", "C": "Session Management", "D": "Buffer overflow"}, "solution": "A"}, {"question": "Which type of firewall analyzes the status of traffic?", "answers": {"A": "Circuit level", "B": "Packet filtering", "C": "Stateful inspection", "D": "NIDS"}, "solution": "C"}, {"question": "What is the purpose of the Crossover Error Rate (CER) in rating the performance of biometric devices?", "answers": {"A": "To compare the effectiveness of different biometric factors", "B": "To compare the accuracy of similar biometric devices", "C": "To assess the acceptance level of biometric factors", "D": "To measure the throughput rate of biometric devices"}, "solution": "B"}, {"question": "What is the primary purpose of a padded cell system in relation to intrusion detection?", "answers": {"A": "Attract unauthorized users", "B": "Simulate a real network for intruders", "C": "Isolate detected intruders", "D": "Send alerts to administrators"}, "solution": "C"}, {"question": "What is the primary goal of penetration testing?", "answers": {"A": "To evaluate the robustness of security systems and identify vulnerabilities", "B": "To exploit vulnerabilities and cause damage to the system", "C": "To eliminate all vulnerabilities in the system", "D": "To simulate unauthorized attacks without actually exploiting vulnerabilities"}, "solution": "A"}, {"question": "What is the primary purpose of RADIUS and TACACS in a network environment?", "answers": {"A": "To centralize the authentication of remote dial-up connections", "B": "To establish secure connections for voice and video conferencing", "C": "To provide security for WAN communication technologies", "D": "To support remote journaling and electronic vaulting"}, "solution": "A"}, {"question": "Which symmetric encryption scheme is recommended for use in SRTP?", "answers": {"A": "AES in ECB mode", "B": "DES in CBC mode", "C": "RC4", "D": "AES in GCM mode"}, "solution": "D"}, {"question": "What is the essential requirement for the security of the Discrete Logarithm Integrated Encryption Scheme (DLIES) to be maintained?", "answers": {"A": "The order q of the base point P should be at least q ≥ 1024.", "B": "Randomly choose two primes p and q such that p*q > 2048 bits.", "C": "The length of the prime number p should be at least 3000 bits.", "D": "All of the above"}, "solution": "C"}, {"question": "Which document provides the recommendation for elliptic curve cryptography?", "answers": {"A": "NIST SP 800-56C", "B": "NIST SP 800-63-3", "C": "NIST SP 800-57 Part 1", "D": "NIST SP 800-186-4"}, "solution": "D"}, {"question": "What is an essential requirement for the internal state of deterministic random number generators?", "answers": {"A": "Protection against readout and manipulation", "B": "Constant reseeding requirement", "C": "Dependence on reliable physical resources", "D": "Regular update of entropy sources"}, "solution": "A"}, {"question": "Which federal government agency has responsibility for ensuring the security of government computer systems that are not used to process sensitive and/or classified information?", "answers": {"A": "National Security Agency", "B": "Federal Bureau of Investigation", "C": "National Institute of Standards and Technology", "D": "Secret Service"}, "solution": "C"}, {"question": "What is the purpose of implementing monitoring systems?", "answers": {"A": "Monitoring the configuration and configuration change of devices", "B": "Ensuring availability and functionality of systems", "C": "Recording access to information by means of system and security logging", "D": "Protection from compromising radiation"}, "solution": "B"}, {"question": "Which principle of cybersecurity focuses on ensuring that data is accurate and trustworthy?", "answers": {"A": "Resilience", "B": "Availability", "C": "Authenticity", "D": "Integrity"}, "solution": "D"}, {"question": "What is the primary goal of the Transport Layer Security (TLS) protocol?", "answers": {"A": "Privacy and authentication between two communicating applications", "B": "Privacy and data integrity between two communicating applications", "C": "Authentication and data integrity between two communicating applications", "D": "Privacy, authentication, and data integrity between two communicating applications"}, "solution": "D"}, {"question": "If you were to see the subnet mask *************, what CIDR notation (prefix) would you use to indicate the same thing?", "answers": {"A": "/23", "B": "/22", "C": "/21", "D": "/20"}, "solution": "C"}, {"question": "Which of the following is NOT one of the five disaster recovery plan testing types?", "answers": {"A": "Simulation", "B": "Checklist", "C": "Availability", "D": "Full Interruption"}, "solution": "C"}, {"question": "What would be the result of sending the string AAAAAAAAAAAAAAAAA into a variable that has been allocated space for 8 bytes?", "answers": {"A": "Heap spraying", "B": "SQL injection", "C": "Buffer overflow", "D": "Slowloris attack"}, "solution": "C"}, {"question": "Which of the following is NOT a form of computer/network surveillance?", "answers": {"A": "Keyboard monitoring", "B": "Use of network sniffers", "C": "Use of Closed-Circuit Television (CCTV)", "D": "Review of audit logs"}, "solution": "C"}, {"question": "How would you calculate risk?", "answers": {"A": "Probability / loss", "B": "Probability * mitigation factor", "C": "(Loss + mitigation factor) * (loss/probability)", "D": "Probability * loss"}, "solution": "D"}, {"question": "What is the common share name used for interprocess communication?", "answers": {"A": "C$", "B": "IPC$", "C": "ADMIN$", "D": "INTERCOM$"}, "solution": "B"}, {"question": "Which port is commonly used by SMB when operating over TCP without NetBIOS?", "answers": {"A": "137", "B": "139", "C": "135", "D": "445"}, "solution": "D"}, {"question": "Which of the following is an example of a strong password?", "answers": {"A": "Password123456", "B": "ME_Fgh4_al445!A", "C": "Qwerty!", "D": "123456"}, "solution": "B"}, {"question": "Which access control model would be found in a firewall?", "answers": {"A": "Mandatory access control", "B": "Discretionary access control", "C": "Role-based access control", "D": "Rule-based access control"}, "solution": "D"}, {"question": "What is the preferred encryption option for sending large amounts of data?", "answers": {"A": "Symmetric encryption", "B": "Asymmetric encryption", "C": "Public Key Cryptography", "D": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> scheme"}, "solution": "A"}, {"question": "Which of the following is an example of a hashing algorithm?", "answers": {"A": "AES", "B": "RSA", "C": "MD5", "D": "3DES"}, "solution": "C"}, {"question": "Which RAID level is not fault tolerant?", "answers": {"A": "RAID 1", "B": "RAID 0", "C": "RAID 5", "D": "RAID 6"}, "solution": "B"}, {"question": "How many keys exist in a public/private key pair?", "answers": {"A": "0", "B": "2", "C": "1", "D": "4"}, "solution": "B"}, {"question": "What is the primary goal of an Information Security Governance strategy?", "answers": {"A": "To minimize the impact of security incidents on the organization", "B": "To obtain senior management commitment and support", "C": "To enforce compliance with information security policies", "D": "To prioritize options to mitigate risks"}, "solution": "B"}, {"question": "What is the process of hiding text within an image called?", "answers": {"A": "Encryption", "B": "Steganography", "C": "Spyware", "D": "Hiding"}, "solution": "B"}, {"question": "What attack involves inserting an unauthorized person in the communication path between two users to intercept and modify packets?", "answers": {"A": "Known Plaintext Attack", "B": "<PERSON><PERSON>her-Text Attack", "C": "MITM Attack", "D": "Brute-Force Attack"}, "solution": "C"}, {"question": "Who is responsible for implementing the organization's planning, budgeting, and performance, including its information security components?", "answers": {"A": "Administrator", "B": "CISO", "C": "MGR", "D": "CEO"}, "solution": "B"}, {"question": "The primary responsibility of the information security steering committee is:", "answers": {"A": "Information security policy development", "B": "Direction setting and performance monitoring", "C": "Information security control implementation", "D": "Provision of information security training for employees"}, "solution": "B"}, {"question": "According to NIST guidelines, what is a recommended practice for creating a strong password?", "answers": {"A": "Implementing complex composition rules requiring a mix of character types", "B": "Setting mandatory periodic password changes for all users", "C": "Encouraging the use of longer, more memorable passphrases", "D": "Requiring the use of password hints for easier recall"}, "solution": "C"}, {"question": "What is the purpose of continuously improving cybersecurity guidelines and their application notes?", "answers": {"A": "To categorize security measures into groups", "B": "To react to new technologies, use cases, and risks", "C": "To prevent all identified threats", "D": "To eliminate all cybersecurity risks"}, "solution": "B"}, {"question": "What does DDoS stand for?", "answers": {"A": "Double Denial of Service", "B": "Distributed Denial of Service", "C": "Dual Denial of Services", "D": "Denial of Distributed Services"}, "solution": "B"}, {"question": "Which is not a common wireless network authentication mechanism?", "answers": {"A": "WEP", "B": "WPA", "C": "802.1X", "D": "RC4"}, "solution": "D"}, {"question": "Which technique aims to identify changes in normal routines by examining patterns of a user's activity?", "answers": {"A": "Vulnerability Assessment", "B": "Behavior-based IDS", "C": "Data Mining", "D": "User Profile Analysis"}, "solution": "B"}, {"question": "What is the primary purpose of conducting a risk analysis in cybersecurity?", "answers": {"A": "To assess the risk level for the organization", "B": "To formulate the most important IT security procedures for the organization", "C": "To create a risk assessment statement", "D": "To determine the level of exposure to identified threats and identify possible safeguards or controls"}, "solution": "D"}, {"question": "What is the primary advantage of using UDP over TCP?", "answers": {"A": "Reliability", "B": "Error recovery", "C": "Speed of data transfer", "D": "Data integrity"}, "solution": "C"}, {"question": "What does the CIA triad in cryptography emphasize?", "answers": {"A": "Confidentiality, Integrity, Authenticity", "B": "Confidentiality, Integrity, Availability", "C": "Confidentiality, Authenticity, Nonrepudiation", "D": "Confidentiality, Availability, Nonrepudiation"}, "solution": "B"}, {"question": "Which nmap command is used to perform a TCP SYN scan, also known as a half-open scan, to determine which ports are open on a target system?", "answers": {"A": "nmap -sP", "B": "nmap -sT", "C": "nmap -SYN", "D": "nmap -sS"}, "solution": "D"}, {"question": "What is the primary purpose of business continuity plans?", "answers": {"A": "To recover from natural disasters", "B": "To conduct regular audits of the organization's security systems", "C": "To minimize the effects of a disruptive event on a company", "D": "To increase the cost associated with a disruptive event"}, "solution": "C"}, {"question": "Which DoS attack sends large amounts of ICMP Echo traffic to a broadcast IP address with a spoofed source address of a victim?", "answers": {"A": "Smurf attack", "B": "SYN flood attack", "C": "Ping of Death", "D": "Botnet attack"}, "solution": "A"}, {"question": "Which of the following tools is commonly used for network packet analysis and troubleshooting?", "answers": {"A": "Wireshark", "B": "Metasploit", "C": "Nmap", "D": "Hydra"}, "solution": "A"}, {"question": "In the context of encrypting data for secure communication, which encryption algorithm is typically preferred for its speed and efficiency?", "answers": {"A": "RSA-2048", "B": "AES-256", "C": "Both of them", "D": "None of them"}, "solution": "B"}, {"question": "What is the purpose of NOP sleds in buffer overflow exploits?", "answers": {"A": "To encrypt the payload", "B": "To alter the content of the NOP register", "C": "To insert no-operation instructions for padding", "D": "To alter the content of the EIP register"}, "solution": "C"}, {"question": "Which security feature can help prevent buffer overflow attacks by marking certain memory regions as non-executable?", "answers": {"A": "Data Execution Prevention (DEP)", "B": "Buffer Overflow Prevention (BOP)", "C": "Intrusion Detection System (IDS)", "D": "Address Space Layout Randomization (ASLR)"}, "solution": "A"}]}