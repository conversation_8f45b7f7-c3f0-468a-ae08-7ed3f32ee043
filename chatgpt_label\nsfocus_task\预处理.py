import json
import random
from tqdm import tqdm
import re
import os

data_dir1 = r"F:\data_process\广西电信_ALL_DATA.tar\DATA"
data_dir2 = r"F:\data_process\重庆移动_ALL_DATA.tar\DATA"

# key_map目前已支持的几种设备类型
service_list = ["IPS-IDS", "UTS", "WAF_WEB", "4A", "Linux", "TAC"]
# key_map新增部分内容
key_map = {
    "msgtype": {
        "name": "msgtype",
        "description": "发送给ISOP的消息类型",
        "value_range": "13：入侵检测（吸星）\n14：入侵检测（zealot）\n17：DDos\n21：webshell\n31489：nti威胁情报\n63：SDK防病毒\n100：自定义情报\n101：自定义规则\n110：内网主机扫描\n130：翻墙行为告警\n131：违法网站告警\n132：违规应用告警",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "13": "入侵检测（吸星）",
            "14": "入侵检测（zealot）",
            "17": "DDos",
            "21": "webshell",
            "31489": "nti威胁情报",
            "63": "SDK防病毒",
            "100": "自定义情报",
            "101": "自定义规则",
            "110": "内网主机扫描",
            "130": "翻墙行为告警",
            "131": "违法网站告警",
            "132": "违规应用告警",
            "109": "钓鱼邮件告警"
        },
        "prompt_template": "这是安全日志的消息类型。",
        "trans_key": "msgtype"
    },
    "product": {
        "name": "product",
        "description": "产品标识",
        "type": "string",
        "prompt_template": "这是告警事件的产品标识。",
        "trans_key": "sip"
    },
    "dev_ip": {
        "name": "dev_ip",
        "description": "告警设备ip",
        "type": "string",
        "prompt_template": "这是告警事件的告警设备IP地址。",
        "trans_key": "dev_ip"
    },
    "devip": {
        "name": "devip",
        "description": "告警设备ip",
        "type": "string",
        "prompt_template": "这是告警事件的告警设备IP地址。",
        "trans_key": "dev_ip"
    },
    "sip": {
        "name": "sip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": ["product_id", "product_type"]
    },
    "sport": {
        "name": "sport",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dip": {
        "name": "dip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dport": {
        "name": "dport",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "src_ip": {
        "name": "src_ip",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是告警事件的源IP地址。",
        "trans_key": "sip"
    },
    "src_port": {
        "name": "src_port",
        "description": "源端口",
        "type": "int",
        "prompt_template": "这是告警事件的源端口。",
        "trans_key": "sport"
    },
    "dst_ip": {
        "name": "dst_ip",
        "description": "目的IP",
        "type": "string",
        "prompt_template": "这是告警事件的目的IP地址。",
        "trans_key": "dip"
    },
    "dst_port": {
        "name": "dst_port",
        "description": "目的端口",
        "type": "int",
        "prompt_template": "这是告警事件的目的端口。",
        "trans_key": "dport"
    },
    "protocol": {
        "name": "protocol",
        "description": "协议",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "6": "HTTP",
            "17": "UDP"
        },
        "prompt_template": "这是告警事件的协议类型。",
        "trans_key": "protocol"
    },
    "direct": {
        "name": "direct",
        "description": "当前会话的方向",
        "value_range": "值：1（内向外）、2（外向内）、3（内对内）、4（其他）",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "内向外",
            "2": "外向内",
            "3": "内对内",
            "4": "其他"
        },
        "prompt_template": "这是告警事件的流量方向。",
        "trans_key": "direction"
    },
    "acted": {
        "name": "acted",
        "description": "实际所完成的动作",
        "type": "int",
        "prompt_template": "这是根据规则设备实际采取的动作。",
        "trans_key": "acted_action"
    },
    "action": {
        "name": "action",
        "description": "根据规则应该采取的动作",
        "type": "string",
        "prompt_template": "这是根据规则设备应该采取的动作。",
        "trans_key": "policy_action"
    },
    "alertlevel": {
        "name": "alertlevel",
        "description": "告警级别",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的告警级别。",
        "trans_key": "alertlevel"
    },
    "timestamp": {
        "name": "timestamp",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "stat_time": {
        "name": "stat_time",
        "description": "时间戳",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "date": {
        "name": "date",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "rawlen": {
        "name": "rawlen",
        "description": "报文长度",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是报文的长度。",
        "trans_key": ["payload_len", "rawlen", "pkt_len"]
    },
    "rawinfo": {
        "name": "rawinfo",
        "description": "原始报文信息",
        "value_range": "base64编码\n包含SDK防病毒日志中的md5信息",
        "type": "string",
        "prompt_template": "这是原始报文信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    "msg": {
        "name": "msg",
        "description": "存放对应该日志的描述",
        "value_range": "将描述中的”,<,>,&分别转换为&quot;,&lt;,&gt;,&amp;",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "ar": {
        "name": "ar",
        "description": "攻击是否成功",
        "value_range": "值为1(成功),2(失败),3(未知)",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "成功",
            "2": "失败",
            "3": "未知"
        },
        "prompt_template": "这是攻击结果，标识攻击是否成功。",
        "trans_key": "ar"
    },
    "q_body": {
        "name": "q_body",
        "description": "http请求消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http请求消息，这是一个Base64编码的字符串，我们可以对其解码，得到q_body_plain字段。",
        "trans_key": "q_body"
    },
    "r_body": {
        "name": "r_body",
        "description": "http回应消息",
        "value_range": "base64编码（4096字节）",
        "type": "string",
        "prompt_template": "这是日志事件的http回应消息，这是一个Base64编码的字符串，我们可以对其解码，得到r_body_plain。",
        "trans_key": "r_body"
    },
    "domain": {
        "name": "domain",
        "description": "域名",
        "type": "string",
        "prompt_template": "这是域名信息。",
        "trans_key": "domain"
    },
    "event_type": {
        "name": "event_type",
        "description": "告警类型",
        "value_range": "",
        "type": "int",
        "prompt_template": "这是日志的告警类型。",
        "trans_key": "event_type"
    },
    "alertinfo": {
        "name": "alertinfo",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志告警事件的消息内容，同时可以由此判断日志对应的事件类型。",
        "trans_key": ["log_message", "log_type"]
    },
    "alert_type": {
        "name": "alert_type",
        "description": "告警信息",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是日志事件的告警类型。",
        "trans_key": "alert_type"
    },
    "protocol_type": {
        "name": "protocol_type",
        "description": "协议类型",
        "value_range": "HTTP/HTTPS",
        "type": "string",
        "prompt_template": "这是协议类型。",
        "trans_key": "protocol_type"
    },
    "uri": {
        "name": "uri",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "url": {
        "name": "url",
        "description": "",
        "value_range": "",
        "type": "string",
        "prompt_template": "这是事件对应的uri。",
        "trans_key": "uri"
    },
    "block": {
        "name": "block",
        "description": "是否禁用ip",
        "value_range": "0：不启用\n1: 启用IP封禁",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "0": "不启用",
            "1": "启用IP封禁"
        },
        "prompt_template": "这是代表是否禁用ip。",
        "trans_key": "block_info"
    },
    "http": {
        "name": "http",
        "description": "HTTP请求或者响应信息",
        "value_range": "base64编码",
        "type": "string",
        "prompt_template": "这是HTTP请求或者响应信息，这是一个Base64编码的字符串，我们可以对其解码，得到payload_plain。",
        "trans_key": "payload"
    },
    # 此处开始为新增

    # "AfertDecodingLen":{
    #     "name":"len_after_decoding",
    #     "description": "编码后长度",
    #     "value_range": "",
    #     "type": "int",
    #     "prompt_template": "这是编码后的长度",
    #     "trans_key": "len_after_decoding"
    # },

}

key_map_4a = {
    "SYSNAME": {
        "name": "SYSNAME",
        "description": "",
        "type": "string",
        "prompt_template": "这是服务名称。",
        "trans_key": "business_name"
    },
    "LOGINTIME": {
        "name": "LOGINTIME",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "SOURCEIP": {
        "name": "SOURCEIP",
        "description": "源IP",
        "type": "string",
        "prompt_template": "这是绕行事件的源IP地址。",
        "trans_key": "sip"
    },
    "RESIP": {
        "name": "RESIP",
        "description": "IP",
        "type": "string",
        "prompt_template": "这是IP地址。",
        "trans_key": "ip"
    },
    "LOGINNAME": {
        "name": "LOGINNAME",
        "description": "用户名",
        "type": "string",
        "prompt_template": "这是绕行事件的用户名。",
        "trans_key": "user_name"
    }

}

key_map_linux = {
    "name": {
        "name": "name",
        "description": "软件名",
        "type": "string",
        "prompt_template": "这是软件名。",
        "trans_key": "software"
    },
    "version": {
        "name": "version",
        "description": "软件版本",
        "type": "string",
        "prompt_template": "这是软件版本。",
        "trans_key": "software_version"
    },
    "action": {
        "name": "action",
        "description": "这是日志的内容",
        "type": "string",
        "prompt_template": "这是日志的内容。",
        "trans_key": "log_message"
    },
    "host_id": {
        "name": "host_id",
        "description": "主机的id",
        "type": "string",
        "prompt_template": "这是主机的id。",
        "trans_key": "host_id"
    }
}

key_map_tac = {
    "file_type": {
        "name": "file_type",
        "description": "文件的类型",
        "type": "string",
        "prompt_template": "这是文件的类型。",
        "trans_key": "sample_file_type"
    },
    "app_desc": {
        "name": "app_desc",
        "description": "app详情",
        "type": "string",
        "prompt_template": "这是app详情。",
        "trans_key": "app_desc"
    },
    "app": {
        "name": "app",
        "description": "应用唯一标志",
        "type": "string",
        "prompt_template": "这是应用的唯一标识。",
        "trans_key": "app"
    },
    "sampleid": {
        "name": "sampleid",
        "description": "样本唯一标识",
        "type": "string",
        "prompt_template": "这是样本的唯一标识。",
        "trans_key": "sample_id"
    },
    "file_name": {
        "name": "file_name",
        "description": "样本文件名称",
        "type": "string",
        "prompt_template": "这是样本文件名称，这是一个Base64编码的字符串，我们可以对其解码，得到具体的样本文件名称。",
        "trans_key": ["file_name", "sample_file_name"]
    },
    "file_hash": {
        "name": "file_hash",
        "description": "样本文件hash",
        "type": "string",
        "prompt_template": "这是样本文件hash。",
        "trans_key": ["file_hash", "sample_file_hash"]
    },
    "detail": {
        "name": "detail",
        "description": "扩展字段",
        "type": "string",
        "prompt_template": "这是告警日志的详情，其中包括一些扩展字段。",
        "trans_key": "info4"
    },
    "app_desc_uri": {
        "name": "app_desc_uri",
        "description": "网络定位信息",
        "type": "string",
        "prompt_template": "这是网络定位信息，这是一个Base64编码的字符串，我们可以对其解码，得到具体的uri。",
        "trans_key": "app_desc_uri"
    },
    "type": {
        "name": "type",
        "description": "威胁类型",
        "value_range": ''' "1": "Trojan",
            "2": "SuspiciousFile",
            "3": "AV" ''',
        "type": "string",
        "is_enum": True,
        "enum_map": {
            "1": "Trojan",
            "2": "SuspiciousFile",
            "3": "AV"
        },
        "prompt_template": "这是日志事件的威胁类型。",
        "trans_key": "alert_type"
    },
    "file_md5": {
        "name": "file_md5",
        "description": "文件MD5",
        "type": "string",
        "prompt_template": "这是样本文件MD5。",
        "trans_key": ["file_md5", "sample_file_md5h"]
    },
    "app_desc_orign": {
        "name": "app_desc_orign",
        "description": "app来源",
        "type": "string",
        "prompt_template": "这是app来源信息，这是一个Base64编码的字符串，我们可以对其解码，得到具体的uri。",
        "trans_key": "app_desc_orign"
    },
    "name": {
        "name": "name",
        "description": "告警的名称",
        "type": "string",
        "prompt_template": "这是告警的名称。",
        "trans_key": "alert_name"
    },
    "alert_time": {
        "name": "alert_time",
        "description": "事件发生的时间",
        "value_range": "1345440491\nUTC时间格式（相对于1970-1-1 00:00:00的秒数）",
        "type": "int",
        "prompt_template": "这是日志事件发生的时间戳。",
        "trans_key": "timestamp"
    },
    "threat": {
        "name": "threat",
        "description": "威胁等级",
        "value_range": "1.高\n2.中\n3.低",
        "type": "int",
        "is_enum": True,
        "enum_map": {
            "1": "高",
            "2": "中",
            "3": "低"
        },
        "prompt_template": "这是日志事件的威胁等级。",
        "trans_key": "threat"
    }
}


def clean_info(raw_info, format_info):
    new_raw_info = {}
    new_format_info = {}
    if isinstance(raw_info, dict):
        raw_info = [raw_info]
    for point in raw_info:
        for key, value in point.items():
            if key not in key_map or len(str(value).strip()) == 0:
                continue
            format_key_list = key_map[key]["trans_key"]
            if isinstance(format_key_list, str):
                format_key_list = [format_key_list]
            for format_key in format_key_list:
                if format_key in format_info and len(str(format_info[format_key]).strip()) > 0:
                    if isinstance(value, str) and len(value) > 150:
                        value = value[:150]
                    format_value = format_info[format_key]
                    if isinstance(format_value, str) and len(format_value) > 150:
                        format_value = format_value[:150]
                    new_raw_info[key] = value
                    new_format_info[format_key] = format_value
    return  new_raw_info, new_format_info


data_list1 = os.listdir(data_dir1)

data_list2 = os.listdir(data_dir2)

all_data = []

for service in service_list:
    for data_name in data_list1:
        if re.search(service, data_name):
            raw_info = None
            with open(data_dir1 + '\\' + data_name, 'r', encoding='utf-8') as f:
                for line in f:
                    line = json.loads(line.strip())
                    if raw_info is None:
                        raw_info = line
                    else:
                        raw_info, format_info = clean_info(raw_info, line)
                        all_data.append({"raw_info": raw_info, "format_info": format_info})
                        raw_info = None

for service in service_list:
    for data_name in data_list2:
        try:
            if re.search(service, data_name):
                raw_info = None
                with open(data_dir2 + '\\' + data_name, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = json.loads(line.strip())
                        if raw_info is None:
                            raw_info = line
                        else:
                            raw_info, format_info = clean_info(raw_info, line)
                            all_data.append({"raw_info": raw_info, "format_info": format_info})
                            raw_info = None
        except:
            print("未成功处理{}".format(data_name))


