name: 💥 Proposal
description: Propose a non-trivial change to Colossal-AI
title: "[PROPOSAL]: "
labels: [enhancement]

body:
- type: markdown
  attributes:
    value: |
      Common reasons for proposals include:

      - Altering the infrastructure;
      - Bumping a critical dependency's major version;
      - A significant improvement in user-friendliness;
      - Significant refactor;
      - Optional: Affiliation/email information helps better analyze and evaluate users to improve the project. Welcome to establish in-depth cooperation.
      - ...

      Please note this is not for feature request or bug template; such action could make us identify the issue wrongly and close it without doing anything.

      We give you maximum freedom to write an elaborated proposal illustrating why you think the change is beneficial for us, and what steps we should take to turn this into reality.


- type: textarea
  attributes:
    label: Proposal
    description: A clear and concise description of what the proposal is.
  validations:
    required: true

- type: checkboxes
  attributes:
    label: Self-service
    description: |
      If you feel like you could contribute to this issue, please check the box below. This would tell us and other people looking for contributions that someone's working on it.
      If you do check this box, please send a pull request within 7 days after a maintainer's approval so we can still delegate this to someone else.

      Proposals usually involve significant code changes, so please reach consensus with the maintainers before rushing to implement it, and make sure you follow the [Contributing Guidelines](https://github.com/hpcaitech/ColossalAI/blob/main/CONTRIBUTING.md).
      This ensures that you don't waste your time and we don't waste ours reading the large diffs.
    options:
      - label: I'd be willing to do some initial work on this proposal myself.


- type: markdown
  attributes:
    value: >
      Thanks for contributing 🎉!
