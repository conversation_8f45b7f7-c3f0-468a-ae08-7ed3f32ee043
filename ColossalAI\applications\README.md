# Applications

This directory contains the applications that are powered by Colossal-AI.

The list of applications include:

- [X] [Colossal-LLaMA-2](./Colossal-LLaMA-2/): Continual Pre-training of LLaMA-2.
- [X] [ColossalEval](./ColossalEval): Evaluation Pipeline for LLMs.
- [X] [Chatbot](./Chat/README.md): Replication of ChatGPT with RLHF.
- [X] [FastFold](https://github.com/hpcaitech/FastFold): Optimizing AlphaFold (Biomedicine) Training and Inference on GPU Clusters.

> Please note that the `Chatbot` application is migrated from the original `ChatGPT` folder.

You can find more example code for base models and functions in the [Examples](https://github.com/hpcaitech/ColossalAI/tree/main/examples) directory.
