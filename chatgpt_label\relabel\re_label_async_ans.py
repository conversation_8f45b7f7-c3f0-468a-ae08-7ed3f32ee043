# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import uuid
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="answer"):
    async with semaphore:
        url = "http://10.37.4.4:8080/v1/chat/completions"


        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            # "model": "Mistral-Large-Instruct-2407",
            # "model": "secllm-v2",
            "model": "Qwen2-72B-Instruct",
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.7,
            "repetition_penalty": 1.05,
            "max_tokens": 3000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 250  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


data1 = pd.read_csv("/home/<USER>/llm_dataset/re_label/train.csv")

data = []

for i in range(len(data1)):
    point = {}
    point["question"] = json.loads(data1.iloc[i]["prompt"])[0]
    point["prompt"] = point["question"]
    point["data_source"] = "lmsys-arena-human-preference-55k"
    point["data_index"] = i
    data.append(point)

data2 = load_dataset("parquet", data_files="/home/<USER>/llm_dataset/re_label/train-00000-of-00001.parquet")
data2 = data2["train"]
for i, raw in enumerate(data2):
    assert raw["conversations"][0]["from"] == "human"
    assert len(raw["conversations"]) == 2

    point = {}
    point["question"] = raw["conversations"][0]["value"]
    point["prompt"] = point["question"]
    point["data_source"] = "tagengo-gpt4"
    point["data_index"] = i
    data.append(point)

data = data[:30000]

print("data size: %s" %  len(data))
print("data example: ", data[0])

loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/re_label/re_lable_answer_qwen_raw.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
