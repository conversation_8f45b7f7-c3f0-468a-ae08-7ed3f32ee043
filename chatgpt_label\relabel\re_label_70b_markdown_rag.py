# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset
import uuid




async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="prompt", answer_name="markdown_answer"):
    async with semaphore:
        url = "http://10.37.4.2:50002/v1/chat/completions"

        # messages = [
        #     {"role": "user",
        #      "content": point[question_name]}
        # ]

        body = {
            "model": "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.1,
            "temperature": 0.1,
            "repetition_penalty": 1.05,
            "max_tokens": 2048,
            "messages": point[question_name],
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 150  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results


system_prompt_webglm = """
你需要基于参考文献回答问题。
回答要求：
- 请使用简洁且专业的语言来回答用户的问题。
- 如果你不知道答案，请回答“没有在知识库中查找到相关信息”。
- 避免提及你是从参考文献中获得的知识。
- 不允许编造，请保证答案与参考文献中描述的一致。
- 请使用 Markdown 语法优化答案的格式。
- 参考文献中的图片链接、网络链接地址和脚本语言不允许省略，请按markdown格式直接返回。
- 请使用与问题相同的语言来回答。
- 在每段答案的末尾使用[x]标识答案在参考文献中的来源。

### 例如：
- 回答：xxx(这是答案段1)xxx[1].xxx(这是答案段2)xxx[3][5].
- 这表示这个答案由两个答案段组成，答案段1来自第1条参考文献，答案段2来自第3条和第5条参考文献。

----

参考文献如下：
{context}


请根据参考文献回答以下问题：
{question}
"""


data_webglm = []
with open("/home/<USER>/llm_dataset/webglm-qa/train.jsonl", "r", encoding="utf-8") as f:
    for line in f:
        point = json.loads(line.strip())

        ref_map = {}
        references = ""
        for i, text in enumerate(point["references"]):
            ref_id = str(uuid.uuid4())
            if random.choice([True, False]):
                ref_api = "<p><img src=\"https://user.nsfocus.com/oss/objs/%s\"  alt=\"\"  width=\"1024\"  height=\"509\"  /></p>" % ref_id
            else:
                ref_api = "![](/api/image/%s)" % ref_id
            references = references + f"参考文献[{i + 1}]: {text}\n图片示例[{i + 1}]：\n" + ref_api + "\n\n"
            ref_map["[%s]" % (i+1)] = "[%s]" % (i+1) + "\n" + ref_api + "\n"
        question = system_prompt_webglm.replace("{context}", references).replace("{question}", point["question"])
        answer = point["answer"]
        for k, v in ref_map.items():
            answer = answer.replace(k, v)
        point["prompt"] = [{"role": "user", "content": question},
                           {"role": "assistant", "content": answer},
                           {"role": "user", "content": "将答案格式修改为markdown格式。注意！不允许删除参考图片的链接！并在文字答案和参考图片之间添加衔接语。"}]

        data_webglm.append(point)


print("\n\ndata size: %s" %  len(data_webglm))
print("\ndata example: \n\n%s" % data_webglm[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data_webglm))

print("Done")


with open("/home/<USER>/llm_dataset/RAG_markdown_relabel.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
