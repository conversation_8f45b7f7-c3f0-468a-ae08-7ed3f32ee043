# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random
import os
import requests
from pprint import pprint
from elasticsearch import Elasticsearch
from elasticsearch import helpers
import json
import uuid
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm

from typing import Type
import base64
import binascii
import urllib.parse
import re


def decode_base64(text):
    pattern = re.compile("[^A-Za-z0-9\+\/=]")
    if re.search(pattern, text):
        text = urllib.parse.unquote(text)
    pad_len = 4 - (len(text) % 4)
    return base64.b64decode(text + "=" * pad_len).decode(errors="ignore")


def encode_base64(string_encode):
    if isinstance(string_encode, dict):
        string_encode = json.dumps(string_encode, ensure_ascii=False, indent=4)
    if not isinstance(string_encode, str):
        string_encode = str(string_encode)
    string_bytes = string_encode.encode('utf-8')
    base64_bytes = base64.b64encode(string_bytes)
    base64_string = base64_bytes.decode('utf-8')
    return base64_string


# 通过edb、日志中的qbody、rbody、知识库、agent的参数   生成


data_list = []

data_dir1 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA"
data_list1 = os.listdir(data_dir1)
data_list += [(data_dir1, x) for x in data_list1]


data_dir2 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-07\DATA2\DATA"
data_list2 = os.listdir(data_dir2)
data_list += [(data_dir2, x) for x in data_list2]


data_dir3 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\广西电信_ALL_DATA\DATA"
data_list3 = os.listdir(data_dir3)
data_list += [(data_dir3, x) for x in data_list3]

data_dir4 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\重庆移动_ALL_DATA\DATA"
data_list4 = os.listdir(data_dir4)
data_list += [(data_dir4, x) for x in data_list4]


data_dir5 = r"D:\cache\WXWorkCache\WXWork\1688850547071684\Cache\File\2023-08\DATA-0810\DATA"
data_list5 = os.listdir(data_dir5)
data_list += [(data_dir5, x) for x in data_list5]



all_data = []

for data_dir, data_name in data_list:
    raw_info = None
    with open(data_dir + '/' + data_name, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line == "":
                continue
            if raw_info is None:
                raw_info = line
            elif line[0] != "{":
                raw_info = line
            else:
                format_data = json.loads(line.strip())
                for key in ["q_body", "r_body"]:
                    if len(format_data.get(key, "").strip()) > 10:
                        text =  format_data.get(key, "")
                        try:
                            decode_text = decode_base64(text)
                            encode_text = encode_base64(decode_text)
                        except:
                            continue
                        all_data.append({"decode_text": decode_text, "encode_text": encode_text})
                raw_info = None
                line = None

all_data = [x for x in all_data if len(x["decode_text"])> 50 and len(x["decode_text"])< 500 and len(x["encode_text"])< 500]
log_data = random.sample(all_data, 300)

print("log data size: %s" % len(log_data))



# edb
all_data = []
with open(r"D:\work\bert_pretrain_model\ChatGPT_dataset\知识库数据\edb/edb-node-20230428.0.json", 'r', encoding='utf-8') as f:
    for line in f:
        line = json.loads(line)
        text = line.get("code", "")
        if len(text) > 10:
            try:
                decode_text = decode_base64(text)
                encode_text = encode_base64(decode_text)
            except:
                continue
            all_data.append({"decode_text": decode_text, "encode_text": encode_text})

all_data = [x for x in all_data if len(x["decode_text"])> 50 and len(x["decode_text"])< 500 and len(x["encode_text"])< 500]
edb_data = random.sample(all_data, 300)

print("edb data size: %s" % len(edb_data))



# table data
table_data = []
with open(r"C:\Users\<USER>\Desktop\train.json", 'r', encoding='utf-8') as f:
    all_data = json.load(f)
    for point in all_data:
        table = point.get("table", [])
        if len(table) < 2:
            continue

        table_data.append(table)

        header = [x.strip() for x in table[0]]

        try:
            list_out = {}
            for i, key in enumerate(header):
                if len(key.strip()) > 0:
                    list_out[key] = [x[i] for x in table[1:]]
            table_data.append(list_out)
        except:
            pass

        try:
            list_out = []
            for i in table[1:]:
                list_out.append({
                    key: value
                    for key, value in zip(table[0], i)
                    if len(key.strip()) > 0
                })
            table_data.append(list_out)
        except:
            pass

all_data = []
for point in table_data:
    encode_text = encode_base64(point)
    decode_text = decode_base64(encode_text)
    all_data.append({"decode_text": decode_text, "encode_text": encode_text})

all_data = [x for x in all_data if len(x["decode_text"])> 50 and len(x["decode_text"])< 500 and len(x["encode_text"])< 500]
table_data = random.sample(all_data, 900)

print("table data size: %s" % len(table_data))

all_data = log_data + edb_data + table_data

print("all data size: %s" % len(all_data))





async def post_request(session, point, semaphore, progress_bar):
    async with semaphore:
        url = "http://************:8080/v1/chat/completions"

        body = {
            "model":point["model"],
            "stream": False,
            "top_p": 0.7,
            "temperature": 0.8,
            "frequency_penalty": 0.2,
            "max_tokens": 800,
            "messages": [{"role": "user", "content": point["input"]}],
            # "repetition_penalty": 1.1,
        }

        try:
            async with session.post(url, json=body) as response:
                res = await response.json()
        except Exception as ex:
            print("openai chatgpt request -- Error:, ex: %s" % ex)
            res = {}

        if res:
            try:
                answer = res["choices"][0]["message"]['content']
            except:
                answer = ""
        else:
            answer = ""

        point['secllm_answer'] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 30  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results



input_prompt = ["base64解码：\n{decode_body}",
                "base64解码这个字符串：\n{decode_body}",
                "解码这个字符串：\n{decode_body}",
                "解码：\n{decode_body}"]

out_prompt = ["这个字符串是Base64编码的，解码后的原文是：\n\n```\n{decode_result}\n```\n",
              "以下是经过Base64解码后的文本内容：\n\n```\n{decode_result}\n```\n"]


use_data = []
for point in all_data:
    point['input'] = random.choice(input_prompt).format(decode_body=point['encode_text'])
    point['output'] = random.choice(out_prompt).format(decode_result=point['decode_text'])
    point_copy = copy.deepcopy(point)
    point_copy['model'] = "secllm-v2-format"
    use_data.append(point_copy)

    point_copy =  copy.deepcopy(point)
    point_copy['model'] = "secllm-v2-dpo"
    use_data.append(point_copy)

print("all data size: %s" % len(use_data))


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(use_data))

with open(r"D:\work\GPT\chatgpt_label/base64_dpo_label_1-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)

with open(r"D:\work\GPT\chatgpt_label/base64_dpo_label_1-o-20240122.json", 'w', encoding='utf-8') as fp:
    json.dump(use_data, fp, indent=4, ensure_ascii=False)

print("Done")

