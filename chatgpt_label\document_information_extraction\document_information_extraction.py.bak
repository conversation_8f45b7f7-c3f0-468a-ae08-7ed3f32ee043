"""输入：上传一事一分析报告，自动提取相关关键信息。
输出：自动提炼相关重要信息，以表格形式输出，包括以下内容：
1、事件:天气、停电时间、复电时间、停电时长、线路名称、影响时户数、故障性质（故障跳闸/主动停运/上级电源点故障）。
2、故障概述：一句话概述故障原因（时间、供电所、变电站、线路名称、开关名称、保护动作跳闸名称/接地选停开关名称（含设备动作情况））
3、设备故障原因及相关情况
4、网架、设备及运行管理情况：网架结构情况、设备故障原因及相关情况、运行方式安排及故障处置情况、历史跳闸情况及原因、线路巡视记录。
5、故障线路有/无故障应急处置预案、故障处置流程按照/未按照先复电后抢修进行、运用/未运用配电自动化系统开展故障处置、故障处置的方式安排、转供方案合理/不合理。
6、暴露问题。
7、下一步措施
最终表格字段包括：文档名称、停电事件名称、所属地市、天气、停电时间、复电时间、停电时长、线路名称、影响时户数、
故障性质（故障跳闸/主动停运/上级电源点故障）、故障概述、网架结构薄弱点、设备故障原因及相关情况、故障线路有/无故障应急处置预案、故障处置流程按照/未按照先复电后抢修进行、运用/未运用配电自动化系统开展故障处置、故障处置的方式安排、转供方案合理/不合理、暴露问题、下一步措施。
"""

import os
import json
import pandas as pd
from docx import Document
import requests
import time

fields = {
    "停电事件名称": {"description": "停电事件名称", "default": ""},
    "所属地市": {"description": "所属地市", "default": ""},
    "天气": {"description": "天气", "default": ""},
    "停电时间": {"description": "停电时间", "default": ""},
    "复电时间": {"description": "复电时间", "default": ""},
    "停电时长": {"description": "停电时长", "default": ""},
    "线路名称": {"description": "线路名称", "default": ""},
    "影响时户数": {"description": "影响时户数", "default": ""},
    "故障性质": {"description": "故障性质，包括：故障跳闸/主动停运/上级电源点故障", "default": ""}  ,
    "故障概述": {"description": "一句话概述故障原因（包含时间、供电所、变电站、线路名称、开关名称、保护动作跳闸名称/接地选停开关名称及设备动作情况）", "default": ""},
    "网架结构情况": {"description": "网架结构情况", "default": ""},
    "网架结构薄弱点": {"description": "网架结构薄弱点", "default": ""},
    "设备故障原因及相关情况": {"description": "设备故障原因及相关情况", "default": ""},
    "运行方式安排": {"description": "运行方式安排", "default": ""},
    "故障处置情况": {"description": "故障处置情况", "default": ""},
    "历史跳闸情况及原因": {"description": "历史跳闸情况及原因", "default": ""},
    "线路巡视记录": {"description": "线路巡视记录", "default": ""},
    "故障线路有/无故障应急处置预案": {"description": "故障线路有/无故障应急处置预案，包括：有/无", "default": ""},
    "故障处置流程按照/未按照先复电后抢修进行": {"description": "故障处置流程按照/未按照先复电后抢修进行，包括：按照/未按照", "default": ""},
    "运用/未运用配电自动化系统开展故障处置": {"description": "运用/未运用配电自动化系统开展故障处置，包括：运用/未运用", "default": ""},
    "故障处置的方式安排": {"description": "故障处置的方式安排", "default": ""},
    "转供方案合理/不合理": {"description": "转供方案合理/不合理，包括：合理/不合理", "default": ""},
    "暴露问题": {"description": "暴露问题", "default": ""},
    "下一步措施": {"description": "下一步措施", "default": ""},
}
# 读取docx文件内容
def read_docx(file_path):
    try:
        doc = Document(file_path)
        full_text = [para.text for para in doc.paragraphs if para.text.strip()]
        return '\n'.join(full_text)
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

# 使用LLM提取信息
def extract_info_with_llm(text, model, api_base, api_key):
    # 定义提取字段
    fields_str = ""
    for field, value in fields.items():
        fields_str += f"- {field}：{value['description']}， 默认值：{value['default']}\n"

    # 构造提示
    prompt = f"""
    从以下文档中提取停电事件的信息。每个事件应包含以下字段：
    {fields}

    文档中可能包含多个事件，请为每个事件分别提取上述字段。如果某个字段无法提取，则返回空字符串。
    返回结果为JSON格式，包含一个事件列表。

    文档内容：
    {text}
    
    注意！！！
    1、返回结果为JSON格式，包含一个事件列表。
    2、如果某个字段无法提取，则返回空字符串。
    3、如果文档中包含多个事件，请为每个事件分别提取上述字段。
    4、如果文档中没有相关信息，请返回空列表。
    5、每条JSON对象中，字段名称必须与上述字段名称一致，不要使用其他字段名称。
    6、你只需要返回JSON格式的结果，不要返回其他任何的说明或解释。
    7、你返回的结果需要能直接使用json.loads()函数转换为列表。
    """

    max_retries = 5  # 最大重试次数
    retry_delay = 1  # 重试间隔，单位秒

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_base,  # 替换为实际API endpoint
                headers={'Authorization': f'Bearer {api_key}', 'Content-Type': 'application/json'},
                json={
                    'model': model,  # 替换为实际模型
                    'messages': [
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 4000,
                    'temperature': 0.1,
                    'top_p': 0.1,
                    'stream': False
                }
            )
            response.raise_for_status()  # 检查HTTP错误
            result = response.json()['choices'][0]['message']['content']
            # 假设LLM返回的是JSON字符串
            extracted_data = json.loads(result)
            return extracted_data
        except requests.exceptions.RequestException as e:
            print(f"LLM API 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)  # 等待后重试
            else:
                print("达到最大重试次数，放弃重试。")
                return []
        except json.JSONDecodeError as e:
            print(f"JSON 解析错误 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)  # 等待后重试
            else:
                print("达到最大重试次数，放弃重试。")
                return []

# 处理单个文档
def process_document(file_path, model, api_base, api_key):
    text = read_docx(file_path)
    if not text:
        return []

    extracted_data = extract_info_with_llm(text, model, api_base, api_key)
    if not extracted_data: # 检查是否成功提取数据
        print(f"  ⚠️  处理 {file_path} 失败，LLM未能提取到有效信息。")
        return []

    output = [] # 修改为列表，以支持多个事件
    # 为每个事件添加文档名称
    file_name = os.path.basename(file_path)
    for event in extracted_data:
        point = {}
        point['文档名称'] = file_name # 直接修改 event 字典
        # 添加默认字段（如果LLM未提供）
        for field, value in fields.items():
            point[field] = event.get(field, value['default'])   
        output.append(point) # 将处理后的事件添加到 output 列表中
    return output

# 保存为JSON
def save_to_json(data, output_path):
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"已保存JSON到: {output_path}")
    except Exception as e:
        print(f"保存JSON失败: {e}")

# 保存为CSV
def save_to_csv(data, output_path):
    # 定义最终表格字段
    csv_columns = ['文档名称'] + list(fields.keys())
    try:
        df = pd.DataFrame(data, columns=csv_columns)
        df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"已保存CSV到: {output_path}")
    except Exception as e:
        print(f"保存CSV失败: {e}")

# 主函数
def main(directory_path, model, api_base, api_key, output_path):
    all_output = []
    all_output_path = os.path.join(output_path, 'all_output.json')
    all_output_csv_path = os.path.join(output_path, 'all_output.csv')
    non_docx_files = []
    failed_files = []

    # 尝试加载已有的 all_output
    if os.path.exists(all_output_path):
        try:
            with open(all_output_path, 'r', encoding='utf-8') as f:
                all_output = json.load(f)
            print(f"  ✅  加载已存在的 all_output 数据，将进行续写。")
        except json.JSONDecodeError:
            print(f"  ⚠️  all_output.json 文件损坏，将重新创建。")
            all_output = []

    for root, _, files in os.walk(directory_path):
        for file in files:
            if not file.endswith('.docx'):
                continue

            file_path = os.path.join(root, file)
            json_path = os.path.splitext(file_path)[0] + '_extracted.json'
            csv_path = os.path.splitext(file_path)[0] + '_extracted.csv'

            # 检查是否已存在输出文件
            if os.path.exists(json_path) and os.path.exists(csv_path):
                print(f"  ⏭️  跳过已处理文件: {file} (JSON/CSV已存在)")
                continue

            print(f"正在处理: {file_path}")
            extracted_data_list = process_document(file_path, model, api_base, api_key) # process_document 返回列表
            if extracted_data_list:
                # 保存每个文档的 JSON 和 CSV
                save_to_json(extracted_data_list, json_path)
                save_to_csv(extracted_data_list, csv_path)
                all_output.extend(extracted_data_list) # 将列表合并到 all_output
            else:
                failed_files.append(file)


    # 保存所有输出
    if all_output: # 只有当 all_output 不为空时才保存
        save_to_json(all_output, all_output_path)
        save_to_csv(all_output, all_output_csv_path)
        print(f"  ✅  已更新 all_output 数据。")
    else:
        print(f"  ⚠️  没有新的数据添加到 all_output。")

    # 打印非 docx 文件列表
    if non_docx_files:
        print("\n  ⚠️  以下文件不是 .docx 格式，已被跳过:")
        for file in non_docx_files:
            print(f"    - {file}")
        print("    请手动将这些文件转换为 .docx 格式后重新运行程序。")

    # 打印处理失败的文件列表
    if failed_files:
        print("\n  ⚠️  以下文件处理失败，LLM未能提取到有效信息:")
        for file in failed_files:
            print(f"    - {file}")
        print("    请检查这些文件内容或稍后重新运行程序。")

    print("\n处理完成")

if __name__ == '__main__':
    directory_path = "./docx/"
    model = "secllm-v3"
    api_base = "http://10.24.45.213:8080/v1/chat/completions"
    api_key = "sk-owl-secllm"
    output_path = "./output"
    main(directory_path, model, api_base, api_key, output_path)
    