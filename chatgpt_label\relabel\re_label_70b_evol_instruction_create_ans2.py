# -*- encoding: utf-8 -*-
# @ModuleName: simple_cluster.py
# @Author: zhanglong
# @Time: 2021/11/22 16:07


from __future__ import print_function

import random

import requests
from pprint import pprint
import json
import re
from tqdm import tqdm
from datetime import datetime
import copy
import pandas as pd
import asyncio
import aiohttp
from tqdm import tqdm
from datasets import load_from_disk, load_dataset , Dataset

import time



prompt = """逐步解决给定的任务。

你可以参考问答示例来帮助你解决问题。

- 请不要在您的回复中提及任何问答示例的存在。
- 请尽以markdown格式提供您的答案！
- 请使用中文回答！
- 回答请尽量详细且专业！


============================
这是一个问答示例：

问题: 
{Task_ref}

答案: 
{Answer_ref}

============================
参考问答示例，逐步解决问题：

问题：
{Task}

答案: 
"""


async def post_request_single(session, url, data):
    try:
        async with session.post(url, json=data) as response:
            res = await response.json()
            answer = res["choices"][0]["message"]['content']
    except Exception as ex:
        # print("openai chatgpt request -- Error:, ex: %s" % ex)
        answer = ""
    return answer


async def post_request(session, point, semaphore, progress_bar, question_name="create_answer_prompt", answer_name="create_answer_answer"):
    async with semaphore:
        url = "http://10.37.4.3:50003/v1/chat/completions"

        messages = [
            {"role": "user",
             "content": point[question_name]}
        ]

        body = {
            "model": "Qwen2.5-72B-Instruct",  # "Mistral-Large-Instruct-2407",
            "stream": False,
            "top_p": 0.3,
            "temperature": 0.3,
            "repetition_penalty": 1.05,
            "max_tokens": 4000,
            "messages": messages,
            # "repetition_penalty": 1.1,
        }

        max_retries = 5
        for attempt in range(max_retries):
            answer = await post_request_single(session, url, body)
            if answer:
                break
            await asyncio.sleep(1)

        if not answer:
            print("openai chatgpt request error")

        point[answer_name] = answer
        progress_bar.update(1)  # 更新进度条
        return point


async def main(data_list):
    concurrency_limit = 100  # 并发限制

    # 创建tqdm进度条
    progress_bar = tqdm(total=len(data_list))

    async with aiohttp.ClientSession() as session:
        semaphore = asyncio.Semaphore(concurrency_limit)
        tasks = [post_request(session, data, semaphore, progress_bar) for data in data_list]

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

    # 关闭tqdm进度条
    progress_bar.close()

    # for data, result in zip(data_list, results):
    #     if result is not None:
    #         print(f"Request Data: {data}, Response: {result}")
    #     else:
    #         print(f"Request for data {data} failed.")
    return results




# print("read data...")
# raw_data = load_dataset("json", data_files="/home/<USER>/llm_dataset/Evol-instruction-66k/re_label_create_zh.json")
# raw_data = raw_data['train']


# raw_data2 = load_dataset("json", data_files="/home/<USER>/llm_dataset/Evol-instruction-66k/re_label.json")
# raw_data2 = raw_data2['train']


# print("\nraw data size: %s" % len(raw_data))


# data = []
# for point, point2 in tqdm(zip(raw_data, raw_data2)):
#     point["meta_answer"] = point2["answer"]
#     point["create_answer_prompt"] = prompt.replace("{Task_ref}", point["instruction"]).replace("{Task}", point["created_answer"]).replace("{Answer_ref}", point["meta_answer"] )
#     data.append(point)


# print("\n\nprompt over. prompt example:\n%s" % data[0]["create_answer_prompt"])

# print("\n\ndata size: %s" %  len(data))
# print("\ndata example: \n\n%s" % data[0])

# print("\n\nstart label...\n\n")


# loop = asyncio.get_event_loop()
# results = loop.run_until_complete(main(data))

# print("Done")


# with open("/home/<USER>/llm_dataset/Evol-instruction-66k/re_label_create_zh_ans.json", 'w', encoding='utf-8') as fp:
#     json.dump(results, fp, indent=4, ensure_ascii=False)


# time.sleep(120)







# print("read data...")
# raw_data = load_dataset("json", data_files="/home/<USER>/llm_dataset/Magicoder-OSS-Instruct-75K/re_label_create_zh.json")
# raw_data = raw_data['train']

# raw_data2 = load_dataset("json", data_files="/home/<USER>/llm_dataset/Magicoder-OSS-Instruct-75K/re_label.json")
# raw_data2 = raw_data2['train']

# print("\nraw data size: %s" % len(raw_data))

# data = []
# for point, point2 in tqdm(zip(raw_data, raw_data2)):
#     point["meta_answer"] = point2["answer"]
#     point["create_answer_prompt"] = prompt.replace("{Task_ref}", point["problem"]).replace("{Task}", point["created_answer"]).replace("{Answer_ref}", point["meta_answer"] )
#     data.append(point)


# print("\n\nprompt over. prompt example:\n%s" % data[0]["create_answer_prompt"])


# print("\n\ndata size: %s" %  len(data))
# print("\ndata example: \n\n%s" % data[0])

# print("\n\nstart label...\n\n")


# # loop = asyncio.get_event_loop()
# results = loop.run_until_complete(main(data))

# print("Done")


# with open("/home/<USER>/llm_dataset/Magicoder-OSS-Instruct-75K/re_label_create_zh_ans.json", 'w', encoding='utf-8') as fp:
#     json.dump(results, fp, indent=4, ensure_ascii=False)



# time.sleep(120)








print("read data...")
raw_data = load_dataset("json", data_files="/home/<USER>/llm_dataset/Magicoder-Evol-Instruct-110K/re_label_create_zh.json")
raw_data = raw_data['train']

raw_data2 = load_dataset("json", data_files="/home/<USER>/llm_dataset/Magicoder-Evol-Instruct-110K/re_label.json")
raw_data2 = raw_data2['train']

assert len(raw_data) == len(raw_data2)

print("\nraw data size: %s" % len(raw_data))

data = []
for point, point2 in tqdm(zip(raw_data, raw_data2)):
    point["meta_answer"] = point2["answer"]
    point["create_answer_prompt"] = prompt.replace("{Task_ref}", point["instruction"]).replace("{Task}", point["created_answer"]).replace("{Answer_ref}", point["meta_answer"] )
    data.append(point)
    
data = data[70000:]

print("\n\nprompt over. prompt example:\n%s" % data[0]["create_answer_prompt"])


print("\n\ndata size: %s" %  len(data))
print("\ndata example: \n\n%s" % data[0])

print("\n\nstart label...\n\n")


loop = asyncio.get_event_loop()
results = loop.run_until_complete(main(data))

print("Done")


with open("/home/<USER>/llm_dataset/Magicoder-Evol-Instruct-110K/re_label_create_zh_ans_70000_111183.json", 'w', encoding='utf-8') as fp:
    json.dump(results, fp, indent=4, ensure_ascii=False)
